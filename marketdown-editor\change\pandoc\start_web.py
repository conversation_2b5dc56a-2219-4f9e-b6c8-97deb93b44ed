#!/usr/bin/env python3
"""
启动文档转换器Web应用
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入Web应用
from web_app.app import app

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 文档转换器Web应用启动中...")
    print("=" * 60)
    print()
    print("📋 功能特性:")
    print("  • 支持 PDF、Word、Excel、TXT、Markdown 互转")
    print("  • 拖拽上传文件")
    print("  • 实时转换进度显示")
    print("  • 美观的Web界面")
    print("  • 最大支持10GB文件 🚀")
    print()
    print("🌐 访问地址:")
    print("  本地访问: http://localhost:5000")
    print("  局域网访问: http://0.0.0.0:5000")
    print()
    print("💡 使用说明:")
    print("  1. 在浏览器中打开上述地址")
    print("  2. 拖拽或选择要转换的文件")
    print("  3. 选择目标格式")
    print("  4. 点击开始转换")
    print("  5. 等待转换完成后下载文件")
    print()
    print("⚠️  注意事项:")
    print("  • 请确保已安装所有依赖包")
    print("  • 某些转换功能需要pandoc或LibreOffice")
    print("  • 大文件(>1GB)转换需要较长时间和足够内存")
    print("  • 确保磁盘空间充足（建议至少20GB可用空间）")
    print("  • 按 Ctrl+C 停止服务")
    print()
    print("=" * 60)
    print("🎉 应用已启动，请在浏览器中访问!")
    print("=" * 60)
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 应用已停止，感谢使用!")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请检查依赖是否正确安装，或查看错误日志")
