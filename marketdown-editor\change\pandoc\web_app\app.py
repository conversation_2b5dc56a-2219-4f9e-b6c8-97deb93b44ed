"""
文档转换器Web应用
"""

import os
import sys
import uuid
import json
import threading
from datetime import datetime
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from document_converter import DocumentConverter
from document_converter.utils.file_utils import detect_file_format, get_file_info
from document_converter.exceptions import ConversionError, UnsupportedFormatError

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024 * 1024  # 10GB max file size

# 针对大文件的优化配置
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用缓存，适合大文件
app.config['UPLOAD_TIMEOUT'] = 3600  # 上传超时时间：1小时

# 启用CORS
CORS(app)

# 配置上传和下载目录
UPLOAD_FOLDER = Path(__file__).parent / 'uploads'
DOWNLOAD_FOLDER = Path(__file__).parent / 'downloads'
UPLOAD_FOLDER.mkdir(exist_ok=True)
DOWNLOAD_FOLDER.mkdir(exist_ok=True)

app.config['UPLOAD_FOLDER'] = str(UPLOAD_FOLDER)
app.config['DOWNLOAD_FOLDER'] = str(DOWNLOAD_FOLDER)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'xlsx', 'txt', 'md'}

# 全局变量存储转换任务状态
conversion_tasks = {}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/formats')
def get_formats():
    """获取支持的格式"""
    converter = DocumentConverter()
    formats = converter.get_supported_formats()
    
    # 构建转换矩阵
    conversion_matrix = {}
    for source_format in formats:
        conversion_matrix[source_format] = [fmt for fmt in formats if fmt != source_format]
    
    return jsonify({
        'formats': formats,
        'conversion_matrix': conversion_matrix
    })


@app.route('/api/upload', methods=['POST'])
def upload_file():
    """文件上传接口"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': f'不支持的文件格式。支持的格式: {", ".join(ALLOWED_EXTENSIONS)}'}), 400
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[1].lower()
        safe_filename = f"{file_id}.{file_extension}"
        
        # 保存文件
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
        file.save(file_path)
        
        # 获取文件信息
        try:
            file_info = get_file_info(file_path)
            detected_format = file_info['format']
        except Exception as e:
            return jsonify({'error': f'文件格式检测失败: {str(e)}'}), 400
        
        return jsonify({
            'file_id': file_id,
            'filename': filename,
            'format': detected_format,
            'size': file_info['size'],
            'message': '文件上传成功'
        })
        
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500


@app.route('/api/convert', methods=['POST'])
def convert_file():
    """文件转换接口"""
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        target_format = data.get('target_format')
        options = data.get('options', {})
        
        if not file_id or not target_format:
            return jsonify({'error': '缺少必要参数'}), 400
        
        # 查找上传的文件
        uploaded_files = list(Path(app.config['UPLOAD_FOLDER']).glob(f"{file_id}.*"))
        if not uploaded_files:
            return jsonify({'error': '文件不存在'}), 404
        
        input_file = uploaded_files[0]
        
        # 生成输出文件名
        output_filename = f"{file_id}_converted.{target_format}"
        output_file = os.path.join(app.config['DOWNLOAD_FOLDER'], output_filename)
        
        # 创建转换任务
        task_id = str(uuid.uuid4())
        conversion_tasks[task_id] = {
            'status': 'processing',
            'progress': 0,
            'message': '开始转换...',
            'input_file': str(input_file),
            'output_file': output_file,
            'target_format': target_format,
            'created_at': datetime.now().isoformat()
        }
        
        # 在后台线程中执行转换
        def convert_in_background():
            try:
                converter = DocumentConverter()
                
                # 更新状态
                conversion_tasks[task_id]['progress'] = 25
                conversion_tasks[task_id]['message'] = '正在转换文件...'
                
                # 执行转换
                success = converter.convert(str(input_file), output_file, **options)
                
                if success:
                    conversion_tasks[task_id]['status'] = 'completed'
                    conversion_tasks[task_id]['progress'] = 100
                    conversion_tasks[task_id]['message'] = '转换完成'
                    conversion_tasks[task_id]['download_filename'] = output_filename
                else:
                    conversion_tasks[task_id]['status'] = 'failed'
                    conversion_tasks[task_id]['message'] = '转换失败'
                    
            except Exception as e:
                conversion_tasks[task_id]['status'] = 'failed'
                conversion_tasks[task_id]['message'] = f'转换错误: {str(e)}'
        
        # 启动后台转换线程
        thread = threading.Thread(target=convert_in_background)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'message': '转换任务已启动'
        })
        
    except Exception as e:
        return jsonify({'error': f'转换失败: {str(e)}'}), 500


@app.route('/api/status/<task_id>')
def get_conversion_status(task_id):
    """获取转换状态"""
    if task_id not in conversion_tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    task = conversion_tasks[task_id]
    return jsonify(task)


@app.route('/api/download/<task_id>')
def download_file(task_id):
    """下载转换后的文件"""
    if task_id not in conversion_tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    task = conversion_tasks[task_id]
    
    if task['status'] != 'completed':
        return jsonify({'error': '文件尚未转换完成'}), 400
    
    output_file = task['output_file']
    if not os.path.exists(output_file):
        return jsonify({'error': '文件不存在'}), 404
    
    download_filename = task.get('download_filename', 'converted_file')
    
    return send_file(
        output_file,
        as_attachment=True,
        download_name=download_filename
    )


@app.route('/api/cleanup', methods=['POST'])
def cleanup_files():
    """清理临时文件"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if task_id and task_id in conversion_tasks:
            task = conversion_tasks[task_id]
            
            # 删除输入文件
            if os.path.exists(task['input_file']):
                os.remove(task['input_file'])
            
            # 删除输出文件
            if os.path.exists(task['output_file']):
                os.remove(task['output_file'])
            
            # 删除任务记录
            del conversion_tasks[task_id]
        
        return jsonify({'message': '清理完成'})
        
    except Exception as e:
        return jsonify({'error': f'清理失败: {str(e)}'}), 500


@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({'error': '文件过大，最大支持10GB'}), 413


@app.errorhandler(500)
def internal_error(e):
    """服务器内部错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500


if __name__ == '__main__':
    print("启动文档转换器Web应用...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
