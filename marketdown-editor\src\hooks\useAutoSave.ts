'use client';

import { useEffect, useRef, useState } from 'react';
import { debounce } from '@/utils';

interface AutoSaveOptions {
  key: string;
  delay?: number;
  enabled?: boolean;
}

export const useAutoSave = <T>(
  data: T,
  options: AutoSaveOptions
) => {
  const { key, delay = 2000, enabled = true } = options;
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const initialLoadRef = useRef(true);

  // 保存到本地存储
  const saveToStorage = (value: T) => {
    if (!enabled) return;
    
    try {
      setIsSaving(true);
      localStorage.setItem(key, JSON.stringify({
        data: value,
        timestamp: new Date().toISOString(),
      }));
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 从本地存储加载
  const loadFromStorage = (): T | null => {
    if (!enabled) return null;
    
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        const parsed = JSON.parse(stored);
        setLastSaved(new Date(parsed.timestamp));
        return parsed.data;
      }
    } catch (error) {
      console.error('Auto-load failed:', error);
    }
    return null;
  };

  // 防抖保存函数
  const debouncedSave = useRef(
    debounce((value: T) => {
      saveToStorage(value);
    }, delay)
  ).current;

  // 监听数据变化
  useEffect(() => {
    if (initialLoadRef.current) {
      initialLoadRef.current = false;
      return;
    }

    if (enabled) {
      setHasUnsavedChanges(true);
      debouncedSave(data);
    }
  }, [data, enabled, debouncedSave]);

  // 手动保存
  const save = () => {
    saveToStorage(data);
  };

  // 清除保存的数据
  const clear = () => {
    try {
      localStorage.removeItem(key);
      setLastSaved(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Clear auto-save failed:', error);
    }
  };

  return {
    lastSaved,
    isSaving,
    hasUnsavedChanges,
    save,
    clear,
    loadFromStorage,
  };
};
