'use client';

import { useEffect, useCallback } from 'react';

interface ShortcutConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  action: () => void;
  description: string;
}

export const useKeyboardShortcuts = (shortcuts: ShortcutConfig[]) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    for (const shortcut of shortcuts) {
      const keyMatch = event.key.toLowerCase() === shortcut.key.toLowerCase();
      const ctrlMatch = !!shortcut.ctrl === (event.ctrlKey || event.metaKey);
      const altMatch = !!shortcut.alt === event.altKey;
      const shiftMatch = !!shortcut.shift === event.shiftKey;
      const metaMatch = !!shortcut.meta === event.metaKey;

      if (keyMatch && ctrlMatch && altMatch && shiftMatch && metaMatch) {
        event.preventDefault();
        shortcut.action();
        break;
      }
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return shortcuts;
};

// 预定义的快捷键配置
export const createEditorShortcuts = (actions: {
  bold: () => void;
  italic: () => void;
  save: () => void;
  open: () => void;
  newDocument: () => void;
  find: () => void;
  replace: () => void;
  undo: () => void;
  redo: () => void;
  selectAll: () => void;
  copy: () => void;
  paste: () => void;
  cut: () => void;
}): ShortcutConfig[] => [
  {
    key: 'b',
    ctrl: true,
    action: actions.bold,
    description: '粗体',
  },
  {
    key: 'i',
    ctrl: true,
    action: actions.italic,
    description: '斜体',
  },
  {
    key: 's',
    ctrl: true,
    action: actions.save,
    description: '保存',
  },
  {
    key: 'o',
    ctrl: true,
    action: actions.open,
    description: '打开',
  },
  {
    key: 'n',
    ctrl: true,
    action: actions.newDocument,
    description: '新建',
  },
  {
    key: 'f',
    ctrl: true,
    action: actions.find,
    description: '查找',
  },
  {
    key: 'h',
    ctrl: true,
    action: actions.replace,
    description: '替换',
  },
  {
    key: 'z',
    ctrl: true,
    action: actions.undo,
    description: '撤销',
  },
  {
    key: 'y',
    ctrl: true,
    action: actions.redo,
    description: '重做',
  },
  {
    key: 'a',
    ctrl: true,
    action: actions.selectAll,
    description: '全选',
  },
  {
    key: 'c',
    ctrl: true,
    action: actions.copy,
    description: '复制',
  },
  {
    key: 'v',
    ctrl: true,
    action: actions.paste,
    description: '粘贴',
  },
  {
    key: 'x',
    ctrl: true,
    action: actions.cut,
    description: '剪切',
  },
];
