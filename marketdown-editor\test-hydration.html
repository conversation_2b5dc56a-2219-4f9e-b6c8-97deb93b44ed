<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hydration Test</title>
</head>
<body>
    <h1>Hydration 错误修复测试</h1>
    <p>如果您在浏览器控制台中看到 hydration 错误，请检查以下内容：</p>
    
    <ol>
        <li>打开浏览器开发者工具 (F12)</li>
        <li>切换到 Console 标签页</li>
        <li>刷新页面 (F5)</li>
        <li>查看是否还有 "Hydration failed" 错误</li>
    </ol>
    
    <h2>修复内容</h2>
    <ul>
        <li>✅ 修复了 CopyButton 组件的 hydration 问题</li>
        <li>✅ 修复了 QuickCopyButton 组件的 hydration 问题</li>
        <li>✅ 使用 useEffect 确保浏览器 API 只在客户端调用</li>
        <li>✅ 添加了 isMounted 状态来避免服务器端和客户端渲染不匹配</li>
    </ul>
    
    <h2>测试步骤</h2>
    <ol>
        <li>访问 <a href="http://localhost:3001" target="_blank">http://localhost:3001</a></li>
        <li>检查控制台是否有 hydration 错误</li>
        <li>测试复制按钮功能是否正常</li>
    </ol>
    
    <script>
        console.log('测试页面已加载，请检查主应用是否还有 hydration 错误');
    </script>
</body>
</html>
