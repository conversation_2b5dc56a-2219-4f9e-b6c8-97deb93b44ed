'use client';

import React, { useState } from 'react';
import { AdvancedRichTextEditor } from './AdvancedRichTextEditor';

const demoContent = `# 🎨 高级富文本编辑器功能演示

## 一、文本样式控制（基础格式）

### 字体属性演示
这是**微软雅黑字体**的示例文本。
这是*斜体文字*的示例。
这是<u>下划线文字</u>的示例。
这是~~删除线文字~~的示例。

### 上标下标演示
- 数学公式：E = mc²
- 化学公式：H₂O + CO₂
- 脚注引用：这是一个脚注¹

### 文本装饰效果
- **粗体文字**：重要内容强调
- *斜体文字*：引用或强调
- <u>下划线</u>：链接或重点标记
- ~~删除线~~：已删除或修改的内容

## 二、结构排版功能（层级与布局）

### 段落格式演示

#### 左对齐段落
这是左对齐的段落文本，通常用于正文内容的显示。文字从左边开始排列，右边自然换行。

#### 居中对齐段落
这是居中对齐的段落文本，通常用于标题或重要声明的显示。

#### 右对齐段落
这是右对齐的段落文本，通常用于日期、签名等内容的显示。

### 列表与层级结构

#### 无序列表
- 第一级列表项
  - 第二级列表项
    - 第三级列表项
- 另一个第一级列表项

#### 有序列表
1. 第一步：准备工作
2. 第二步：开始编辑
3. 第三步：格式调整
4. 第四步：保存文档

#### 任务列表
- [x] 已完成的任务
- [x] 另一个已完成的任务
- [ ] 未完成的任务
- [ ] 待处理的任务

### 引用与特殊格式

> 这是一个块引用示例。
> 引用通常用于引用他人的话语或重要的声明。
> 可以包含多行内容。

#### 代码块示例
\`\`\`javascript
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to our rich text editor!\`;
}

greetUser("用户");
\`\`\`

#### 行内代码
在文本中可以使用 \`console.log()\` 这样的行内代码。

## 三、媒体与元素嵌入功能

### 图片相关
![示例图片](https://via.placeholder.com/400x200/4F46E5/FFFFFF?text=富文本编辑器示例图片)

### 表格处理

| 功能分类 | 具体功能 | 支持程度 | 说明 |
|----------|----------|----------|------|
| 文本样式 | 字体设置 | ✅ 完全支持 | 支持多种字体选择 |
| 文本样式 | 颜色设置 | ✅ 完全支持 | 支持文字和背景色 |
| 排版功能 | 对齐方式 | ✅ 完全支持 | 左、右、居中、两端 |
| 排版功能 | 列表格式 | ✅ 完全支持 | 有序、无序、任务列表 |
| 媒体元素 | 图片插入 | ✅ 完全支持 | 本地和网络图片 |
| 媒体元素 | 表格编辑 | ✅ 完全支持 | 自定义行列和样式 |

### 链接示例
这里有一个[示例链接](https://example.com)，点击可以跳转到外部网站。

### 特殊符号与字符
- 数学符号：∑ ∫ ∞ ≤ ≥ ≠ ± √
- 希腊字母：α β γ δ ε ζ η θ
- 箭头符号：→ ← ↑ ↓ ↔ ⇒ ⇐
- 其他符号：© ® ™ § ¶ † ‡

---

## 四、交互与辅助功能

### 编辑辅助功能
- **撤销/重做**：支持多级操作回退
- **查找替换**：快速定位和替换文本
- **全选操作**：一键选择所有内容
- **格式刷**：复制和应用格式样式

### 快捷键支持
| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 粗体 | Ctrl+B | 加粗选中文字 |
| 斜体 | Ctrl+I | 倾斜选中文字 |
| 下划线 | Ctrl+U | 添加下划线 |
| 撤销 | Ctrl+Z | 撤销上一步操作 |
| 重做 | Ctrl+Y | 重做被撤销的操作 |
| 全选 | Ctrl+A | 选择所有内容 |

### 格式兼容性
- **导入支持**：Markdown、HTML、纯文本
- **导出支持**：Markdown、HTML、PDF、Word
- **跨平台**：支持Windows、Mac、Linux
- **移动端**：响应式设计，支持触摸操作

---

## 五、高级功能特性

### 协作编辑
- 多人实时编辑
- 修改痕迹显示
- 批注和评论系统
- 版本历史记录

### 自动化功能
- 自动保存
- 智能格式识别
- 拼写检查
- 语法建议

### 扩展功能
- 插件系统
- 自定义主题
- 模板库
- 宏命令

---

*这个演示文档展示了富文本编辑器的主要功能。您可以在编辑器中尝试各种格式化操作，体验完整的富文本编辑功能。*

**提示**：切换到富文本模式可以获得更直观的编辑体验！`;

export const RichTextFeatureDemo: React.FC = () => {
  const [content, setContent] = useState(demoContent);
  const [activeDemo, setActiveDemo] = useState<string>('all');

  const demoSections = [
    { id: 'all', name: '完整演示', icon: '🎨' },
    { id: 'text', name: '文本样式', icon: '✏️' },
    { id: 'layout', name: '排版功能', icon: '📐' },
    { id: 'media', name: '媒体元素', icon: '🖼️' },
    { id: 'interactive', name: '交互功能', icon: '⚡' }
  ];

  const loadDemoContent = (type: string) => {
    switch (type) {
      case 'text':
        setContent(`# 文本样式演示

## 字体效果
**粗体文字** - 用于强调重要内容
*斜体文字* - 用于引用或轻微强调
<u>下划线文字</u> - 用于链接或重点标记
~~删除线文字~~ - 用于标记删除的内容

## 上标下标
- 数学公式：E = mc²
- 化学公式：H₂O
- 脚注：参考文献¹

## 特殊格式
\`行内代码\` 用于标记代码片段

> 这是引用文本
> 用于引用他人观点

---

尝试选择文字并使用工具栏进行格式化！`);
        break;
      case 'layout':
        setContent(`# 排版功能演示

## 对齐方式

### 左对齐（默认）
这是左对齐的文本，从左边开始排列。

### 居中对齐
这段文字应该居中显示。

### 右对齐
这段文字应该右对齐显示。

## 列表结构

### 无序列表
- 项目一
- 项目二
  - 子项目A
  - 子项目B
- 项目三

### 有序列表
1. 第一步
2. 第二步
3. 第三步

## 缩进控制
使用Tab键或工具栏按钮可以控制文本缩进。

---

尝试使用对齐按钮和列表功能！`);
        break;
      case 'media':
        setContent(`# 媒体元素演示

## 图片插入
![示例图片](https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=示例图片)

## 链接创建
这是一个[示例链接](https://example.com)。

## 表格功能

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 代码块
\`\`\`javascript
function hello() {
  console.log("Hello World!");
}
\`\`\`

---

使用工具栏的媒体按钮插入各种元素！`);
        break;
      case 'interactive':
        setContent(`# 交互功能演示

## 编辑操作
- 使用 Ctrl+Z 撤销操作
- 使用 Ctrl+Y 重做操作
- 使用 Ctrl+A 全选内容
- 使用格式清除按钮移除所有格式

## 快捷键测试
选择文字后尝试：
- Ctrl+B：粗体
- Ctrl+I：斜体
- Ctrl+U：下划线

## 查找功能
使用查找按钮搜索文档中的特定文字。

## 格式刷
1. 选择已格式化的文字
2. 点击格式刷按钮
3. 选择要应用格式的文字

---

尝试各种交互功能和快捷键！`);
        break;
      default:
        setContent(demoContent);
    }
    setActiveDemo(type);
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          🎨 富文本编辑器功能演示
        </h1>
        
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">🚀 演示说明</h2>
          <p className="text-blue-700">
            这个演示页面展示了富文本编辑器的完整功能。您可以选择不同的演示内容，
            然后在编辑器中尝试各种格式化操作。切换到富文本模式可以获得最佳的编辑体验！
          </p>
        </div>

        {/* 演示选择器 */}
        <div className="flex flex-wrap gap-2 mb-6">
          {demoSections.map(section => (
            <button
              key={section.id}
              onClick={() => loadDemoContent(section.id)}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                activeDemo === section.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              {section.icon} {section.name}
            </button>
          ))}
        </div>
      </div>

      {/* 编辑器 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div className="p-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            富文本编辑器
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            当前演示：{demoSections.find(s => s.id === activeDemo)?.name}
          </p>
        </div>
        <div className="p-4">
          <AdvancedRichTextEditor
            content={content}
            onChange={setContent}
            className="border border-gray-300 dark:border-gray-600 rounded-md"
          />
        </div>
      </div>

      {/* 功能提示 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-2">💡 使用提示</h3>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• 切换到富文本模式体验可视化编辑</li>
            <li>• 使用工具栏按钮快速格式化</li>
            <li>• 尝试键盘快捷键提高效率</li>
          </ul>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">⌨️ 常用快捷键</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Ctrl+B：粗体</li>
            <li>• Ctrl+I：斜体</li>
            <li>• Ctrl+U：下划线</li>
            <li>• Ctrl+Z：撤销</li>
          </ul>
        </div>
        
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="font-semibold text-purple-800 mb-2">🎯 高级功能</h3>
          <ul className="text-sm text-purple-700 space-y-1">
            <li>• 字体和颜色设置</li>
            <li>• 表格插入和编辑</li>
            <li>• 图片和链接插入</li>
            <li>• 格式清除和查找</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
