'use client';

import { useState, useEffect } from 'react';
import { dbManager, AIModel, initializeDefaultModels } from '@/utils/indexedDB';

export default function TestDB() {
  const [models, setModels] = useState<AIModel[]>([]);
  const [status, setStatus] = useState('');

  const loadModels = async () => {
    try {
      setStatus('正在加载模型...');
      await initializeDefaultModels();
      const allModels = await dbManager.getAllAIModels();
      setModels(allModels);
      setStatus(`成功加载 ${allModels.length} 个模型`);
    } catch (error) {
      setStatus(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const testCreateModel = async () => {
    try {
      setStatus('正在创建测试模型...');
      const id = await dbManager.createAIModel({
        name: '测试模型',
        api_key: 'test-key',
        base_url: 'https://test.com',
        model_name: 'test-model',
        temperature: 0.5,
        max_tokens: 1000,
        description: '这是一个测试模型',
        is_default: false,
      });
      setStatus(`成功创建模型，ID: ${id}`);
      await loadModels();
    } catch (error) {
      setStatus(`创建失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  useEffect(() => {
    loadModels();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
          IndexedDB 测试页面
        </h1>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            操作状态
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{status}</p>
          
          <div className="flex gap-4">
            <button
              onClick={loadModels}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md"
            >
              重新加载模型
            </button>
            <button
              onClick={testCreateModel}
              className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md"
            >
              创建测试模型
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            AI模型列表 ({models.length})
          </h2>
          
          {models.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">暂无模型</p>
          ) : (
            <div className="space-y-4">
              {models.map((model) => (
                <div
                  key={model.id}
                  className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {model.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        模型: {model.model_name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        API密钥: {model.api_key ? '已配置' : '未配置'}
                      </p>
                      {model.description && (
                        <p className="text-sm text-gray-400 mt-1">
                          {model.description}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {model.is_default && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          默认
                        </span>
                      )}
                      <span className="text-xs text-gray-500">
                        ID: {model.id}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
