// 测试修复分析功能
const testContent = `#测试文档

##标题没有空格

这是一个段落，中文English混合没有空格。还有数字123和中文混合。

这里有多个    空格。

这行有尾随空格   

###标题结尾有标点符号。`;

const fixedContent = `# 测试文档

## 标题没有空格

这是一个段落，中文 English 混合没有空格。还有数字 123 和中文混合。

这里有多个 空格。

这行有尾随空格

### 标题结尾有标点符号`;

// 模拟分析函数
function analyzeFixDifferences(originalContent, fixedContent) {
  const originalLines = originalContent.split('\n');
  const fixedLines = fixedContent.split('\n');
  const changes = [];
  
  let changedLines = 0;
  let addedLines = 0;
  let removedLines = 0;
  
  const maxLength = Math.max(originalLines.length, fixedLines.length);
  
  for (let i = 0; i < maxLength; i++) {
    const originalLine = originalLines[i];
    const fixedLine = fixedLines[i];
    
    if (originalLine !== fixedLine) {
      if (originalLine === undefined) {
        // 新增行
        addedLines++;
        changes.push({ line: i + 1, type: 'added', fixed: fixedLine });
      } else if (fixedLine === undefined) {
        // 删除行
        removedLines++;
        changes.push({ line: i + 1, type: 'removed', original: originalLine });
      } else {
        // 修改行
        changedLines++;
        changes.push({ line: i + 1, type: 'modified', original: originalLine, fixed: fixedLine });
      }
    }
  }
  
  return { changedLines, addedLines, removedLines, changes };
}

console.log('=== 修复分析测试 ===');
console.log('\n原始内容:');
console.log(testContent);

console.log('\n修复后内容:');
console.log(fixedContent);

console.log('\n=== 分析结果 ===');
const analysis = analyzeFixDifferences(testContent, fixedContent);
console.log('统计:', {
  修改行数: analysis.changedLines,
  新增行数: analysis.addedLines,
  删除行数: analysis.removedLines,
  总变更: analysis.changedLines + analysis.addedLines + analysis.removedLines
});

console.log('\n详细变更:');
analysis.changes.forEach((change, index) => {
  console.log(`${index + 1}. 第${change.line}行 [${change.type}]:`);
  if (change.original) {
    console.log(`   原文: "${change.original}"`);
  }
  if (change.fixed) {
    console.log(`   修复: "${change.fixed}"`);
  }
  console.log('');
});

// 分析修复类型
console.log('=== 修复类型分析 ===');
const changeTypes = new Set();
analysis.changes.forEach(change => {
  if (change.type === 'modified' && change.original && change.fixed) {
    // 分析修改类型
    if (change.original.match(/^#{1,6}[^\s]/) && change.fixed.match(/^#{1,6}\s/)) {
      changeTypes.add('标题格式');
    }
    if (change.original.match(/[\u4e00-\u9fff][a-zA-Z]|[a-zA-Z][\u4e00-\u9fff]/) && 
        change.fixed.includes(' ')) {
      changeTypes.add('中英文排版');
    }
    if (change.original.match(/\s{2,}/) && !change.fixed.match(/\s{2,}/)) {
      changeTypes.add('空格规范');
    }
    if (change.original.match(/\s+$/) && !change.fixed.match(/\s+$/)) {
      changeTypes.add('行尾清理');
    }
    if (change.original.match(/[。！？：；，]$/) && !change.fixed.match(/[。！？：；，]$/)) {
      changeTypes.add('标点符号');
    }
  }
});

console.log('检测到的修复类型:', Array.from(changeTypes).join('、'));
