'use client';

import React, { useState, useEffect, useRef } from 'react';

interface FindReplaceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  onContentChange: (newContent: string) => void;
  textareaRef: React.RefObject<HTMLTextAreaElement>;
}

export const FindReplaceDialog: React.FC<FindReplaceDialogProps> = ({
  isOpen,
  onClose,
  content,
  onContentChange,
  textareaRef,
}) => {
  const [findText, setFindText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [useRegex, setUseRegex] = useState(false);
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [currentMatch, setCurrentMatch] = useState(0);
  const [totalMatches, setTotalMatches] = useState(0);
  const [matches, setMatches] = useState<Array<{ start: number; end: number }>>([]);
  
  const findInputRef = useRef<HTMLInputElement>(null);

  // 查找匹配项
  const findMatches = (searchText: string) => {
    if (!searchText) {
      setMatches([]);
      setTotalMatches(0);
      setCurrentMatch(0);
      return;
    }

    try {
      let regex: RegExp;
      if (useRegex) {
        const flags = caseSensitive ? 'g' : 'gi';
        regex = new RegExp(searchText, flags);
      } else {
        const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const flags = caseSensitive ? 'g' : 'gi';
        regex = new RegExp(escapedText, flags);
      }

      const foundMatches: Array<{ start: number; end: number }> = [];
      let match;
      while ((match = regex.exec(content)) !== null) {
        foundMatches.push({
          start: match.index,
          end: match.index + match[0].length,
        });
        // 防止无限循环
        if (match[0].length === 0) break;
      }

      setMatches(foundMatches);
      setTotalMatches(foundMatches.length);
      setCurrentMatch(foundMatches.length > 0 ? 1 : 0);
      
      // 高亮第一个匹配项
      if (foundMatches.length > 0) {
        highlightMatch(0);
      }
    } catch (error) {
      // 正则表达式错误
      setMatches([]);
      setTotalMatches(0);
      setCurrentMatch(0);
    }
  };

  // 高亮匹配项
  const highlightMatch = (index: number) => {
    if (matches.length === 0 || !textareaRef.current) return;
    
    const match = matches[index];
    textareaRef.current.focus();
    textareaRef.current.setSelectionRange(match.start, match.end);
    textareaRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };

  // 查找下一个
  const findNext = () => {
    if (matches.length === 0) return;
    const nextIndex = currentMatch >= matches.length ? 0 : currentMatch;
    setCurrentMatch(nextIndex + 1);
    highlightMatch(nextIndex);
  };

  // 查找上一个
  const findPrevious = () => {
    if (matches.length === 0) return;
    const prevIndex = currentMatch <= 1 ? matches.length - 1 : currentMatch - 2;
    setCurrentMatch(prevIndex + 1);
    highlightMatch(prevIndex);
  };

  // 替换当前匹配项
  const replaceCurrent = () => {
    if (matches.length === 0 || currentMatch === 0) return;
    
    const matchIndex = currentMatch - 1;
    const match = matches[matchIndex];
    const newContent = content.substring(0, match.start) + 
                      replaceText + 
                      content.substring(match.end);
    
    onContentChange(newContent);
    
    // 重新查找（因为内容已改变）
    setTimeout(() => {
      findMatches(findText);
    }, 0);
  };

  // 替换全部
  const replaceAll = () => {
    if (!findText) return;
    
    try {
      let regex: RegExp;
      if (useRegex) {
        const flags = caseSensitive ? 'g' : 'gi';
        regex = new RegExp(findText, flags);
      } else {
        const escapedText = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const flags = caseSensitive ? 'g' : 'gi';
        regex = new RegExp(escapedText, flags);
      }

      const newContent = content.replace(regex, replaceText);
      onContentChange(newContent);
      
      // 重新查找
      setTimeout(() => {
        findMatches(findText);
      }, 0);
    } catch (error) {
      // 正则表达式错误
    }
  };

  // 监听查找文本变化
  useEffect(() => {
    findMatches(findText);
  }, [findText, useRegex, caseSensitive, content]);

  // 对话框打开时聚焦输入框
  useEffect(() => {
    if (isOpen && findInputRef.current) {
      findInputRef.current.focus();
    }
  }, [isOpen]);

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter') {
      if (e.shiftKey) {
        findPrevious();
      } else {
        findNext();
      }
    } else if (e.key === 'F3') {
      e.preventDefault();
      if (e.shiftKey) {
        findPrevious();
      } else {
        findNext();
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-96 max-w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            查找和替换
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ×
          </button>
        </div>

        <div className="space-y-4" onKeyDown={handleKeyDown}>
          {/* 查找输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              查找
            </label>
            <div className="flex gap-2">
              <input
                ref={findInputRef}
                type="text"
                value={findText}
                onChange={(e) => setFindText(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="输入要查找的文本..."
              />
              <button
                onClick={findPrevious}
                disabled={matches.length === 0}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md disabled:opacity-50"
                title="上一个 (Shift+Enter)"
              >
                ↑
              </button>
              <button
                onClick={findNext}
                disabled={matches.length === 0}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md disabled:opacity-50"
                title="下一个 (Enter)"
              >
                ↓
              </button>
            </div>
            {findText && (
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {totalMatches > 0 ? `${currentMatch}/${totalMatches} 个匹配项` : '无匹配项'}
              </div>
            )}
          </div>

          {/* 替换输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              替换为
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={replaceText}
                onChange={(e) => setReplaceText(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="输入替换文本..."
              />
              <button
                onClick={replaceCurrent}
                disabled={matches.length === 0 || currentMatch === 0}
                className="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md disabled:opacity-50"
              >
                替换
              </button>
              <button
                onClick={replaceAll}
                disabled={matches.length === 0}
                className="px-3 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md disabled:opacity-50"
              >
                全部
              </button>
            </div>
          </div>

          {/* 选项 */}
          <div className="flex gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={caseSensitive}
                onChange={(e) => setCaseSensitive(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">区分大小写</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={useRegex}
                onChange={(e) => setUseRegex(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">正则表达式</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};
