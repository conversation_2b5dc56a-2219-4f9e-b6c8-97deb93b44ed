# 文档转换器Web界面使用指南

## 🌐 Web界面已成功创建！

您现在可以通过浏览器使用文档转换器了！

### 🚀 启动Web应用

```bash
# 方法1：使用启动脚本
python start_web.py

# 方法2：直接运行Flask应用
python web_app/app.py
```

### 🌍 访问地址

- **本地访问**: http://localhost:5000
- **局域网访问**: http://您的IP地址:5000

### ✨ 功能特性

1. **🎨 美观的用户界面**
   - 基于Bootstrap 5的现代化设计
   - 响应式布局，支持移动设备
   - 直观的操作流程

2. **📁 文件上传**
   - 支持拖拽上传
   - 点击选择文件
   - 自动文件格式检测
   - 文件大小验证（最大10GB）

3. **🔄 格式转换**
   - 支持5种格式互转：PDF、Word、Excel、TXT、Markdown
   - 智能格式选择（根据源文件自动显示可转换格式）
   - 转换选项配置（如生成目录）

4. **📊 实时进度**
   - 转换进度条显示
   - 状态消息更新
   - 转换完成通知

5. **💾 文件下载**
   - 一键下载转换后的文件
   - 自动文件命名
   - 安全的文件传输

### 🎯 使用步骤

1. **打开浏览器**
   - 访问 http://localhost:5000

2. **上传文件**
   - 拖拽文件到上传区域，或
   - 点击"选择文件"按钮

3. **选择目标格式**
   - 从下拉菜单中选择要转换的格式
   - 可选：勾选"生成目录"选项

4. **开始转换**
   - 点击"开始转换"按钮
   - 等待转换完成

5. **下载文件**
   - 转换完成后点击"下载文件"
   - 文件将自动下载到您的下载文件夹

### 📋 支持的文件格式

| 格式 | 扩展名 | 说明 |
|------|--------|------|
| PDF | .pdf | 便携式文档格式 |
| Word | .docx | Microsoft Word文档 |
| Excel | .xlsx | Microsoft Excel电子表格 |
| 文本 | .txt | 纯文本文件 |
| Markdown | .md | 标记语言文档 |

### 🔧 技术架构

**后端**:
- Flask 3.0.0 - Web框架
- Flask-CORS - 跨域支持
- 文档转换器核心库

**前端**:
- HTML5 + CSS3 + JavaScript
- Bootstrap 5 - UI框架
- Bootstrap Icons - 图标库
- AJAX - 异步通信

**API接口**:
- `GET /api/formats` - 获取支持的格式
- `POST /api/upload` - 文件上传
- `POST /api/convert` - 开始转换
- `GET /api/status/<task_id>` - 查询转换状态
- `GET /api/download/<task_id>` - 下载文件
- `POST /api/cleanup` - 清理临时文件

### 🛡️ 安全特性

1. **文件验证**
   - 文件类型检查
   - 文件大小限制
   - 安全文件名处理

2. **临时文件管理**
   - 唯一文件ID生成
   - 自动清理机制
   - 安全的文件存储

3. **错误处理**
   - 友好的错误提示
   - 详细的日志记录
   - 异常情况处理

### 📱 响应式设计

Web界面完全支持移动设备：
- 手机端优化布局
- 触摸友好的操作
- 自适应屏幕尺寸

### 🔍 故障排除

**常见问题**:

1. **无法访问Web界面**
   - 检查Flask应用是否正常启动
   - 确认端口5000未被占用
   - 检查防火墙设置

2. **文件上传失败**
   - 确认文件格式受支持
   - 检查文件大小是否超过10GB
   - 确认网络连接正常

3. **转换失败**
   - 检查文件是否损坏
   - 确认所有依赖库已安装
   - 查看控制台错误信息

4. **下载失败**
   - 等待转换完全完成
   - 检查浏览器下载设置
   - 确认磁盘空间充足

### 🎉 享受使用！

现在您可以通过美观的Web界面轻松转换文档了！

- 🌐 访问地址: http://localhost:5000
- 📚 支持格式: PDF、Word、Excel、TXT、Markdown
- 🚀 快速转换: 拖拽上传，一键转换
- 📱 移动友好: 支持手机和平板设备

如有任何问题，请查看控制台日志或联系技术支持。
