{"version": 3, "sources": [], "sections": [{"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/hooks/useKeyboardShortcuts.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useCallback } from 'react';\r\n\r\ninterface ShortcutConfig {\r\n  key: string;\r\n  ctrl?: boolean;\r\n  alt?: boolean;\r\n  shift?: boolean;\r\n  meta?: boolean;\r\n  action: () => void;\r\n  description: string;\r\n}\r\n\r\nexport const useKeyboardShortcuts = (shortcuts: ShortcutConfig[]) => {\r\n  const handleKeyDown = useCallback((event: KeyboardEvent) => {\r\n    for (const shortcut of shortcuts) {\r\n      const keyMatch = event.key.toLowerCase() === shortcut.key.toLowerCase();\r\n      const ctrlMatch = !!shortcut.ctrl === (event.ctrlKey || event.metaKey);\r\n      const altMatch = !!shortcut.alt === event.altKey;\r\n      const shiftMatch = !!shortcut.shift === event.shiftKey;\r\n      const metaMatch = !!shortcut.meta === event.metaKey;\r\n\r\n      if (keyMatch && ctrlMatch && altMatch && shiftMatch && metaMatch) {\r\n        event.preventDefault();\r\n        shortcut.action();\r\n        break;\r\n      }\r\n    }\r\n  }, [shortcuts]);\r\n\r\n  useEffect(() => {\r\n    document.addEventListener('keydown', handleKeyDown);\r\n    return () => {\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [handleKeyDown]);\r\n\r\n  return shortcuts;\r\n};\r\n\r\n// 预定义的快捷键配置\r\nexport const createEditorShortcuts = (actions: {\r\n  bold: () => void;\r\n  italic: () => void;\r\n  save: () => void;\r\n  open: () => void;\r\n  newDocument: () => void;\r\n  find: () => void;\r\n  replace: () => void;\r\n  undo: () => void;\r\n  redo: () => void;\r\n  selectAll: () => void;\r\n  copy: () => void;\r\n  paste: () => void;\r\n  cut: () => void;\r\n}): ShortcutConfig[] => [\r\n  {\r\n    key: 'b',\r\n    ctrl: true,\r\n    action: actions.bold,\r\n    description: '粗体',\r\n  },\r\n  {\r\n    key: 'i',\r\n    ctrl: true,\r\n    action: actions.italic,\r\n    description: '斜体',\r\n  },\r\n  {\r\n    key: 's',\r\n    ctrl: true,\r\n    action: actions.save,\r\n    description: '保存',\r\n  },\r\n  {\r\n    key: 'o',\r\n    ctrl: true,\r\n    action: actions.open,\r\n    description: '打开',\r\n  },\r\n  {\r\n    key: 'n',\r\n    ctrl: true,\r\n    action: actions.newDocument,\r\n    description: '新建',\r\n  },\r\n  {\r\n    key: 'f',\r\n    ctrl: true,\r\n    action: actions.find,\r\n    description: '查找',\r\n  },\r\n  {\r\n    key: 'h',\r\n    ctrl: true,\r\n    action: actions.replace,\r\n    description: '替换',\r\n  },\r\n  {\r\n    key: 'z',\r\n    ctrl: true,\r\n    action: actions.undo,\r\n    description: '撤销',\r\n  },\r\n  {\r\n    key: 'y',\r\n    ctrl: true,\r\n    action: actions.redo,\r\n    description: '重做',\r\n  },\r\n  {\r\n    key: 'a',\r\n    ctrl: true,\r\n    action: actions.selectAll,\r\n    description: '全选',\r\n  },\r\n  {\r\n    key: 'c',\r\n    ctrl: true,\r\n    action: actions.copy,\r\n    description: '复制',\r\n  },\r\n  {\r\n    key: 'v',\r\n    ctrl: true,\r\n    action: actions.paste,\r\n    description: '粘贴',\r\n  },\r\n  {\r\n    key: 'x',\r\n    ctrl: true,\r\n    action: actions.cut,\r\n    description: '剪切',\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAcO,MAAM,uBAAuB,CAAC;IACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,WAAW,MAAM,GAAG,CAAC,WAAW,OAAO,SAAS,GAAG,CAAC,WAAW;YACrE,MAAM,YAAY,CAAC,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO;YACrE,MAAM,WAAW,CAAC,CAAC,SAAS,GAAG,KAAK,MAAM,MAAM;YAChD,MAAM,aAAa,CAAC,CAAC,SAAS,KAAK,KAAK,MAAM,QAAQ;YACtD,MAAM,YAAY,CAAC,CAAC,SAAS,IAAI,KAAK,MAAM,OAAO;YAEnD,IAAI,YAAY,aAAa,YAAY,cAAc,WAAW;gBAChE,MAAM,cAAc;gBACpB,SAAS,MAAM;gBACf;YACF;QACF;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;AACT;AAGO,MAAM,wBAAwB,CAAC,UAcd;QACtB;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,MAAM;YACtB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,WAAW;YAC3B,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,OAAO;YACvB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,SAAS;YACzB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,IAAI;YACpB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,KAAK;YACrB,aAAa;QACf;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ,QAAQ,GAAG;YACnB,aAAa;QACf;KACD", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/FindReplaceDialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\n\r\ninterface FindReplaceDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  content: string;\r\n  onContentChange: (newContent: string) => void;\r\n  textareaRef: React.RefObject<HTMLTextAreaElement>;\r\n}\r\n\r\nexport const FindReplaceDialog: React.FC<FindReplaceDialogProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  content,\r\n  onContentChange,\r\n  textareaRef,\r\n}) => {\r\n  const [findText, setFindText] = useState('');\r\n  const [replaceText, setReplaceText] = useState('');\r\n  const [useRegex, setUseRegex] = useState(false);\r\n  const [caseSensitive, setCaseSensitive] = useState(false);\r\n  const [currentMatch, setCurrentMatch] = useState(0);\r\n  const [totalMatches, setTotalMatches] = useState(0);\r\n  const [matches, setMatches] = useState<Array<{ start: number; end: number }>>([]);\r\n  \r\n  const findInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // 查找匹配项\r\n  const findMatches = (searchText: string) => {\r\n    if (!searchText) {\r\n      setMatches([]);\r\n      setTotalMatches(0);\r\n      setCurrentMatch(0);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      let regex: RegExp;\r\n      if (useRegex) {\r\n        const flags = caseSensitive ? 'g' : 'gi';\r\n        regex = new RegExp(searchText, flags);\r\n      } else {\r\n        const escapedText = searchText.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n        const flags = caseSensitive ? 'g' : 'gi';\r\n        regex = new RegExp(escapedText, flags);\r\n      }\r\n\r\n      const foundMatches: Array<{ start: number; end: number }> = [];\r\n      let match;\r\n      while ((match = regex.exec(content)) !== null) {\r\n        foundMatches.push({\r\n          start: match.index,\r\n          end: match.index + match[0].length,\r\n        });\r\n        // 防止无限循环\r\n        if (match[0].length === 0) break;\r\n      }\r\n\r\n      setMatches(foundMatches);\r\n      setTotalMatches(foundMatches.length);\r\n      setCurrentMatch(foundMatches.length > 0 ? 1 : 0);\r\n      \r\n      // 高亮第一个匹配项\r\n      if (foundMatches.length > 0) {\r\n        highlightMatch(0);\r\n      }\r\n    } catch (error) {\r\n      // 正则表达式错误\r\n      setMatches([]);\r\n      setTotalMatches(0);\r\n      setCurrentMatch(0);\r\n    }\r\n  };\r\n\r\n  // 高亮匹配项\r\n  const highlightMatch = (index: number) => {\r\n    if (matches.length === 0 || !textareaRef.current) return;\r\n    \r\n    const match = matches[index];\r\n    textareaRef.current.focus();\r\n    textareaRef.current.setSelectionRange(match.start, match.end);\r\n    textareaRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n  };\r\n\r\n  // 查找下一个\r\n  const findNext = () => {\r\n    if (matches.length === 0) return;\r\n    const nextIndex = currentMatch >= matches.length ? 0 : currentMatch;\r\n    setCurrentMatch(nextIndex + 1);\r\n    highlightMatch(nextIndex);\r\n  };\r\n\r\n  // 查找上一个\r\n  const findPrevious = () => {\r\n    if (matches.length === 0) return;\r\n    const prevIndex = currentMatch <= 1 ? matches.length - 1 : currentMatch - 2;\r\n    setCurrentMatch(prevIndex + 1);\r\n    highlightMatch(prevIndex);\r\n  };\r\n\r\n  // 替换当前匹配项\r\n  const replaceCurrent = () => {\r\n    if (matches.length === 0 || currentMatch === 0) return;\r\n    \r\n    const matchIndex = currentMatch - 1;\r\n    const match = matches[matchIndex];\r\n    const newContent = content.substring(0, match.start) + \r\n                      replaceText + \r\n                      content.substring(match.end);\r\n    \r\n    onContentChange(newContent);\r\n    \r\n    // 重新查找（因为内容已改变）\r\n    setTimeout(() => {\r\n      findMatches(findText);\r\n    }, 0);\r\n  };\r\n\r\n  // 替换全部\r\n  const replaceAll = () => {\r\n    if (!findText) return;\r\n    \r\n    try {\r\n      let regex: RegExp;\r\n      if (useRegex) {\r\n        const flags = caseSensitive ? 'g' : 'gi';\r\n        regex = new RegExp(findText, flags);\r\n      } else {\r\n        const escapedText = findText.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n        const flags = caseSensitive ? 'g' : 'gi';\r\n        regex = new RegExp(escapedText, flags);\r\n      }\r\n\r\n      const newContent = content.replace(regex, replaceText);\r\n      onContentChange(newContent);\r\n      \r\n      // 重新查找\r\n      setTimeout(() => {\r\n        findMatches(findText);\r\n      }, 0);\r\n    } catch (error) {\r\n      // 正则表达式错误\r\n    }\r\n  };\r\n\r\n  // 监听查找文本变化\r\n  useEffect(() => {\r\n    findMatches(findText);\r\n  }, [findText, useRegex, caseSensitive, content]);\r\n\r\n  // 对话框打开时聚焦输入框\r\n  useEffect(() => {\r\n    if (isOpen && findInputRef.current) {\r\n      findInputRef.current.focus();\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // 键盘事件处理\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Escape') {\r\n      onClose();\r\n    } else if (e.key === 'Enter') {\r\n      if (e.shiftKey) {\r\n        findPrevious();\r\n      } else {\r\n        findNext();\r\n      }\r\n    } else if (e.key === 'F3') {\r\n      e.preventDefault();\r\n      if (e.shiftKey) {\r\n        findPrevious();\r\n      } else {\r\n        findNext();\r\n      }\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-96 max-w-full mx-4\">\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            查找和替换\r\n          </h3>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n          >\r\n            ×\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\" onKeyDown={handleKeyDown}>\r\n          {/* 查找输入 */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              查找\r\n            </label>\r\n            <div className=\"flex gap-2\">\r\n              <input\r\n                ref={findInputRef}\r\n                type=\"text\"\r\n                value={findText}\r\n                onChange={(e) => setFindText(e.target.value)}\r\n                className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                placeholder=\"输入要查找的文本...\"\r\n              />\r\n              <button\r\n                onClick={findPrevious}\r\n                disabled={matches.length === 0}\r\n                className=\"px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md disabled:opacity-50\"\r\n                title=\"上一个 (Shift+Enter)\"\r\n              >\r\n                ↑\r\n              </button>\r\n              <button\r\n                onClick={findNext}\r\n                disabled={matches.length === 0}\r\n                className=\"px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md disabled:opacity-50\"\r\n                title=\"下一个 (Enter)\"\r\n              >\r\n                ↓\r\n              </button>\r\n            </div>\r\n            {findText && (\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n                {totalMatches > 0 ? `${currentMatch}/${totalMatches} 个匹配项` : '无匹配项'}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 替换输入 */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              替换为\r\n            </label>\r\n            <div className=\"flex gap-2\">\r\n              <input\r\n                type=\"text\"\r\n                value={replaceText}\r\n                onChange={(e) => setReplaceText(e.target.value)}\r\n                className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                placeholder=\"输入替换文本...\"\r\n              />\r\n              <button\r\n                onClick={replaceCurrent}\r\n                disabled={matches.length === 0 || currentMatch === 0}\r\n                className=\"px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md disabled:opacity-50\"\r\n              >\r\n                替换\r\n              </button>\r\n              <button\r\n                onClick={replaceAll}\r\n                disabled={matches.length === 0}\r\n                className=\"px-3 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md disabled:opacity-50\"\r\n              >\r\n                全部\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 选项 */}\r\n          <div className=\"flex gap-4\">\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={caseSensitive}\r\n                onChange={(e) => setCaseSensitive(e.target.checked)}\r\n                className=\"mr-2\"\r\n              />\r\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">区分大小写</span>\r\n            </label>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={useRegex}\r\n                onChange={(e) => setUseRegex(e.target.checked)}\r\n                className=\"mr-2\"\r\n              />\r\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">正则表达式</span>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYO,MAAM,oBAAsD,CAAC,EAClE,MAAM,EACN,OAAO,EACP,OAAO,EACP,eAAe,EACf,WAAW,EACZ;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyC,EAAE;IAEhF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,QAAQ;IACR,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,YAAY;YACf,WAAW,EAAE;YACb,gBAAgB;YAChB,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,IAAI;YACJ,IAAI,UAAU;gBACZ,MAAM,QAAQ,gBAAgB,MAAM;gBACpC,QAAQ,IAAI,OAAO,YAAY;YACjC,OAAO;gBACL,MAAM,cAAc,WAAW,OAAO,CAAC,uBAAuB;gBAC9D,MAAM,QAAQ,gBAAgB,MAAM;gBACpC,QAAQ,IAAI,OAAO,aAAa;YAClC;YAEA,MAAM,eAAsD,EAAE;YAC9D,IAAI;YACJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,KAAM;gBAC7C,aAAa,IAAI,CAAC;oBAChB,OAAO,MAAM,KAAK;oBAClB,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;gBACpC;gBACA,SAAS;gBACT,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,GAAG;YAC7B;YAEA,WAAW;YACX,gBAAgB,aAAa,MAAM;YACnC,gBAAgB,aAAa,MAAM,GAAG,IAAI,IAAI;YAE9C,WAAW;YACX,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,UAAU;YACV,WAAW,EAAE;YACb,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IAEA,QAAQ;IACR,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY,OAAO,EAAE;QAElD,MAAM,QAAQ,OAAO,CAAC,MAAM;QAC5B,YAAY,OAAO,CAAC,KAAK;QACzB,YAAY,OAAO,CAAC,iBAAiB,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;QAC5D,YAAY,OAAO,CAAC,cAAc,CAAC;YAAE,UAAU;YAAU,OAAO;QAAS;IAC3E;IAEA,QAAQ;IACR,MAAM,WAAW;QACf,IAAI,QAAQ,MAAM,KAAK,GAAG;QAC1B,MAAM,YAAY,gBAAgB,QAAQ,MAAM,GAAG,IAAI;QACvD,gBAAgB,YAAY;QAC5B,eAAe;IACjB;IAEA,QAAQ;IACR,MAAM,eAAe;QACnB,IAAI,QAAQ,MAAM,KAAK,GAAG;QAC1B,MAAM,YAAY,gBAAgB,IAAI,QAAQ,MAAM,GAAG,IAAI,eAAe;QAC1E,gBAAgB,YAAY;QAC5B,eAAe;IACjB;IAEA,UAAU;IACV,MAAM,iBAAiB;QACrB,IAAI,QAAQ,MAAM,KAAK,KAAK,iBAAiB,GAAG;QAEhD,MAAM,aAAa,eAAe;QAClC,MAAM,QAAQ,OAAO,CAAC,WAAW;QACjC,MAAM,aAAa,QAAQ,SAAS,CAAC,GAAG,MAAM,KAAK,IACjC,cACA,QAAQ,SAAS,CAAC,MAAM,GAAG;QAE7C,gBAAgB;QAEhB,gBAAgB;QAChB,WAAW;YACT,YAAY;QACd,GAAG;IACL;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,IAAI;YACJ,IAAI,UAAU;gBACZ,MAAM,QAAQ,gBAAgB,MAAM;gBACpC,QAAQ,IAAI,OAAO,UAAU;YAC/B,OAAO;gBACL,MAAM,cAAc,SAAS,OAAO,CAAC,uBAAuB;gBAC5D,MAAM,QAAQ,gBAAgB,MAAM;gBACpC,QAAQ,IAAI,OAAO,aAAa;YAClC;YAEA,MAAM,aAAa,QAAQ,OAAO,CAAC,OAAO;YAC1C,gBAAgB;YAEhB,OAAO;YACP,WAAW;gBACT,YAAY;YACd,GAAG;QACL,EAAE,OAAO,OAAO;QACd,UAAU;QACZ;IACF;IAEA,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG;QAAC;QAAU;QAAU;QAAe;KAAQ;IAE/C,cAAc;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,aAAa,OAAO,EAAE;YAClC,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF,GAAG;QAAC;KAAO;IAEX,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;YAC5B,IAAI,EAAE,QAAQ,EAAE;gBACd;YACF,OAAO;gBACL;YACF;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,MAAM;YACzB,EAAE,cAAc;YAChB,IAAI,EAAE,QAAQ,EAAE;gBACd;YACF,OAAO;gBACL;YACF;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;oBAAY,WAAW;;sCAEpC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;;;;;;sDAEd,8OAAC;4CACC,SAAS;4CACT,UAAU,QAAQ,MAAM,KAAK;4CAC7B,WAAU;4CACV,OAAM;sDACP;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,UAAU,QAAQ,MAAM,KAAK;4CAC7B,WAAU;4CACV,OAAM;sDACP;;;;;;;;;;;;gCAIF,0BACC,8OAAC;oCAAI,WAAU;8CACZ,eAAe,IAAI,GAAG,aAAa,CAAC,EAAE,aAAa,KAAK,CAAC,GAAG;;;;;;;;;;;;sCAMnE,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;4CACV,aAAY;;;;;;sDAEd,8OAAC;4CACC,SAAS;4CACT,UAAU,QAAQ,MAAM,KAAK,KAAK,iBAAiB;4CACnD,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,UAAU,QAAQ,MAAM,KAAK;4CAC7B,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;4CAClD,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;8CAE7D,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,OAAO;4CAC7C,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/export.ts"], "sourcesContent": ["import jsPDF from 'jspdf';\r\nimport html2canvas from 'html2canvas';\r\nimport { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType } from 'docx';\r\nimport { saveAs } from 'file-saver';\r\n\r\nexport interface ExportOptions {\r\n  format: 'pdf' | 'docx' | 'html' | 'txt';\r\n  title?: string;\r\n  author?: string;\r\n  pageSize?: 'A4' | 'Letter' | 'A3';\r\n  margin?: number;\r\n  fontSize?: number;\r\n  fontFamily?: string;\r\n  includeStyles?: boolean;\r\n  tableTheme?: 'professional' | 'modern' | 'minimal' | 'colorful';\r\n}\r\n\r\n// 表格主题配置 - 修复黑色背景问题\r\nconst tableThemes = {\r\n  professional: {\r\n    headerBg: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',\r\n    headerColor: '#ffffff',\r\n    borderColor: '#cbd5e0',\r\n    evenRowBg: '#f7fafc',\r\n    oddRowBg: '#ffffff',\r\n    hoverBg: '#edf2f7',\r\n    textColor: '#2d3748',\r\n    wordHeaderBg: '4A5568',\r\n    wordEvenBg: 'F7FAFC',\r\n    wordOddBg: 'FFFFFF',\r\n  },\r\n  modern: {\r\n    headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    headerColor: '#ffffff',\r\n    borderColor: '#e2e8f0',\r\n    evenRowBg: '#f8fafc',\r\n    oddRowBg: '#ffffff',\r\n    hoverBg: '#ebf8ff',\r\n    textColor: '#2d3748',\r\n    wordHeaderBg: '667EEA',\r\n    wordEvenBg: 'F8FAFC',\r\n    wordOddBg: 'FFFFFF',\r\n  },\r\n  minimal: {\r\n    headerBg: 'linear-gradient(135deg, #a0aec0 0%, #718096 100%)',\r\n    headerColor: '#ffffff',\r\n    borderColor: '#e2e8f0',\r\n    evenRowBg: '#f9fafb',\r\n    oddRowBg: '#ffffff',\r\n    hoverBg: '#f1f5f9',\r\n    textColor: '#374151',\r\n    wordHeaderBg: 'A0AEC0',\r\n    wordEvenBg: 'F9FAFB',\r\n    wordOddBg: 'FFFFFF',\r\n  },\r\n  colorful: {\r\n    headerBg: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',\r\n    headerColor: '#ffffff',\r\n    borderColor: '#fed7d7',\r\n    evenRowBg: '#fef5e7',\r\n    oddRowBg: '#ffffff',\r\n    hoverBg: '#ffeaa7',\r\n    textColor: '#2d3748',\r\n    wordHeaderBg: 'F56565',\r\n    wordEvenBg: 'FEF5E7',\r\n    wordOddBg: 'FFFFFF',\r\n  },\r\n};\r\n\r\n// Markdown解析器，专门处理表格\r\ninterface ParsedElement {\r\n  type: 'heading' | 'paragraph' | 'table' | 'list' | 'code' | 'blockquote';\r\n  level?: number;\r\n  content: string;\r\n  rows?: string[][];\r\n  isOrdered?: boolean;\r\n}\r\n\r\nconst parseMarkdown = (content: string): ParsedElement[] => {\r\n  const lines = content.split('\\n');\r\n  const elements: ParsedElement[] = [];\r\n  let i = 0;\r\n\r\n  while (i < lines.length) {\r\n    const line = lines[i].trim();\r\n\r\n    if (line === '') {\r\n      i++;\r\n      continue;\r\n    }\r\n\r\n    // 标题\r\n    if (line.startsWith('#')) {\r\n      const level = line.match(/^#+/)?.[0].length || 1;\r\n      elements.push({\r\n        type: 'heading',\r\n        level,\r\n        content: line.substring(level).trim(),\r\n      });\r\n      i++;\r\n    }\r\n    // 表格\r\n    else if (line.includes('|') && lines[i + 1]?.includes('|') && lines[i + 1]?.includes('-')) {\r\n      const tableRows: string[][] = [];\r\n\r\n      // 解析表头\r\n      const headerCells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');\r\n      tableRows.push(headerCells);\r\n\r\n      i++; // 跳过分隔行\r\n      i++; // 移动到数据行\r\n\r\n      // 解析数据行\r\n      while (i < lines.length && lines[i].includes('|')) {\r\n        const rowCells = lines[i].split('|').map(cell => cell.trim()).filter(cell => cell !== '');\r\n        if (rowCells.length > 0) {\r\n          tableRows.push(rowCells);\r\n        }\r\n        i++;\r\n      }\r\n\r\n      elements.push({\r\n        type: 'table',\r\n        content: '',\r\n        rows: tableRows,\r\n      });\r\n    }\r\n    // 代码块\r\n    else if (line.startsWith('```')) {\r\n      let codeContent = '';\r\n      i++; // 跳过开始标记\r\n      while (i < lines.length && !lines[i].startsWith('```')) {\r\n        codeContent += lines[i] + '\\n';\r\n        i++;\r\n      }\r\n      i++; // 跳过结束标记\r\n\r\n      elements.push({\r\n        type: 'code',\r\n        content: codeContent.trim(),\r\n      });\r\n    }\r\n    // 引用\r\n    else if (line.startsWith('>')) {\r\n      let quoteContent = line.substring(1).trim();\r\n      i++;\r\n      while (i < lines.length && lines[i].startsWith('>')) {\r\n        quoteContent += '\\n' + lines[i].substring(1).trim();\r\n        i++;\r\n      }\r\n\r\n      elements.push({\r\n        type: 'blockquote',\r\n        content: quoteContent,\r\n      });\r\n    }\r\n    // 列表\r\n    else if (line.match(/^[\\*\\-\\+]\\s/) || line.match(/^\\d+\\.\\s/)) {\r\n      const isOrdered = line.match(/^\\d+\\.\\s/) !== null;\r\n      let listContent = line;\r\n      i++;\r\n\r\n      while (i < lines.length && (lines[i].match(/^[\\*\\-\\+]\\s/) || lines[i].match(/^\\d+\\.\\s/) || lines[i].startsWith('  '))) {\r\n        listContent += '\\n' + lines[i];\r\n        i++;\r\n      }\r\n\r\n      elements.push({\r\n        type: 'list',\r\n        content: listContent,\r\n        isOrdered,\r\n      });\r\n    }\r\n    // 普通段落\r\n    else {\r\n      let paragraphContent = line;\r\n      i++;\r\n\r\n      // 合并连续的非空行为一个段落\r\n      while (i < lines.length && lines[i].trim() !== '' && !lines[i].startsWith('#') && !lines[i].includes('|') && !lines[i].startsWith('```') && !lines[i].startsWith('>') && !lines[i].match(/^[\\*\\-\\+]\\s/) && !lines[i].match(/^\\d+\\.\\s/)) {\r\n        paragraphContent += ' ' + lines[i].trim();\r\n        i++;\r\n      }\r\n\r\n      elements.push({\r\n        type: 'paragraph',\r\n        content: paragraphContent,\r\n      });\r\n    }\r\n  }\r\n\r\n  return elements;\r\n};\r\n\r\n// 优化表格数据，确保所有行都有相同的列数\r\nconst normalizeTableData = (rows: string[][]): string[][] => {\r\n  if (rows.length === 0) return rows;\r\n\r\n  const maxCols = Math.max(...rows.map(row => row.length));\r\n\r\n  return rows.map(row => {\r\n    const normalizedRow = [...row];\r\n    while (normalizedRow.length < maxCols) {\r\n      normalizedRow.push('');\r\n    }\r\n    return normalizedRow.map(cell => cell.trim());\r\n  });\r\n};\r\n\r\n// 计算表格列宽\r\nconst calculateColumnWidths = (rows: string[][]): number[] => {\r\n  if (rows.length === 0) return [];\r\n\r\n  const maxCols = Math.max(...rows.map(row => row.length));\r\n  const columnWidths: number[] = new Array(maxCols).fill(0);\r\n\r\n  // 计算每列的最大字符长度\r\n  rows.forEach(row => {\r\n    row.forEach((cell, colIndex) => {\r\n      const cellLength = cell.length;\r\n      if (cellLength > columnWidths[colIndex]) {\r\n        columnWidths[colIndex] = cellLength;\r\n      }\r\n    });\r\n  });\r\n\r\n  // 转换为百分比，确保总和为100%\r\n  const totalLength = columnWidths.reduce((sum, width) => sum + width, 0);\r\n  if (totalLength === 0) {\r\n    return new Array(maxCols).fill(100 / maxCols);\r\n  }\r\n\r\n  return columnWidths.map(width => Math.max((width / totalLength) * 100, 10)); // 最小10%\r\n};\r\n\r\n// 确保文字对比度的辅助函数\r\nconst ensureTextContrast = (backgroundColor: string, lightText: string = '#ffffff', darkText: string = '#2d3748') => {\r\n  // 简单的对比度检查，深色背景用浅色文字，浅色背景用深色文字\r\n  const bgLower = backgroundColor.toLowerCase();\r\n  if (bgLower.includes('#000') || bgLower.includes('black') || bgLower.includes('dark')) {\r\n    return lightText;\r\n  }\r\n  return darkText;\r\n};\r\n\r\n// 创建优化的HTML内容用于PDF导出\r\nconst createOptimizedHTML = (content: string, options: ExportOptions): string => {\r\n  const elements = parseMarkdown(content);\r\n  let html = '';\r\n\r\n  for (const element of elements) {\r\n    switch (element.type) {\r\n      case 'heading':\r\n        html += `<h${element.level}>${element.content}</h${element.level}>`;\r\n        break;\r\n\r\n      case 'table':\r\n        if (element.rows && element.rows.length > 0) {\r\n          const normalizedRows = normalizeTableData(element.rows);\r\n          const columnWidths = calculateColumnWidths(normalizedRows);\r\n\r\n          html += '<table class=\"export-table\">';\r\n          normalizedRows.forEach((row, index) => {\r\n            const tag = index === 0 ? 'th' : 'td';\r\n            html += '<tr>';\r\n            row.forEach((cell, colIndex) => {\r\n              const width = columnWidths[colIndex] || (100 / row.length);\r\n              html += `<${tag} style=\"width: ${width.toFixed(1)}%\">${cell || ''}</${tag}>`;\r\n            });\r\n            html += '</tr>';\r\n          });\r\n          html += '</table>';\r\n        }\r\n        break;\r\n\r\n      case 'paragraph':\r\n        // 处理内联格式\r\n        let text = element.content\r\n          .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n          .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n          .replace(/`(.*?)`/g, '<code>$1</code>');\r\n        html += `<p>${text}</p>`;\r\n        break;\r\n\r\n      case 'code':\r\n        html += `<pre><code>${element.content}</code></pre>`;\r\n        break;\r\n\r\n      case 'blockquote':\r\n        html += `<blockquote>${element.content}</blockquote>`;\r\n        break;\r\n\r\n      case 'list':\r\n        const listItems = element.content.split('\\n').filter(line => line.trim());\r\n        const listTag = element.isOrdered ? 'ol' : 'ul';\r\n        html += `<${listTag}>`;\r\n        listItems.forEach(item => {\r\n          const cleanItem = item.replace(/^[\\*\\-\\+\\d\\.]\\s*/, '');\r\n          html += `<li>${cleanItem}</li>`;\r\n        });\r\n        html += `</${listTag}>`;\r\n        break;\r\n    }\r\n  }\r\n\r\n  return html;\r\n};\r\n\r\n// PDF导出\r\nexport const exportToPDF = async (content: string, htmlContent: string, options: ExportOptions = { format: 'pdf' }) => {\r\n  try {\r\n    // 使用优化的HTML内容\r\n    const optimizedHTML = createOptimizedHTML(content, options);\r\n\r\n    // 创建临时div来渲染HTML内容\r\n    const tempDiv = document.createElement('div');\r\n    tempDiv.innerHTML = optimizedHTML;\r\n    tempDiv.style.cssText = `\r\n      position: absolute;\r\n      top: -9999px;\r\n      left: -9999px;\r\n      width: 750px;\r\n      padding: 40px;\r\n      font-family: ${options.fontFamily || 'Arial, sans-serif'};\r\n      font-size: ${options.fontSize || 14}px;\r\n      line-height: 1.6;\r\n      color: #333;\r\n      background: white;\r\n      box-sizing: border-box;\r\n    `;\r\n    \r\n    // 添加样式\r\n    if (options.includeStyles !== false) {\r\n      tempDiv.style.cssText += `\r\n        h1 { font-size: 24px; font-weight: bold; margin: 20px 0 10px 0; }\r\n        h2 { font-size: 20px; font-weight: bold; margin: 18px 0 8px 0; }\r\n        h3 { font-size: 16px; font-weight: bold; margin: 16px 0 6px 0; }\r\n        p { margin: 8px 0; }\r\n        ul, ol { margin: 8px 0; padding-left: 20px; }\r\n        li { margin: 4px 0; }\r\n        code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-family: monospace; }\r\n        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }\r\n        blockquote { border-left: 4px solid #ddd; margin: 10px 0; padding-left: 16px; font-style: italic; }\r\n        .export-table {\r\n          border-collapse: collapse;\r\n          width: 100%;\r\n          margin: 24px 0;\r\n          font-size: ${Math.max((options.fontSize || 14), 12)}px;\r\n          font-family: ${options.fontFamily || 'Arial, \"Microsoft YaHei\", sans-serif'};\r\n          table-layout: auto;\r\n          break-inside: avoid;\r\n          box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n          border-radius: 8px;\r\n          overflow: hidden;\r\n        }\r\n        .export-table th,\r\n        .export-table td {\r\n          border: 1px solid ${tableThemes[options.tableTheme || 'modern'].borderColor};\r\n          padding: 12px 16px;\r\n          text-align: left;\r\n          vertical-align: middle;\r\n          word-wrap: break-word;\r\n          overflow-wrap: break-word;\r\n          hyphens: auto;\r\n          line-height: 1.5;\r\n          color: ${tableThemes[options.tableTheme || 'modern'].textColor};\r\n        }\r\n        .export-table th {\r\n          background: ${tableThemes[options.tableTheme || 'modern'].headerBg};\r\n          font-weight: 600;\r\n          color: ${tableThemes[options.tableTheme || 'modern'].headerColor};\r\n          text-align: center;\r\n          font-size: ${Math.max((options.fontSize || 14), 12)}px;\r\n          text-shadow: 0 1px 2px rgba(0,0,0,0.1);\r\n          border-bottom: 2px solid ${tableThemes[options.tableTheme || 'modern'].borderColor};\r\n        }\r\n        .export-table tr:nth-child(even) td {\r\n          background-color: ${tableThemes[options.tableTheme || 'modern'].evenRowBg};\r\n        }\r\n        .export-table tr:nth-child(odd) td {\r\n          background-color: ${tableThemes[options.tableTheme || 'modern'].oddRowBg};\r\n        }\r\n        .export-table tr:hover td {\r\n          background-color: ${tableThemes[options.tableTheme || 'modern'].hoverBg};\r\n        }\r\n        .export-table td {\r\n          border-left: 1px solid #e1e5e9;\r\n          border-right: 1px solid #e1e5e9;\r\n        }\r\n        .export-table tr:last-child td {\r\n          border-bottom: 2px solid #667eea;\r\n        }\r\n        /* 确保表格在PDF中正确分页 */\r\n        .export-table tr {\r\n          page-break-inside: avoid;\r\n        }\r\n        /* 强制文字可见性 */\r\n        .export-table td {\r\n          color: ${tableThemes[options.tableTheme || 'modern'].textColor} !important;\r\n          background-color: transparent;\r\n        }\r\n        .export-table tr:nth-child(even) td {\r\n          background-color: ${tableThemes[options.tableTheme || 'modern'].evenRowBg} !important;\r\n          color: ${tableThemes[options.tableTheme || 'modern'].textColor} !important;\r\n        }\r\n        .export-table tr:nth-child(odd) td {\r\n          background-color: ${tableThemes[options.tableTheme || 'modern'].oddRowBg} !important;\r\n          color: ${tableThemes[options.tableTheme || 'modern'].textColor} !important;\r\n        }\r\n        /* 数字列右对齐 */\r\n        .export-table td:has-text(/^\\d+$/) {\r\n          text-align: right;\r\n        }\r\n        /* 防止任何黑色背景 */\r\n        .export-table * {\r\n          background-color: inherit !important;\r\n        }\r\n      `;\r\n    }\r\n    \r\n    document.body.appendChild(tempDiv);\r\n    \r\n    // 使用html2canvas转换为图片，优化表格渲染\r\n    const canvas = await html2canvas(tempDiv, {\r\n      scale: 3, // 提高分辨率\r\n      useCORS: true,\r\n      allowTaint: true,\r\n      backgroundColor: '#ffffff',\r\n      logging: false,\r\n      width: 750,\r\n      height: tempDiv.scrollHeight,\r\n      scrollX: 0,\r\n      scrollY: 0,\r\n      windowWidth: 750,\r\n      windowHeight: tempDiv.scrollHeight,\r\n    });\r\n    \r\n    document.body.removeChild(tempDiv);\r\n    \r\n    // 创建PDF\r\n    const pdf = new jsPDF({\r\n      orientation: 'portrait',\r\n      unit: 'mm',\r\n      format: options.pageSize?.toLowerCase() || 'a4',\r\n    });\r\n    \r\n    const imgData = canvas.toDataURL('image/png');\r\n    const imgWidth = 210; // A4 width in mm\r\n    const pageHeight = 295; // A4 height in mm\r\n    const imgHeight = (canvas.height * imgWidth) / canvas.width;\r\n    let heightLeft = imgHeight;\r\n    \r\n    let position = 0;\r\n    \r\n    // 添加页面\r\n    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\r\n    heightLeft -= pageHeight;\r\n    \r\n    while (heightLeft >= 0) {\r\n      position = heightLeft - imgHeight;\r\n      pdf.addPage();\r\n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\r\n      heightLeft -= pageHeight;\r\n    }\r\n    \r\n    // 添加元数据\r\n    if (options.title) {\r\n      pdf.setProperties({\r\n        title: options.title,\r\n        author: options.author || 'Martetdown Editor',\r\n        creator: 'Martetdown Editor',\r\n      });\r\n    }\r\n    \r\n    // 保存文件\r\n    const fileName = `${options.title || 'document'}.pdf`;\r\n    pdf.save(fileName);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('PDF export failed:', error);\r\n    throw new Error('PDF导出失败');\r\n  }\r\n};\r\n\r\n// Word导出\r\nexport const exportToWord = async (content: string, options: ExportOptions = { format: 'docx' }) => {\r\n  try {\r\n    const elements = parseMarkdown(content);\r\n    const documentChildren: any[] = [];\r\n\r\n    for (const element of elements) {\r\n      switch (element.type) {\r\n        case 'heading':\r\n          const headingLevel = element.level === 1 ? HeadingLevel.HEADING_1 :\r\n                              element.level === 2 ? HeadingLevel.HEADING_2 :\r\n                              element.level === 3 ? HeadingLevel.HEADING_3 :\r\n                              element.level === 4 ? HeadingLevel.HEADING_4 :\r\n                              element.level === 5 ? HeadingLevel.HEADING_5 :\r\n                              HeadingLevel.HEADING_6;\r\n\r\n          documentChildren.push(new Paragraph({\r\n            text: element.content,\r\n            heading: headingLevel,\r\n          }));\r\n          break;\r\n\r\n        case 'table':\r\n          if (element.rows && element.rows.length > 0) {\r\n            const normalizedRows = normalizeTableData(element.rows);\r\n            const columnWidths = calculateColumnWidths(normalizedRows);\r\n            const maxCols = normalizedRows[0]?.length || 0;\r\n\r\n            const tableRows = normalizedRows.map((row, rowIndex) => {\r\n              const cells = row.map((cellText, colIndex) => {\r\n                // 根据内容长度计算列宽\r\n                const widthPercent = columnWidths[colIndex] || (100 / maxCols);\r\n                const colWidth = Math.floor((9000 * widthPercent) / 100); // 转换为DXA单位\r\n\r\n                return new TableCell({\r\n                  children: [new Paragraph({\r\n                    children: [new TextRun({\r\n                      text: cellText || '',\r\n                      bold: rowIndex === 0, // 表头加粗\r\n                      color: rowIndex === 0 ? 'FFFFFF' : '2D3748', // 表头白色文字，内容深灰色\r\n                      size: rowIndex === 0 ? (options.fontSize || 14) * 2 : (options.fontSize || 14) * 2 - 2,\r\n                    })],\r\n                    spacing: {\r\n                      before: 120,\r\n                      after: 120,\r\n                    },\r\n                    alignment: rowIndex === 0 ? 'center' : 'left', // 表头居中\r\n                  })],\r\n                  width: {\r\n                    size: Math.max(colWidth, 1000), // 最小宽度\r\n                    type: WidthType.DXA,\r\n                  },\r\n                  margins: {\r\n                    top: 120,\r\n                    bottom: 120,\r\n                    left: 150,\r\n                    right: 150,\r\n                  },\r\n                  verticalAlign: 'center',\r\n                  shading: rowIndex === 0 ? {\r\n                    fill: tableThemes[options.tableTheme || 'modern'].wordHeaderBg,\r\n                    type: 'solid',\r\n                  } : rowIndex % 2 === 0 ? {\r\n                    fill: tableThemes[options.tableTheme || 'modern'].wordEvenBg,\r\n                    type: 'solid',\r\n                  } : {\r\n                    fill: tableThemes[options.tableTheme || 'modern'].wordOddBg,\r\n                    type: 'solid',\r\n                  },\r\n                });\r\n              });\r\n\r\n              return new TableRow({\r\n                children: cells,\r\n                tableHeader: rowIndex === 0,\r\n                height: {\r\n                  value: 400, // 最小行高\r\n                  rule: 'atLeast',\r\n                },\r\n              });\r\n            });\r\n\r\n            documentChildren.push(new Table({\r\n              rows: tableRows,\r\n              width: {\r\n                size: 100,\r\n                type: WidthType.PERCENTAGE,\r\n              },\r\n              borders: {\r\n                top: { style: 'single', size: 8, color: '4A90E2' },\r\n                bottom: { style: 'single', size: 8, color: '4A90E2' },\r\n                left: { style: 'single', size: 6, color: '4A90E2' },\r\n                right: { style: 'single', size: 6, color: '4A90E2' },\r\n                insideHorizontal: { style: 'single', size: 3, color: 'E1E5E9' },\r\n                insideVertical: { style: 'single', size: 3, color: 'E1E5E9' },\r\n              },\r\n              layout: 'fixed', // 固定表格布局\r\n            }));\r\n\r\n            // 在表格后添加空行\r\n            documentChildren.push(new Paragraph({ text: '' }));\r\n          }\r\n          break;\r\n\r\n        case 'code':\r\n          documentChildren.push(new Paragraph({\r\n            children: [new TextRun({\r\n              text: element.content,\r\n              font: 'Courier New',\r\n              size: (options.fontSize || 14) * 2 - 4, // Word uses half-points\r\n            })],\r\n            style: 'Code',\r\n          }));\r\n          break;\r\n\r\n        case 'blockquote':\r\n          documentChildren.push(new Paragraph({\r\n            text: element.content,\r\n            style: 'Quote',\r\n          }));\r\n          break;\r\n\r\n        case 'list':\r\n          // 简单处理列表项\r\n          const listItems = element.content.split('\\n').filter(line => line.trim());\r\n          for (const item of listItems) {\r\n            const cleanItem = item.replace(/^[\\*\\-\\+\\d\\.]\\s*/, '');\r\n            documentChildren.push(new Paragraph({\r\n              text: `• ${cleanItem}`,\r\n            }));\r\n          }\r\n          break;\r\n\r\n        case 'paragraph':\r\n          // 处理粗体和斜体\r\n          const textRuns: TextRun[] = [];\r\n          let text = element.content;\r\n\r\n          // 简单的格式处理\r\n          const parts = text.split(/(\\*\\*.*?\\*\\*|\\*.*?\\*|`.*?`)/);\r\n\r\n          for (const part of parts) {\r\n            if (part.startsWith('**') && part.endsWith('**')) {\r\n              textRuns.push(new TextRun({\r\n                text: part.slice(2, -2),\r\n                bold: true,\r\n              }));\r\n            } else if (part.startsWith('*') && part.endsWith('*')) {\r\n              textRuns.push(new TextRun({\r\n                text: part.slice(1, -1),\r\n                italics: true,\r\n              }));\r\n            } else if (part.startsWith('`') && part.endsWith('`')) {\r\n              textRuns.push(new TextRun({\r\n                text: part.slice(1, -1),\r\n                font: 'Courier New',\r\n              }));\r\n            } else if (part) {\r\n              textRuns.push(new TextRun({ text: part }));\r\n            }\r\n          }\r\n\r\n          documentChildren.push(new Paragraph({\r\n            children: textRuns.length > 0 ? textRuns : [new TextRun({ text: element.content })],\r\n          }));\r\n          break;\r\n      }\r\n    }\r\n    \r\n    const doc = new Document({\r\n      sections: [{\r\n        properties: {},\r\n        children: documentChildren,\r\n      }],\r\n      creator: 'Martetdown Editor',\r\n      title: options.title || 'Document',\r\n      description: 'Generated by Martetdown Editor',\r\n      styles: {\r\n        paragraphStyles: [\r\n          {\r\n            id: 'Code',\r\n            name: 'Code',\r\n            basedOn: 'Normal',\r\n            next: 'Normal',\r\n            run: {\r\n              font: 'Courier New',\r\n              size: (options.fontSize || 14) * 2 - 4,\r\n            },\r\n            paragraph: {\r\n              spacing: {\r\n                before: 200,\r\n                after: 200,\r\n              },\r\n            },\r\n          },\r\n          {\r\n            id: 'Quote',\r\n            name: 'Quote',\r\n            basedOn: 'Normal',\r\n            next: 'Normal',\r\n            run: {\r\n              italics: true,\r\n              color: '666666',\r\n            },\r\n            paragraph: {\r\n              spacing: {\r\n                before: 200,\r\n                after: 200,\r\n              },\r\n              indent: {\r\n                left: 720, // 0.5 inch\r\n              },\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    });\r\n    \r\n    const blob = await Packer.toBlob(doc);\r\n    const fileName = `${options.title || 'document'}.docx`;\r\n    saveAs(blob, fileName);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('Word export failed:', error);\r\n    throw new Error('Word导出失败');\r\n  }\r\n};\r\n\r\n// HTML导出\r\nexport const exportToHTML = (content: string, htmlContent: string, options: ExportOptions = { format: 'html' }) => {\r\n  try {\r\n    const htmlTemplate = `\r\n<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>${options.title || 'Document'}</title>\r\n    <style>\r\n        body {\r\n            font-family: ${options.fontFamily || 'Arial, sans-serif'};\r\n            font-size: ${options.fontSize || 14}px;\r\n            line-height: 1.6;\r\n            color: #333;\r\n            max-width: 800px;\r\n            margin: 0 auto;\r\n            padding: 40px 20px;\r\n            background: white;\r\n        }\r\n        h1, h2, h3, h4, h5, h6 {\r\n            color: #2c3e50;\r\n            margin-top: 24px;\r\n            margin-bottom: 12px;\r\n        }\r\n        h1 { font-size: 2em; border-bottom: 2px solid #eee; padding-bottom: 8px; }\r\n        h2 { font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 4px; }\r\n        h3 { font-size: 1.25em; }\r\n        p { margin: 12px 0; }\r\n        ul, ol { margin: 12px 0; padding-left: 24px; }\r\n        li { margin: 4px 0; }\r\n        code {\r\n            background: #f8f9fa;\r\n            padding: 2px 6px;\r\n            border-radius: 4px;\r\n            font-family: 'Courier New', monospace;\r\n            font-size: 0.9em;\r\n        }\r\n        pre {\r\n            background: #f8f9fa;\r\n            padding: 16px;\r\n            border-radius: 6px;\r\n            overflow-x: auto;\r\n            border: 1px solid #e9ecef;\r\n        }\r\n        pre code {\r\n            background: none;\r\n            padding: 0;\r\n        }\r\n        blockquote {\r\n            border-left: 4px solid #3498db;\r\n            margin: 16px 0;\r\n            padding-left: 16px;\r\n            color: #666;\r\n            font-style: italic;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin: 24px 0;\r\n            font-size: ${options.fontSize || 14}px;\r\n            font-family: ${options.fontFamily || 'Arial, \"Microsoft YaHei\", sans-serif'};\r\n            box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n            border-radius: 12px;\r\n            overflow: hidden;\r\n            background: #ffffff;\r\n        }\r\n        th, td {\r\n            border: none;\r\n            padding: 16px 20px;\r\n            text-align: left;\r\n            vertical-align: middle;\r\n            word-wrap: break-word;\r\n            position: relative;\r\n            color: ${tableThemes[options.tableTheme || 'modern'].textColor};\r\n        }\r\n        th {\r\n            background: ${tableThemes[options.tableTheme || 'modern'].headerBg};\r\n            font-weight: 600;\r\n            color: ${tableThemes[options.tableTheme || 'modern'].headerColor};\r\n            text-align: center;\r\n            text-shadow: 0 1px 2px rgba(0,0,0,0.2);\r\n            font-size: ${(options.fontSize || 14) + 1}px;\r\n            letter-spacing: 0.5px;\r\n        }\r\n        td {\r\n            border-bottom: 1px solid ${tableThemes[options.tableTheme || 'modern'].borderColor};\r\n            transition: all 0.2s ease;\r\n        }\r\n        tr:nth-child(even) td {\r\n            background-color: ${tableThemes[options.tableTheme || 'modern'].evenRowBg};\r\n        }\r\n        tr:nth-child(odd) td {\r\n            background-color: ${tableThemes[options.tableTheme || 'modern'].oddRowBg};\r\n        }\r\n        tr:hover td {\r\n            background-color: ${tableThemes[options.tableTheme || 'modern'].hoverBg} !important;\r\n            transform: scale(1.01);\r\n        }\r\n        tr:last-child td {\r\n            border-bottom: 3px solid #667eea;\r\n        }\r\n        /* 第一列加粗 */\r\n        td:first-child {\r\n            font-weight: 600;\r\n            color: #2d3748;\r\n        }\r\n        /* 响应式表格 */\r\n        @media (max-width: 768px) {\r\n            table {\r\n                font-size: 12px;\r\n                margin: 16px 0;\r\n            }\r\n            th, td {\r\n                padding: 12px 8px;\r\n            }\r\n        }\r\n        @media print {\r\n            table {\r\n                box-shadow: none;\r\n                border: 1px solid #ddd;\r\n            }\r\n            tr:hover td {\r\n                background-color: inherit !important;\r\n                transform: none;\r\n            }\r\n        }\r\n        a {\r\n            color: #3498db;\r\n            text-decoration: none;\r\n        }\r\n        a:hover {\r\n            text-decoration: underline;\r\n        }\r\n        img {\r\n            max-width: 100%;\r\n            height: auto;\r\n            border-radius: 4px;\r\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n        }\r\n        @media print {\r\n            body { margin: 0; padding: 20px; }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    ${htmlContent}\r\n</body>\r\n</html>`;\r\n    \r\n    const blob = new Blob([htmlTemplate], { type: 'text/html;charset=utf-8' });\r\n    const fileName = `${options.title || 'document'}.html`;\r\n    saveAs(blob, fileName);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('HTML export failed:', error);\r\n    throw new Error('HTML导出失败');\r\n  }\r\n};\r\n\r\n// 纯文本导出\r\nexport const exportToText = (content: string, options: ExportOptions = { format: 'txt' }) => {\r\n  try {\r\n    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });\r\n    const fileName = `${options.title || 'document'}.txt`;\r\n    saveAs(blob, fileName);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('Text export failed:', error);\r\n    throw new Error('文本导出失败');\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAcA,oBAAoB;AACpB,MAAM,cAAc;IAClB,cAAc;QACZ,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;QACT,WAAW;QACX,cAAc;QACd,YAAY;QACZ,WAAW;IACb;IACA,QAAQ;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;QACT,WAAW;QACX,cAAc;QACd,YAAY;QACZ,WAAW;IACb;IACA,SAAS;QACP,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;QACT,WAAW;QACX,cAAc;QACd,YAAY;QACZ,WAAW;IACb;IACA,UAAU;QACR,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;QACT,WAAW;QACX,cAAc;QACd,YAAY;QACZ,WAAW;IACb;AACF;AAWA,MAAM,gBAAgB,CAAC;IACrB,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,WAA4B,EAAE;IACpC,IAAI,IAAI;IAER,MAAO,IAAI,MAAM,MAAM,CAAE;QACvB,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAE1B,IAAI,SAAS,IAAI;YACf;YACA;QACF;QAEA,KAAK;QACL,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU;YAC/C,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN;gBACA,SAAS,KAAK,SAAS,CAAC,OAAO,IAAI;YACrC;YACA;QACF,OAEK,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,CAAC,IAAI,EAAE,EAAE,SAAS,QAAQ,KAAK,CAAC,IAAI,EAAE,EAAE,SAAS,MAAM;YACzF,MAAM,YAAwB,EAAE;YAEhC,OAAO;YACP,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,SAAS;YACrF,UAAU,IAAI,CAAC;YAEf,KAAK,QAAQ;YACb,KAAK,SAAS;YAEd,QAAQ;YACR,MAAO,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAM;gBACjD,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,SAAS;gBACtF,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,UAAU,IAAI,CAAC;gBACjB;gBACA;YACF;YAEA,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,SAAS;gBACT,MAAM;YACR;QACF,OAEK,IAAI,KAAK,UAAU,CAAC,QAAQ;YAC/B,IAAI,cAAc;YAClB,KAAK,SAAS;YACd,MAAO,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,OAAQ;gBACtD,eAAe,KAAK,CAAC,EAAE,GAAG;gBAC1B;YACF;YACA,KAAK,SAAS;YAEd,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,SAAS,YAAY,IAAI;YAC3B;QACF,OAEK,IAAI,KAAK,UAAU,CAAC,MAAM;YAC7B,IAAI,eAAe,KAAK,SAAS,CAAC,GAAG,IAAI;YACzC;YACA,MAAO,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,KAAM;gBACnD,gBAAgB,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,IAAI;gBACjD;YACF;YAEA,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,SAAS;YACX;QACF,OAEK,IAAI,KAAK,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC,aAAa;YAC5D,MAAM,YAAY,KAAK,KAAK,CAAC,gBAAgB;YAC7C,IAAI,cAAc;YAClB;YAEA,MAAO,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,EAAG;gBACrH,eAAe,OAAO,KAAK,CAAC,EAAE;gBAC9B;YACF;YAEA,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,SAAS;gBACT;YACF;QACF,OAEK;YACH,IAAI,mBAAmB;YACvB;YAEA,gBAAgB;YAChB,MAAO,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,YAAa;gBACtO,oBAAoB,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI;gBACvC;YACF;YAEA,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEA,OAAO;AACT;AAEA,sBAAsB;AACtB,MAAM,qBAAqB,CAAC;IAC1B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;IAE9B,MAAM,UAAU,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;IAEtD,OAAO,KAAK,GAAG,CAAC,CAAA;QACd,MAAM,gBAAgB;eAAI;SAAI;QAC9B,MAAO,cAAc,MAAM,GAAG,QAAS;YACrC,cAAc,IAAI,CAAC;QACrB;QACA,OAAO,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC5C;AACF;AAEA,SAAS;AACT,MAAM,wBAAwB,CAAC;IAC7B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,EAAE;IAEhC,MAAM,UAAU,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;IACtD,MAAM,eAAyB,IAAI,MAAM,SAAS,IAAI,CAAC;IAEvD,cAAc;IACd,KAAK,OAAO,CAAC,CAAA;QACX,IAAI,OAAO,CAAC,CAAC,MAAM;YACjB,MAAM,aAAa,KAAK,MAAM;YAC9B,IAAI,aAAa,YAAY,CAAC,SAAS,EAAE;gBACvC,YAAY,CAAC,SAAS,GAAG;YAC3B;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO;IACrE,IAAI,gBAAgB,GAAG;QACrB,OAAO,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM;IACvC;IAEA,OAAO,aAAa,GAAG,CAAC,CAAA,QAAS,KAAK,GAAG,CAAC,AAAC,QAAQ,cAAe,KAAK,MAAM,QAAQ;AACvF;AAEA,eAAe;AACf,MAAM,qBAAqB,CAAC,iBAAyB,YAAoB,SAAS,EAAE,WAAmB,SAAS;IAC9G,+BAA+B;IAC/B,MAAM,UAAU,gBAAgB,WAAW;IAC3C,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,SAAS;QACrF,OAAO;IACT;IACA,OAAO;AACT;AAEA,qBAAqB;AACrB,MAAM,sBAAsB,CAAC,SAAiB;IAC5C,MAAM,WAAW,cAAc;IAC/B,IAAI,OAAO;IAEX,KAAK,MAAM,WAAW,SAAU;QAC9B,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,QAAQ,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;gBACnE;YAEF,KAAK;gBACH,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;oBAC3C,MAAM,iBAAiB,mBAAmB,QAAQ,IAAI;oBACtD,MAAM,eAAe,sBAAsB;oBAE3C,QAAQ;oBACR,eAAe,OAAO,CAAC,CAAC,KAAK;wBAC3B,MAAM,MAAM,UAAU,IAAI,OAAO;wBACjC,QAAQ;wBACR,IAAI,OAAO,CAAC,CAAC,MAAM;4BACjB,MAAM,QAAQ,YAAY,CAAC,SAAS,IAAK,MAAM,IAAI,MAAM;4BACzD,QAAQ,CAAC,CAAC,EAAE,IAAI,eAAe,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,EAAE,QAAQ,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC;wBAC9E;wBACA,QAAQ;oBACV;oBACA,QAAQ;gBACV;gBACA;YAEF,KAAK;gBACH,SAAS;gBACT,IAAI,OAAO,QAAQ,OAAO,CACvB,OAAO,CAAC,kBAAkB,uBAC1B,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,YAAY;gBACvB,QAAQ,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;gBACxB;YAEF,KAAK;gBACH,QAAQ,CAAC,WAAW,EAAE,QAAQ,OAAO,CAAC,aAAa,CAAC;gBACpD;YAEF,KAAK;gBACH,QAAQ,CAAC,YAAY,EAAE,QAAQ,OAAO,CAAC,aAAa,CAAC;gBACrD;YAEF,KAAK;gBACH,MAAM,YAAY,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;gBACtE,MAAM,UAAU,QAAQ,SAAS,GAAG,OAAO;gBAC3C,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACtB,UAAU,OAAO,CAAC,CAAA;oBAChB,MAAM,YAAY,KAAK,OAAO,CAAC,oBAAoB;oBACnD,QAAQ,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC;gBACjC;gBACA,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACvB;QACJ;IACF;IAEA,OAAO;AACT;AAGO,MAAM,cAAc,OAAO,SAAiB,aAAqB,UAAyB;IAAE,QAAQ;AAAM,CAAC;IAChH,IAAI;QACF,cAAc;QACd,MAAM,gBAAgB,oBAAoB,SAAS;QAEnD,mBAAmB;QACnB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,SAAS,GAAG;QACpB,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;;mBAMV,EAAE,QAAQ,UAAU,IAAI,oBAAoB;iBAC9C,EAAE,QAAQ,QAAQ,IAAI,GAAG;;;;;IAKtC,CAAC;QAED,OAAO;QACP,IAAI,QAAQ,aAAa,KAAK,OAAO;YACnC,QAAQ,KAAK,CAAC,OAAO,IAAI,CAAC;;;;;;;;;;;;;;qBAcX,EAAE,KAAK,GAAG,CAAE,QAAQ,QAAQ,IAAI,IAAK,IAAI;uBACvC,EAAE,QAAQ,UAAU,IAAI,uCAAuC;;;;;;;;;4BAS1D,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,WAAW,CAAC;;;;;;;;iBAQrE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;sBAGnD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,QAAQ,CAAC;;iBAE5D,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,WAAW,CAAC;;qBAEtD,EAAE,KAAK,GAAG,CAAE,QAAQ,QAAQ,IAAI,IAAK,IAAI;;mCAE3B,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,WAAW,CAAC;;;4BAGjE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;4BAGxD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,QAAQ,CAAC;;;4BAGvD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;iBAejE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;;4BAI7C,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;iBACnE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;4BAG7C,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,QAAQ,CAAC;iBAClE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;;;;;;;;MAUnE,CAAC;QACH;QAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,4BAA4B;QAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAW,AAAD,EAAE,SAAS;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,iBAAiB;YACjB,SAAS;YACT,OAAO;YACP,QAAQ,QAAQ,YAAY;YAC5B,SAAS;YACT,SAAS;YACT,aAAa;YACb,cAAc,QAAQ,YAAY;QACpC;QAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,QAAQ;QACR,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK,CAAC;YACpB,aAAa;YACb,MAAM;YACN,QAAQ,QAAQ,QAAQ,EAAE,iBAAiB;QAC7C;QAEA,MAAM,UAAU,OAAO,SAAS,CAAC;QACjC,MAAM,WAAW,KAAK,iBAAiB;QACvC,MAAM,aAAa,KAAK,kBAAkB;QAC1C,MAAM,YAAY,AAAC,OAAO,MAAM,GAAG,WAAY,OAAO,KAAK;QAC3D,IAAI,aAAa;QAEjB,IAAI,WAAW;QAEf,OAAO;QACP,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;QACpD,cAAc;QAEd,MAAO,cAAc,EAAG;YACtB,WAAW,aAAa;YACxB,IAAI,OAAO;YACX,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;YACpD,cAAc;QAChB;QAEA,QAAQ;QACR,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,aAAa,CAAC;gBAChB,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,SAAS;YACX;QACF;QAEA,OAAO;QACP,MAAM,WAAW,GAAG,QAAQ,KAAK,IAAI,WAAW,IAAI,CAAC;QACrD,IAAI,IAAI,CAAC;QAET,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,eAAe,OAAO,SAAiB,UAAyB;IAAE,QAAQ;AAAO,CAAC;IAC7F,IAAI;QACF,MAAM,WAAW,cAAc;QAC/B,MAAM,mBAA0B,EAAE;QAElC,KAAK,MAAM,WAAW,SAAU;YAC9B,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,MAAM,eAAe,QAAQ,KAAK,KAAK,IAAI,sIAAA,CAAA,eAAY,CAAC,SAAS,GAC7C,QAAQ,KAAK,KAAK,IAAI,sIAAA,CAAA,eAAY,CAAC,SAAS,GAC5C,QAAQ,KAAK,KAAK,IAAI,sIAAA,CAAA,eAAY,CAAC,SAAS,GAC5C,QAAQ,KAAK,KAAK,IAAI,sIAAA,CAAA,eAAY,CAAC,SAAS,GAC5C,QAAQ,KAAK,KAAK,IAAI,sIAAA,CAAA,eAAY,CAAC,SAAS,GAC5C,sIAAA,CAAA,eAAY,CAAC,SAAS;oBAE1C,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;wBAClC,MAAM,QAAQ,OAAO;wBACrB,SAAS;oBACX;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;wBAC3C,MAAM,iBAAiB,mBAAmB,QAAQ,IAAI;wBACtD,MAAM,eAAe,sBAAsB;wBAC3C,MAAM,UAAU,cAAc,CAAC,EAAE,EAAE,UAAU;wBAE7C,MAAM,YAAY,eAAe,GAAG,CAAC,CAAC,KAAK;4BACzC,MAAM,QAAQ,IAAI,GAAG,CAAC,CAAC,UAAU;gCAC/B,aAAa;gCACb,MAAM,eAAe,YAAY,CAAC,SAAS,IAAK,MAAM;gCACtD,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,OAAO,eAAgB,MAAM,WAAW;gCAErE,OAAO,IAAI,sIAAA,CAAA,YAAS,CAAC;oCACnB,UAAU;wCAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;4CACvB,UAAU;gDAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;oDACrB,MAAM,YAAY;oDAClB,MAAM,aAAa;oDACnB,OAAO,aAAa,IAAI,WAAW;oDACnC,MAAM,aAAa,IAAI,CAAC,QAAQ,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,QAAQ,IAAI,EAAE,IAAI,IAAI;gDACvF;6CAAG;4CACH,SAAS;gDACP,QAAQ;gDACR,OAAO;4CACT;4CACA,WAAW,aAAa,IAAI,WAAW;wCACzC;qCAAG;oCACH,OAAO;wCACL,MAAM,KAAK,GAAG,CAAC,UAAU;wCACzB,MAAM,sIAAA,CAAA,YAAS,CAAC,GAAG;oCACrB;oCACA,SAAS;wCACP,KAAK;wCACL,QAAQ;wCACR,MAAM;wCACN,OAAO;oCACT;oCACA,eAAe;oCACf,SAAS,aAAa,IAAI;wCACxB,MAAM,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,YAAY;wCAC9D,MAAM;oCACR,IAAI,WAAW,MAAM,IAAI;wCACvB,MAAM,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,UAAU;wCAC5D,MAAM;oCACR,IAAI;wCACF,MAAM,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS;wCAC3D,MAAM;oCACR;gCACF;4BACF;4BAEA,OAAO,IAAI,sIAAA,CAAA,WAAQ,CAAC;gCAClB,UAAU;gCACV,aAAa,aAAa;gCAC1B,QAAQ;oCACN,OAAO;oCACP,MAAM;gCACR;4BACF;wBACF;wBAEA,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,QAAK,CAAC;4BAC9B,MAAM;4BACN,OAAO;gCACL,MAAM;gCACN,MAAM,sIAAA,CAAA,YAAS,CAAC,UAAU;4BAC5B;4BACA,SAAS;gCACP,KAAK;oCAAE,OAAO;oCAAU,MAAM;oCAAG,OAAO;gCAAS;gCACjD,QAAQ;oCAAE,OAAO;oCAAU,MAAM;oCAAG,OAAO;gCAAS;gCACpD,MAAM;oCAAE,OAAO;oCAAU,MAAM;oCAAG,OAAO;gCAAS;gCAClD,OAAO;oCAAE,OAAO;oCAAU,MAAM;oCAAG,OAAO;gCAAS;gCACnD,kBAAkB;oCAAE,OAAO;oCAAU,MAAM;oCAAG,OAAO;gCAAS;gCAC9D,gBAAgB;oCAAE,OAAO;oCAAU,MAAM;oCAAG,OAAO;gCAAS;4BAC9D;4BACA,QAAQ;wBACV;wBAEA,WAAW;wBACX,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;4BAAE,MAAM;wBAAG;oBACjD;oBACA;gBAEF,KAAK;oBACH,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;wBAClC,UAAU;4BAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;gCACrB,MAAM,QAAQ,OAAO;gCACrB,MAAM;gCACN,MAAM,CAAC,QAAQ,QAAQ,IAAI,EAAE,IAAI,IAAI;4BACvC;yBAAG;wBACH,OAAO;oBACT;oBACA;gBAEF,KAAK;oBACH,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;wBAClC,MAAM,QAAQ,OAAO;wBACrB,OAAO;oBACT;oBACA;gBAEF,KAAK;oBACH,UAAU;oBACV,MAAM,YAAY,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;oBACtE,KAAK,MAAM,QAAQ,UAAW;wBAC5B,MAAM,YAAY,KAAK,OAAO,CAAC,oBAAoB;wBACnD,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;4BAClC,MAAM,CAAC,EAAE,EAAE,WAAW;wBACxB;oBACF;oBACA;gBAEF,KAAK;oBACH,UAAU;oBACV,MAAM,WAAsB,EAAE;oBAC9B,IAAI,OAAO,QAAQ,OAAO;oBAE1B,UAAU;oBACV,MAAM,QAAQ,KAAK,KAAK,CAAC;oBAEzB,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI,KAAK,UAAU,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO;4BAChD,SAAS,IAAI,CAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;gCACxB,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;gCACrB,MAAM;4BACR;wBACF,OAAO,IAAI,KAAK,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM;4BACrD,SAAS,IAAI,CAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;gCACxB,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;gCACrB,SAAS;4BACX;wBACF,OAAO,IAAI,KAAK,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM;4BACrD,SAAS,IAAI,CAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;gCACxB,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;gCACrB,MAAM;4BACR;wBACF,OAAO,IAAI,MAAM;4BACf,SAAS,IAAI,CAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;gCAAE,MAAM;4BAAK;wBACzC;oBACF;oBAEA,iBAAiB,IAAI,CAAC,IAAI,sIAAA,CAAA,YAAS,CAAC;wBAClC,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;4BAAC,IAAI,sIAAA,CAAA,UAAO,CAAC;gCAAE,MAAM,QAAQ,OAAO;4BAAC;yBAAG;oBACrF;oBACA;YACJ;QACF;QAEA,MAAM,MAAM,IAAI,sIAAA,CAAA,WAAQ,CAAC;YACvB,UAAU;gBAAC;oBACT,YAAY,CAAC;oBACb,UAAU;gBACZ;aAAE;YACF,SAAS;YACT,OAAO,QAAQ,KAAK,IAAI;YACxB,aAAa;YACb,QAAQ;gBACN,iBAAiB;oBACf;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,KAAK;4BACH,MAAM;4BACN,MAAM,CAAC,QAAQ,QAAQ,IAAI,EAAE,IAAI,IAAI;wBACvC;wBACA,WAAW;4BACT,SAAS;gCACP,QAAQ;gCACR,OAAO;4BACT;wBACF;oBACF;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,KAAK;4BACH,SAAS;4BACT,OAAO;wBACT;wBACA,WAAW;4BACT,SAAS;gCACP,QAAQ;gCACR,OAAO;4BACT;4BACA,QAAQ;gCACN,MAAM;4BACR;wBACF;oBACF;iBACD;YACH;QACF;QAEA,MAAM,OAAO,MAAM,sIAAA,CAAA,SAAM,CAAC,MAAM,CAAC;QACjC,MAAM,WAAW,GAAG,QAAQ,KAAK,IAAI,WAAW,KAAK,CAAC;QACtD,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAEb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,eAAe,CAAC,SAAiB,aAAqB,UAAyB;IAAE,QAAQ;AAAO,CAAC;IAC5G,IAAI;QACF,MAAM,eAAe,CAAC;;;;;;WAMf,EAAE,QAAQ,KAAK,IAAI,WAAW;;;yBAGhB,EAAE,QAAQ,UAAU,IAAI,oBAAoB;uBAC9C,EAAE,QAAQ,QAAQ,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAgDzB,EAAE,QAAQ,QAAQ,IAAI,GAAG;yBACvB,EAAE,QAAQ,UAAU,IAAI,uCAAuC;;;;;;;;;;;;;mBAarE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;wBAGnD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,QAAQ,CAAC;;mBAE5D,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,WAAW,CAAC;;;uBAGtD,EAAE,CAAC,QAAQ,QAAQ,IAAI,EAAE,IAAI,EAAE;;;;qCAIjB,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,WAAW,CAAC;;;;8BAIjE,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,SAAS,CAAC;;;8BAGxD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,QAAQ,CAAC;;;8BAGvD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkDhF,EAAE,YAAY;;OAEX,CAAC;QAEJ,MAAM,OAAO,IAAI,KAAK;YAAC;SAAa,EAAE;YAAE,MAAM;QAA0B;QACxE,MAAM,WAAW,GAAG,QAAQ,KAAK,IAAI,WAAW,KAAK,CAAC;QACtD,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAEb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,eAAe,CAAC,SAAiB,UAAyB;IAAE,QAAQ;AAAM,CAAC;IACtF,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAA2B;QACpE,MAAM,WAAW,GAAG,QAAQ,KAAK,IAAI,WAAW,IAAI,CAAC;QACrD,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAEb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/TableThemePreview.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface TableThemePreviewProps {\r\n  theme: 'professional' | 'modern' | 'minimal' | 'colorful';\r\n  className?: string;\r\n}\r\n\r\nconst themeStyles = {\r\n  professional: {\r\n    headerBg: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',\r\n    headerColor: '#ffffff',\r\n    evenRowBg: '#f8f9fa',\r\n    oddRowBg: '#ffffff',\r\n    borderColor: '#bdc3c7',\r\n  },\r\n  modern: {\r\n    headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    headerColor: '#ffffff',\r\n    evenRowBg: '#f8fafc',\r\n    oddRowBg: '#ffffff',\r\n    borderColor: '#e1e5e9',\r\n  },\r\n  minimal: {\r\n    headerBg: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',\r\n    headerColor: '#ffffff',\r\n    evenRowBg: '#fdfdfd',\r\n    oddRowBg: '#ffffff',\r\n    borderColor: '#ecf0f1',\r\n  },\r\n  colorful: {\r\n    headerBg: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',\r\n    headerColor: '#ffffff',\r\n    evenRowBg: '#fff5f5',\r\n    oddRowBg: '#ffffff',\r\n    borderColor: '#fab1a0',\r\n  },\r\n};\r\n\r\nexport const TableThemePreview: React.FC<TableThemePreviewProps> = ({ \r\n  theme, \r\n  className = '' \r\n}) => {\r\n  const style = themeStyles[theme];\r\n  \r\n  return (\r\n    <div className={`overflow-hidden rounded-lg shadow-sm ${className}`}>\r\n      <table className=\"w-full text-xs border-collapse\">\r\n        <thead>\r\n          <tr>\r\n            <th \r\n              className=\"px-3 py-2 text-center font-semibold border\"\r\n              style={{ \r\n                background: style.headerBg,\r\n                color: style.headerColor,\r\n                borderColor: style.borderColor,\r\n              }}\r\n            >\r\n              姓名\r\n            </th>\r\n            <th \r\n              className=\"px-3 py-2 text-center font-semibold border\"\r\n              style={{ \r\n                background: style.headerBg,\r\n                color: style.headerColor,\r\n                borderColor: style.borderColor,\r\n              }}\r\n            >\r\n              职位\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr>\r\n            <td \r\n              className=\"px-3 py-1 border text-center\"\r\n              style={{ \r\n                backgroundColor: style.oddRowBg,\r\n                borderColor: style.borderColor,\r\n              }}\r\n            >\r\n              张三\r\n            </td>\r\n            <td \r\n              className=\"px-3 py-1 border text-center\"\r\n              style={{ \r\n                backgroundColor: style.oddRowBg,\r\n                borderColor: style.borderColor,\r\n              }}\r\n            >\r\n              工程师\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td \r\n              className=\"px-3 py-1 border text-center\"\r\n              style={{ \r\n                backgroundColor: style.evenRowBg,\r\n                borderColor: style.borderColor,\r\n              }}\r\n            >\r\n              李四\r\n            </td>\r\n            <td \r\n              className=\"px-3 py-1 border text-center\"\r\n              style={{ \r\n                backgroundColor: style.evenRowBg,\r\n                borderColor: style.borderColor,\r\n              }}\r\n            >\r\n              设计师\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AASA,MAAM,cAAc;IAClB,cAAc;QACZ,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACV,aAAa;IACf;IACA,QAAQ;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACV,aAAa;IACf;IACA,SAAS;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACV,aAAa;IACf;IACA,UAAU;QACR,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACV,aAAa;IACf;AACF;AAEO,MAAM,oBAAsD,CAAC,EAClE,KAAK,EACL,YAAY,EAAE,EACf;IACC,MAAM,QAAQ,WAAW,CAAC,MAAM;IAEhC,qBACE,8OAAC;QAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;kBACjE,cAAA,8OAAC;YAAM,WAAU;;8BACf,8OAAC;8BACC,cAAA,8OAAC;;0CACC,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY,MAAM,QAAQ;oCAC1B,OAAO,MAAM,WAAW;oCACxB,aAAa,MAAM,WAAW;gCAChC;0CACD;;;;;;0CAGD,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY,MAAM,QAAQ;oCAC1B,OAAO,MAAM,WAAW;oCACxB,aAAa,MAAM,WAAW;gCAChC;0CACD;;;;;;;;;;;;;;;;;8BAKL,8OAAC;;sCACC,8OAAC;;8CACC,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,MAAM,QAAQ;wCAC/B,aAAa,MAAM,WAAW;oCAChC;8CACD;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,MAAM,QAAQ;wCAC/B,aAAa,MAAM,WAAW;oCAChC;8CACD;;;;;;;;;;;;sCAIH,8OAAC;;8CACC,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,MAAM,SAAS;wCAChC,aAAa,MAAM,WAAW;oCAChC;8CACD;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,MAAM,SAAS;wCAChC,aAAa,MAAM,WAAW;oCAChC;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/clipboard.ts"], "sourcesContent": ["'use client';\r\n\r\n// 复制工具类\r\nexport class ClipboardUtils {\r\n  \r\n  // 将Markdown表格转换为HTML表格\r\n  static markdownTableToHTML(markdownContent: string, theme: string = 'modern'): string {\r\n    const lines = markdownContent.split('\\n');\r\n    let htmlContent = '';\r\n    let inTable = false;\r\n    let tableRows: string[][] = [];\r\n    \r\n    for (let i = 0; i < lines.length; i++) {\r\n      const line = lines[i].trim();\r\n      \r\n      // 检测表格开始\r\n      if (line.includes('|') && !inTable) {\r\n        // 检查下一行是否是分隔符\r\n        const nextLine = lines[i + 1]?.trim();\r\n        if (nextLine && nextLine.includes('|') && nextLine.includes('-')) {\r\n          inTable = true;\r\n          // 解析表头\r\n          const headerCells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');\r\n          tableRows.push(headerCells);\r\n          i++; // 跳过分隔符行\r\n          continue;\r\n        }\r\n      }\r\n      \r\n      // 在表格中\r\n      if (inTable && line.includes('|')) {\r\n        const rowCells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');\r\n        if (rowCells.length > 0) {\r\n          tableRows.push(rowCells);\r\n        }\r\n      } \r\n      // 表格结束\r\n      else if (inTable && !line.includes('|')) {\r\n        // 生成HTML表格\r\n        htmlContent += this.generateStyledTable(tableRows, theme);\r\n        tableRows = [];\r\n        inTable = false;\r\n        \r\n        // 处理非表格内容\r\n        if (line) {\r\n          htmlContent += this.processNonTableContent(line);\r\n        }\r\n      }\r\n      // 非表格内容\r\n      else if (!inTable && line) {\r\n        htmlContent += this.processNonTableContent(line);\r\n      }\r\n    }\r\n    \r\n    // 处理最后的表格\r\n    if (inTable && tableRows.length > 0) {\r\n      htmlContent += this.generateStyledTable(tableRows, theme);\r\n    }\r\n    \r\n    return htmlContent;\r\n  }\r\n  \r\n  // 生成带样式的HTML表格\r\n  static generateStyledTable(rows: string[][], theme: string): string {\r\n    if (rows.length === 0) return '';\r\n    \r\n    const themeStyles = this.getThemeStyles(theme);\r\n    \r\n    // 确保所有行都有相同的列数\r\n    const maxCols = Math.max(...rows.map(row => row.length));\r\n    const normalizedRows = rows.map(row => {\r\n      const newRow = [...row];\r\n      while (newRow.length < maxCols) {\r\n        newRow.push('');\r\n      }\r\n      return newRow;\r\n    });\r\n    \r\n    let html = `<table style=\"${themeStyles.tableStyle}\">`;\r\n    \r\n    // 表头\r\n    if (normalizedRows.length > 0) {\r\n      html += '<thead><tr>';\r\n      normalizedRows[0].forEach(cell => {\r\n        html += `<th style=\"${themeStyles.headerStyle}\">${this.formatCellContent(cell)}</th>`;\r\n      });\r\n      html += '</tr></thead>';\r\n    }\r\n    \r\n    // 表体\r\n    if (normalizedRows.length > 1) {\r\n      html += '<tbody>';\r\n      normalizedRows.slice(1).forEach((row, index) => {\r\n        const isEven = index % 2 === 0;\r\n        html += '<tr>';\r\n        row.forEach(cell => {\r\n          const cellStyle = isEven ? themeStyles.evenCellStyle : themeStyles.oddCellStyle;\r\n          html += `<td style=\"${cellStyle}\">${this.formatCellContent(cell)}</td>`;\r\n        });\r\n        html += '</tr>';\r\n      });\r\n      html += '</tbody>';\r\n    }\r\n    \r\n    html += '</table><br>';\r\n    return html;\r\n  }\r\n  \r\n  // 获取主题样式\r\n  static getThemeStyles(theme: string) {\r\n    const themes = {\r\n      professional: {\r\n        headerBg: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',\r\n        headerColor: '#ffffff',\r\n        evenRowBg: '#f7fafc',\r\n        oddRowBg: '#ffffff',\r\n        textColor: '#2d3748',\r\n        borderColor: '#cbd5e0',\r\n      },\r\n      modern: {\r\n        headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        headerColor: '#ffffff',\r\n        evenRowBg: '#f8fafc',\r\n        oddRowBg: '#ffffff',\r\n        textColor: '#2d3748',\r\n        borderColor: '#e2e8f0',\r\n      },\r\n      minimal: {\r\n        headerBg: 'linear-gradient(135deg, #a0aec0 0%, #718096 100%)',\r\n        headerColor: '#ffffff',\r\n        evenRowBg: '#f9fafb',\r\n        oddRowBg: '#ffffff',\r\n        textColor: '#374151',\r\n        borderColor: '#e2e8f0',\r\n      },\r\n      colorful: {\r\n        headerBg: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',\r\n        headerColor: '#ffffff',\r\n        evenRowBg: '#fef5e7',\r\n        oddRowBg: '#ffffff',\r\n        textColor: '#2d3748',\r\n        borderColor: '#fed7d7',\r\n      },\r\n    };\r\n    \r\n    const selectedTheme = themes[theme as keyof typeof themes] || themes.modern;\r\n    \r\n    return {\r\n      tableStyle: `\r\n        border-collapse: collapse;\r\n        width: 100%;\r\n        margin: 16px 0;\r\n        font-family: 'Microsoft YaHei', Arial, sans-serif;\r\n        font-size: 14px;\r\n        border: 2px solid ${selectedTheme.borderColor};\r\n      `,\r\n      headerStyle: `\r\n        background: ${selectedTheme.headerBg};\r\n        color: ${selectedTheme.headerColor};\r\n        padding: 12px 16px;\r\n        text-align: center;\r\n        font-weight: bold;\r\n        border: 1px solid ${selectedTheme.borderColor};\r\n        font-size: 15px;\r\n      `,\r\n      evenCellStyle: `\r\n        background-color: ${selectedTheme.evenRowBg};\r\n        color: ${selectedTheme.textColor};\r\n        padding: 10px 16px;\r\n        border: 1px solid ${selectedTheme.borderColor};\r\n        text-align: left;\r\n        vertical-align: top;\r\n      `,\r\n      oddCellStyle: `\r\n        background-color: ${selectedTheme.oddRowBg};\r\n        color: ${selectedTheme.textColor};\r\n        padding: 10px 16px;\r\n        border: 1px solid ${selectedTheme.borderColor};\r\n        text-align: left;\r\n        vertical-align: top;\r\n      `,\r\n    };\r\n  }\r\n  \r\n  // 处理非表格内容\r\n  static processNonTableContent(line: string): string {\r\n    // 标题\r\n    if (line.startsWith('# ')) {\r\n      return `<h1 style=\"color: #2d3748; font-size: 24px; font-weight: bold; margin: 20px 0 10px 0;\">${line.substring(2)}</h1>`;\r\n    } else if (line.startsWith('## ')) {\r\n      return `<h2 style=\"color: #2d3748; font-size: 20px; font-weight: bold; margin: 16px 0 8px 0;\">${line.substring(3)}</h2>`;\r\n    } else if (line.startsWith('### ')) {\r\n      return `<h3 style=\"color: #2d3748; font-size: 18px; font-weight: bold; margin: 14px 0 6px 0;\">${line.substring(4)}</h3>`;\r\n    }\r\n    \r\n    // 普通段落\r\n    return `<p style=\"color: #2d3748; line-height: 1.6; margin: 8px 0;\">${this.formatCellContent(line)}</p>`;\r\n  }\r\n  \r\n  // 格式化单元格内容\r\n  static formatCellContent(content: string): string {\r\n    return content\r\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n      .replace(/`(.*?)`/g, '<code style=\"background-color: #f1f5f9; padding: 2px 4px; border-radius: 3px; font-family: monospace;\">$1</code>');\r\n  }\r\n  \r\n  // 复制到剪贴板\r\n  static async copyToClipboard(markdownContent: string, theme: string = 'modern'): Promise<boolean> {\r\n    try {\r\n      const htmlContent = this.markdownTableToHTML(markdownContent, theme);\r\n      \r\n      // 创建ClipboardItem\r\n      const clipboardItem = new ClipboardItem({\r\n        'text/html': new Blob([htmlContent], { type: 'text/html' }),\r\n        'text/plain': new Blob([markdownContent], { type: 'text/plain' })\r\n      });\r\n      \r\n      await navigator.clipboard.write([clipboardItem]);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('复制失败:', error);\r\n      \r\n      // 降级方案：复制纯文本\r\n      try {\r\n        await navigator.clipboard.writeText(markdownContent);\r\n        return true;\r\n      } catch (fallbackError) {\r\n        console.error('降级复制也失败:', fallbackError);\r\n        return false;\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 检查剪贴板API支持\r\n  static isClipboardSupported(): boolean {\r\n    return typeof navigator !== 'undefined' && \r\n           'clipboard' in navigator && \r\n           'write' in navigator.clipboard;\r\n  }\r\n  \r\n  // 复制选中的表格\r\n  static async copySelectedTable(tableElement: HTMLTableElement, theme: string = 'modern'): Promise<boolean> {\r\n    try {\r\n      const themeStyles = this.getThemeStyles(theme);\r\n      \r\n      // 克隆表格并应用样式\r\n      const clonedTable = tableElement.cloneNode(true) as HTMLTableElement;\r\n      clonedTable.style.cssText = themeStyles.tableStyle;\r\n      \r\n      // 应用表头样式\r\n      const headers = clonedTable.querySelectorAll('th');\r\n      headers.forEach(th => {\r\n        th.style.cssText = themeStyles.headerStyle;\r\n      });\r\n      \r\n      // 应用单元格样式\r\n      const rows = clonedTable.querySelectorAll('tbody tr');\r\n      rows.forEach((row, index) => {\r\n        const cells = row.querySelectorAll('td');\r\n        const isEven = index % 2 === 0;\r\n        const cellStyle = isEven ? themeStyles.evenCellStyle : themeStyles.oddCellStyle;\r\n        \r\n        cells.forEach(cell => {\r\n          (cell as HTMLElement).style.cssText = cellStyle;\r\n        });\r\n      });\r\n      \r\n      const htmlContent = clonedTable.outerHTML;\r\n      \r\n      const clipboardItem = new ClipboardItem({\r\n        'text/html': new Blob([htmlContent], { type: 'text/html' })\r\n      });\r\n      \r\n      await navigator.clipboard.write([clipboardItem]);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('复制表格失败:', error);\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAGO,MAAM;IAEX,uBAAuB;IACvB,OAAO,oBAAoB,eAAuB,EAAE,QAAgB,QAAQ,EAAU;QACpF,MAAM,QAAQ,gBAAgB,KAAK,CAAC;QACpC,IAAI,cAAc;QAClB,IAAI,UAAU;QACd,IAAI,YAAwB,EAAE;QAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YAE1B,SAAS;YACT,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,SAAS;gBAClC,cAAc;gBACd,MAAM,WAAW,KAAK,CAAC,IAAI,EAAE,EAAE;gBAC/B,IAAI,YAAY,SAAS,QAAQ,CAAC,QAAQ,SAAS,QAAQ,CAAC,MAAM;oBAChE,UAAU;oBACV,OAAO;oBACP,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,SAAS;oBACrF,UAAU,IAAI,CAAC;oBACf,KAAK,SAAS;oBACd;gBACF;YACF;YAEA,OAAO;YACP,IAAI,WAAW,KAAK,QAAQ,CAAC,MAAM;gBACjC,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,SAAS;gBAClF,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,UAAU,IAAI,CAAC;gBACjB;YACF,OAEK,IAAI,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM;gBACvC,WAAW;gBACX,eAAe,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBACnD,YAAY,EAAE;gBACd,UAAU;gBAEV,UAAU;gBACV,IAAI,MAAM;oBACR,eAAe,IAAI,CAAC,sBAAsB,CAAC;gBAC7C;YACF,OAEK,IAAI,CAAC,WAAW,MAAM;gBACzB,eAAe,IAAI,CAAC,sBAAsB,CAAC;YAC7C;QACF;QAEA,UAAU;QACV,IAAI,WAAW,UAAU,MAAM,GAAG,GAAG;YACnC,eAAe,IAAI,CAAC,mBAAmB,CAAC,WAAW;QACrD;QAEA,OAAO;IACT;IAEA,eAAe;IACf,OAAO,oBAAoB,IAAgB,EAAE,KAAa,EAAU;QAClE,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC;QAExC,eAAe;QACf,MAAM,UAAU,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;QACtD,MAAM,iBAAiB,KAAK,GAAG,CAAC,CAAA;YAC9B,MAAM,SAAS;mBAAI;aAAI;YACvB,MAAO,OAAO,MAAM,GAAG,QAAS;gBAC9B,OAAO,IAAI,CAAC;YACd;YACA,OAAO;QACT;QAEA,IAAI,OAAO,CAAC,cAAc,EAAE,YAAY,UAAU,CAAC,EAAE,CAAC;QAEtD,KAAK;QACL,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ;YACR,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;gBACxB,QAAQ,CAAC,WAAW,EAAE,YAAY,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC;YACvF;YACA,QAAQ;QACV;QAEA,KAAK;QACL,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ;YACR,eAAe,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,KAAK;gBACpC,MAAM,SAAS,QAAQ,MAAM;gBAC7B,QAAQ;gBACR,IAAI,OAAO,CAAC,CAAA;oBACV,MAAM,YAAY,SAAS,YAAY,aAAa,GAAG,YAAY,YAAY;oBAC/E,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC;gBACzE;gBACA,QAAQ;YACV;YACA,QAAQ;QACV;QAEA,QAAQ;QACR,OAAO;IACT;IAEA,SAAS;IACT,OAAO,eAAe,KAAa,EAAE;QACnC,MAAM,SAAS;YACb,cAAc;gBACZ,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,aAAa;YACf;YACA,QAAQ;gBACN,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,aAAa;YACf;YACA,SAAS;gBACP,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,aAAa;YACf;YACA,UAAU;gBACR,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,aAAa;YACf;QACF;QAEA,MAAM,gBAAgB,MAAM,CAAC,MAA6B,IAAI,OAAO,MAAM;QAE3E,OAAO;YACL,YAAY,CAAC;;;;;;0BAMO,EAAE,cAAc,WAAW,CAAC;MAChD,CAAC;YACD,aAAa,CAAC;oBACA,EAAE,cAAc,QAAQ,CAAC;eAC9B,EAAE,cAAc,WAAW,CAAC;;;;0BAIjB,EAAE,cAAc,WAAW,CAAC;;MAEhD,CAAC;YACD,eAAe,CAAC;0BACI,EAAE,cAAc,SAAS,CAAC;eACrC,EAAE,cAAc,SAAS,CAAC;;0BAEf,EAAE,cAAc,WAAW,CAAC;;;MAGhD,CAAC;YACD,cAAc,CAAC;0BACK,EAAE,cAAc,QAAQ,CAAC;eACpC,EAAE,cAAc,SAAS,CAAC;;0BAEf,EAAE,cAAc,WAAW,CAAC;;;MAGhD,CAAC;QACH;IACF;IAEA,UAAU;IACV,OAAO,uBAAuB,IAAY,EAAU;QAClD,KAAK;QACL,IAAI,KAAK,UAAU,CAAC,OAAO;YACzB,OAAO,CAAC,uFAAuF,EAAE,KAAK,SAAS,CAAC,GAAG,KAAK,CAAC;QAC3H,OAAO,IAAI,KAAK,UAAU,CAAC,QAAQ;YACjC,OAAO,CAAC,sFAAsF,EAAE,KAAK,SAAS,CAAC,GAAG,KAAK,CAAC;QAC1H,OAAO,IAAI,KAAK,UAAU,CAAC,SAAS;YAClC,OAAO,CAAC,sFAAsF,EAAE,KAAK,SAAS,CAAC,GAAG,KAAK,CAAC;QAC1H;QAEA,OAAO;QACP,OAAO,CAAC,4DAA4D,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC;IAC1G;IAEA,WAAW;IACX,OAAO,kBAAkB,OAAe,EAAU;QAChD,OAAO,QACJ,OAAO,CAAC,kBAAkB,uBAC1B,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,YAAY;IACzB;IAEA,SAAS;IACT,aAAa,gBAAgB,eAAuB,EAAE,QAAgB,QAAQ,EAAoB;QAChG,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;YAE9D,kBAAkB;YAClB,MAAM,gBAAgB,IAAI,cAAc;gBACtC,aAAa,IAAI,KAAK;oBAAC;iBAAY,EAAE;oBAAE,MAAM;gBAAY;gBACzD,cAAc,IAAI,KAAK;oBAAC;iBAAgB,EAAE;oBAAE,MAAM;gBAAa;YACjE;YAEA,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;gBAAC;aAAc;YAC/C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YAEvB,aAAa;YACb,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,OAAO;YACT,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,YAAY;gBAC1B,OAAO;YACT;QACF;IACF;IAEA,aAAa;IACb,OAAO,uBAAgC;QACrC,OAAO,OAAO,cAAc,eACrB,eAAe,aACf,WAAW,UAAU,SAAS;IACvC;IAEA,UAAU;IACV,aAAa,kBAAkB,YAA8B,EAAE,QAAgB,QAAQ,EAAoB;QACzG,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC;YAExC,YAAY;YACZ,MAAM,cAAc,aAAa,SAAS,CAAC;YAC3C,YAAY,KAAK,CAAC,OAAO,GAAG,YAAY,UAAU;YAElD,SAAS;YACT,MAAM,UAAU,YAAY,gBAAgB,CAAC;YAC7C,QAAQ,OAAO,CAAC,CAAA;gBACd,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY,WAAW;YAC5C;YAEA,UAAU;YACV,MAAM,OAAO,YAAY,gBAAgB,CAAC;YAC1C,KAAK,OAAO,CAAC,CAAC,KAAK;gBACjB,MAAM,QAAQ,IAAI,gBAAgB,CAAC;gBACnC,MAAM,SAAS,QAAQ,MAAM;gBAC7B,MAAM,YAAY,SAAS,YAAY,aAAa,GAAG,YAAY,YAAY;gBAE/E,MAAM,OAAO,CAAC,CAAA;oBACX,KAAqB,KAAK,CAAC,OAAO,GAAG;gBACxC;YACF;YAEA,MAAM,cAAc,YAAY,SAAS;YAEzC,MAAM,gBAAgB,IAAI,cAAc;gBACtC,aAAa,IAAI,KAAK;oBAAC;iBAAY,EAAE;oBAAE,MAAM;gBAAY;YAC3D;YAEA,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;gBAAC;aAAc;YAC/C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/CopyButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ClipboardUtils } from '@/utils/clipboard';\r\n\r\ninterface CopyButtonProps {\r\n  content: string;\r\n  theme?: 'professional' | 'modern' | 'minimal' | 'colorful';\r\n  className?: string;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  variant?: 'primary' | 'secondary' | 'outline';\r\n}\r\n\r\nexport const CopyButton: React.FC<CopyButtonProps> = ({\r\n  content,\r\n  theme = 'modern',\r\n  className = '',\r\n  size = 'md',\r\n  variant = 'primary'\r\n}) => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [copyStatus, setCopyStatus] = useState<'idle' | 'success' | 'error'>('idle');\r\n  const [isSupported, setIsSupported] = useState(false);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  // 只在客户端检查剪贴板支持\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n    setIsSupported(ClipboardUtils.isClipboardSupported());\r\n  }, []);\r\n\r\n  const handleCopy = async () => {\r\n    if (!content.trim()) {\r\n      setCopyStatus('error');\r\n      setTimeout(() => setCopyStatus('idle'), 2000);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setCopyStatus('idle');\r\n\r\n    try {\r\n      const success = await ClipboardUtils.copyToClipboard(content, theme);\r\n      \r\n      if (success) {\r\n        setCopyStatus('success');\r\n        setTimeout(() => setCopyStatus('idle'), 3000);\r\n      } else {\r\n        setCopyStatus('error');\r\n        setTimeout(() => setCopyStatus('idle'), 2000);\r\n      }\r\n    } catch (error) {\r\n      console.error('复制失败:', error);\r\n      setCopyStatus('error');\r\n      setTimeout(() => setCopyStatus('idle'), 2000);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 尺寸样式\r\n  const sizeClasses = {\r\n    sm: 'px-3 py-1.5 text-sm',\r\n    md: 'px-4 py-2 text-sm',\r\n    lg: 'px-6 py-3 text-base'\r\n  };\r\n\r\n  // 变体样式\r\n  const variantClasses = {\r\n    primary: 'bg-blue-500 hover:bg-blue-600 text-white',\r\n    secondary: 'bg-gray-500 hover:bg-gray-600 text-white',\r\n    outline: 'border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white bg-transparent'\r\n  };\r\n\r\n  // 状态样式\r\n  const getStatusClasses = () => {\r\n    switch (copyStatus) {\r\n      case 'success':\r\n        return 'bg-green-500 hover:bg-green-600 text-white';\r\n      case 'error':\r\n        return 'bg-red-500 hover:bg-red-600 text-white';\r\n      default:\r\n        return variantClasses[variant];\r\n    }\r\n  };\r\n\r\n  // 图标\r\n  const getIcon = () => {\r\n    if (isLoading) {\r\n      return (\r\n        <svg className=\"w-4 h-4 animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n        </svg>\r\n      );\r\n    }\r\n\r\n    switch (copyStatus) {\r\n      case 'success':\r\n        return (\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n          </svg>\r\n        );\r\n      case 'error':\r\n        return (\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n          </svg>\r\n        );\r\n      default:\r\n        return (\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\r\n          </svg>\r\n        );\r\n    }\r\n  };\r\n\r\n  // 按钮文字\r\n  const getButtonText = () => {\r\n    if (isLoading) return '复制中...';\r\n    \r\n    switch (copyStatus) {\r\n      case 'success':\r\n        return '复制成功！';\r\n      case 'error':\r\n        return '复制失败';\r\n      default:\r\n        return '复制到Word';\r\n    }\r\n  };\r\n\r\n  // 在服务器端渲染时显示加载状态，避免hydration不匹配\r\n  if (!isMounted) {\r\n    return (\r\n      <button\r\n        disabled\r\n        className={`\r\n          inline-flex items-center gap-2 rounded-md font-medium transition-all duration-200\r\n          ${sizeClasses[size]}\r\n          ${variantClasses[variant]}\r\n          cursor-not-allowed opacity-70\r\n          ${className}\r\n        `}\r\n      >\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\r\n        </svg>\r\n        <span>复制到Word</span>\r\n      </button>\r\n    );\r\n  }\r\n\r\n  if (!isSupported) {\r\n    return (\r\n      <div className={`inline-flex items-center gap-2 px-4 py-2 text-sm text-gray-500 bg-gray-100 rounded-md cursor-not-allowed ${className}`}>\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728\" />\r\n        </svg>\r\n        浏览器不支持\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleCopy}\r\n      disabled={isLoading}\r\n      className={`\r\n        inline-flex items-center gap-2 rounded-md font-medium transition-all duration-200\r\n        ${sizeClasses[size]}\r\n        ${getStatusClasses()}\r\n        ${isLoading ? 'cursor-not-allowed opacity-70' : 'hover:shadow-md active:scale-95'}\r\n        ${className}\r\n      `}\r\n      title=\"复制带样式的内容到Word\"\r\n    >\r\n      {getIcon()}\r\n      <span>{getButtonText()}</span>\r\n    </button>\r\n  );\r\n};\r\n\r\n// 快速复制组件 - 用于工具栏\r\nexport const QuickCopyButton: React.FC<{\r\n  content: string;\r\n  theme?: string;\r\n  className?: string;\r\n}> = ({ content, theme = 'modern', className = '' }) => {\r\n  const [copied, setCopied] = useState(false);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  const handleQuickCopy = async () => {\r\n    if (!isMounted) return;\r\n\r\n    try {\r\n      const success = await ClipboardUtils.copyToClipboard(content, theme);\r\n      if (success) {\r\n        setCopied(true);\r\n        setTimeout(() => setCopied(false), 2000);\r\n      }\r\n    } catch (error) {\r\n      console.error('快速复制失败:', error);\r\n    }\r\n  };\r\n\r\n  // 在服务器端渲染时显示默认状态\r\n  if (!isMounted) {\r\n    return (\r\n      <button\r\n        disabled\r\n        className={`\r\n          p-2 rounded-md transition-colors\r\n          bg-gray-100 text-gray-600 cursor-not-allowed opacity-70\r\n          ${className}\r\n        `}\r\n        title=\"复制到Word\"\r\n      >\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\r\n        </svg>\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleQuickCopy}\r\n      className={`\r\n        p-2 rounded-md transition-colors\r\n        ${copied\r\n          ? 'bg-green-100 text-green-600 hover:bg-green-200'\r\n          : 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800'\r\n        }\r\n        ${className}\r\n      `}\r\n      title={copied ? '已复制！' : '复制到Word'}\r\n    >\r\n      {copied ? (\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n        </svg>\r\n      ) : (\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\r\n        </svg>\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAaO,MAAM,aAAwC,CAAC,EACpD,OAAO,EACP,QAAQ,QAAQ,EAChB,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACpB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,eAAe,yHAAA,CAAA,iBAAc,CAAC,oBAAoB;IACpD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,cAAc;YACd,WAAW,IAAM,cAAc,SAAS;YACxC;QACF;QAEA,aAAa;QACb,cAAc;QAEd,IAAI;YACF,MAAM,UAAU,MAAM,yHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,SAAS;YAE9D,IAAI,SAAS;gBACX,cAAc;gBACd,WAAW,IAAM,cAAc,SAAS;YAC1C,OAAO;gBACL,cAAc;gBACd,WAAW,IAAM,cAAc,SAAS;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,cAAc;YACd,WAAW,IAAM,cAAc,SAAS;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,cAAc,CAAC,QAAQ;QAClC;IACF;IAEA,KAAK;IACL,MAAM,UAAU;QACd,IAAI,WAAW;YACb,qBACE,8OAAC;gBAAI,WAAU;gBAAuB,MAAK;gBAAO,SAAQ;;kCACxD,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;QAGzD;QAEA,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,qBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAG7E;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,IAAI,WAAW,OAAO;QAEtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,gCAAgC;IAChC,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YACC,QAAQ;YACR,WAAW,CAAC;;UAEV,EAAE,WAAW,CAAC,KAAK,CAAC;UACpB,EAAE,cAAc,CAAC,QAAQ,CAAC;;UAE1B,EAAE,UAAU;QACd,CAAC;;8BAED,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;8BAEvE,8OAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAW,CAAC,yGAAyG,EAAE,WAAW;;8BACrI,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBACjE;;;;;;;IAIZ;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,CAAC;;QAEV,EAAE,WAAW,CAAC,KAAK,CAAC;QACpB,EAAE,mBAAmB;QACrB,EAAE,YAAY,kCAAkC,kCAAkC;QAClF,EAAE,UAAU;MACd,CAAC;QACD,OAAM;;YAEL;0BACD,8OAAC;0BAAM;;;;;;;;;;;;AAGb;AAGO,MAAM,kBAIR,CAAC,EAAE,OAAO,EAAE,QAAQ,QAAQ,EAAE,YAAY,EAAE,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,UAAU,MAAM,yHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,SAAS;YAC9D,IAAI,SAAS;gBACX,UAAU;gBACV,WAAW,IAAM,UAAU,QAAQ;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,iBAAiB;IACjB,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YACC,QAAQ;YACR,WAAW,CAAC;;;UAGV,EAAE,UAAU;QACd,CAAC;YACD,OAAM;sBAEN,cAAA,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;;;;;IAI7E;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,CAAC;;QAEV,EAAE,SACE,mDACA,kEACH;QACD,EAAE,UAAU;MACd,CAAC;QACD,OAAO,SAAS,SAAS;kBAExB,uBACC,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;qEAGvE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;;;;;;AAK/E", "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/ExportDialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { ExportOptions, exportToPDF, exportToWord, exportToHTML, exportToText } from '@/utils/export';\r\nimport { TableThemePreview } from './TableThemePreview';\r\nimport { CopyButton } from './CopyButton';\r\n\r\ninterface ExportDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  content: string;\r\n  htmlContent: string;\r\n}\r\n\r\nexport const ExportDialog: React.FC<ExportDialogProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  content,\r\n  htmlContent,\r\n}) => {\r\n  const [format, setFormat] = useState<'pdf' | 'docx' | 'html' | 'txt'>('pdf');\r\n  const [title, setTitle] = useState('');\r\n  const [author, setAuthor] = useState('');\r\n  const [pageSize, setPageSize] = useState<'A4' | 'Letter' | 'A3'>('A4');\r\n  const [fontSize, setFontSize] = useState(14);\r\n  const [fontFamily, setFontFamily] = useState('Arial, sans-serif');\r\n  const [includeStyles, setIncludeStyles] = useState(true);\r\n  const [tableTheme, setTableTheme] = useState<'professional' | 'modern' | 'minimal' | 'colorful'>('modern');\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  const handleExport = async () => {\r\n    if (isExporting) return;\r\n\r\n    setIsExporting(true);\r\n    \r\n    const options: ExportOptions = {\r\n      format,\r\n      title: title || 'Untitled Document',\r\n      author,\r\n      pageSize,\r\n      fontSize,\r\n      fontFamily,\r\n      includeStyles,\r\n      tableTheme,\r\n    };\r\n\r\n    try {\r\n      switch (format) {\r\n        case 'pdf':\r\n          await exportToPDF(content, htmlContent, options);\r\n          break;\r\n        case 'docx':\r\n          await exportToWord(content, options);\r\n          break;\r\n        case 'html':\r\n          exportToHTML(content, htmlContent, options);\r\n          break;\r\n        case 'txt':\r\n          exportToText(content, options);\r\n          break;\r\n      }\r\n      \r\n      onClose();\r\n    } catch (error) {\r\n      alert(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-96 max-w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            导出文档\r\n          </h3>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n          >\r\n            ×\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          {/* 格式选择 */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              导出格式\r\n            </label>\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n              {[\r\n                { value: 'pdf', label: 'PDF', icon: '📄' },\r\n                { value: 'docx', label: 'Word', icon: '📝' },\r\n                { value: 'html', label: 'HTML', icon: '🌐' },\r\n                { value: 'txt', label: '纯文本', icon: '📋' },\r\n              ].map((option) => (\r\n                <button\r\n                  key={option.value}\r\n                  onClick={() => setFormat(option.value as any)}\r\n                  className={`p-3 rounded-lg border-2 transition-colors ${\r\n                    format === option.value\r\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\r\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\r\n                  }`}\r\n                >\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl mb-1\">{option.icon}</div>\r\n                    <div className=\"text-sm font-medium\">{option.label}</div>\r\n                  </div>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 文档信息 */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              文档标题\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={title}\r\n              onChange={(e) => setTitle(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n              placeholder=\"输入文档标题...\"\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              作者\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={author}\r\n              onChange={(e) => setAuthor(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n              placeholder=\"输入作者名称...\"\r\n            />\r\n          </div>\r\n\r\n          {/* PDF/Word 特定设置 */}\r\n          {(format === 'pdf' || format === 'docx') && (\r\n            <>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  页面大小\r\n                </label>\r\n                <select\r\n                  value={pageSize}\r\n                  onChange={(e) => setPageSize(e.target.value as any)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                >\r\n                  <option value=\"A4\">A4</option>\r\n                  <option value=\"Letter\">Letter</option>\r\n                  <option value=\"A3\">A3</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  字体大小\r\n                </label>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"10\"\r\n                    max=\"24\"\r\n                    value={fontSize}\r\n                    onChange={(e) => setFontSize(Number(e.target.value))}\r\n                    className=\"flex-1\"\r\n                  />\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400 min-w-[3rem]\">\r\n                    {fontSize}px\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  字体\r\n                </label>\r\n                <select\r\n                  value={fontFamily}\r\n                  onChange={(e) => setFontFamily(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                >\r\n                  <option value=\"Arial, sans-serif\">Arial</option>\r\n                  <option value=\"'Times New Roman', serif\">Times New Roman</option>\r\n                  <option value=\"'Courier New', monospace\">Courier New</option>\r\n                  <option value=\"'Microsoft YaHei', sans-serif\">微软雅黑</option>\r\n                  <option value=\"'SimSun', serif\">宋体</option>\r\n                </select>\r\n              </div>\r\n\r\n              {/* 表格主题选择 */}\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  表格主题\r\n                </label>\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  {[\r\n                    { value: 'professional', label: '专业', color: '#2c3e50', desc: '商务风格' },\r\n                    { value: 'modern', label: '现代', color: '#667eea', desc: '时尚渐变' },\r\n                    { value: 'minimal', label: '简约', color: '#95a5a6', desc: '简洁清爽' },\r\n                    { value: 'colorful', label: '彩色', color: '#ff6b6b', desc: '活泼鲜艳' },\r\n                  ].map((theme) => (\r\n                    <button\r\n                      key={theme.value}\r\n                      onClick={() => setTableTheme(theme.value as any)}\r\n                      className={`p-3 rounded-lg border-2 transition-colors ${\r\n                        tableTheme === theme.value\r\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\r\n                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\r\n                      }`}\r\n                    >\r\n                      <div className=\"text-center\">\r\n                        <TableThemePreview\r\n                          theme={theme.value as any}\r\n                          className=\"mb-2\"\r\n                        />\r\n                        <div className=\"text-sm font-medium\">{theme.label}</div>\r\n                        <div className=\"text-xs text-gray-500\">{theme.desc}</div>\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* 样式选项 */}\r\n          {(format === 'pdf' || format === 'html') && (\r\n            <div>\r\n              <label className=\"flex items-center\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={includeStyles}\r\n                  onChange={(e) => setIncludeStyles(e.target.checked)}\r\n                  className=\"mr-2\"\r\n                />\r\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">包含样式</span>\r\n              </label>\r\n            </div>\r\n          )}\r\n\r\n          {/* 预览信息 */}\r\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-3 rounded-md\">\r\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              <div>字符数: {content.length}</div>\r\n              <div>单词数: {content.trim() ? content.trim().split(/\\s+/).length : 0}</div>\r\n              <div>行数: {content.split('\\n').length}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <div className=\"flex flex-col gap-3 mt-6\">\r\n          {/* 复制到Word按钮 */}\r\n          <CopyButton\r\n            content={content}\r\n            theme={tableTheme}\r\n            size=\"md\"\r\n            variant=\"outline\"\r\n            className=\"w-full border-green-500 text-green-600 hover:bg-green-500 hover:text-white\"\r\n          />\r\n\r\n          <div className=\"flex gap-3\">\r\n            <button\r\n              onClick={onClose}\r\n              className=\"flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors\"\r\n            >\r\n              取消\r\n            </button>\r\n            <button\r\n              onClick={handleExport}\r\n              disabled={isExporting}\r\n              className=\"flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {isExporting ? '导出中...' : '导出文件'}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcO,MAAM,eAA4C,CAAC,EACxD,MAAM,EACN,OAAO,EACP,OAAO,EACP,WAAW,EACZ;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IACjG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,IAAI,aAAa;QAEjB,eAAe;QAEf,MAAM,UAAyB;YAC7B;YACA,OAAO,SAAS;YAChB;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,aAAa;oBACxC;gBACF,KAAK;oBACH,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,SAAS;oBAC5B;gBACF,KAAK;oBACH,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,aAAa;oBACnC;gBACF,KAAK;oBACH,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,SAAS;oBACtB;YACJ;YAEA;QACF,EAAE,OAAO,OAAO;YACd,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,eAAe;QACjB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAO,OAAO;4CAAO,MAAM;wCAAK;wCACzC;4CAAE,OAAO;4CAAQ,OAAO;4CAAQ,MAAM;wCAAK;wCAC3C;4CAAE,OAAO;4CAAQ,OAAO;4CAAQ,MAAM;wCAAK;wCAC3C;4CAAE,OAAO;4CAAO,OAAO;4CAAO,MAAM;wCAAK;qCAC1C,CAAC,GAAG,CAAC,CAAC,uBACL,8OAAC;4CAEC,SAAS,IAAM,UAAU,OAAO,KAAK;4CACrC,WAAW,CAAC,0CAA0C,EACpD,WAAW,OAAO,KAAK,GACnB,mDACA,yFACJ;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,OAAO,IAAI;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;kEAAuB,OAAO,KAAK;;;;;;;;;;;;2CAV/C,OAAO,KAAK;;;;;;;;;;;;;;;;sCAkBzB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,CAAC,WAAW,SAAS,WAAW,MAAM,mBACrC;;8CACE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;;8CAIvB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;oDAClD,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;;wDACb;wDAAS;;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,8OAAC;oDAAO,OAAM;8DAA2B;;;;;;8DACzC,8OAAC;oDAAO,OAAM;8DAA2B;;;;;;8DACzC,8OAAC;oDAAO,OAAM;8DAAgC;;;;;;8DAC9C,8OAAC;oDAAO,OAAM;8DAAkB;;;;;;;;;;;;;;;;;;8CAKpC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,OAAO;oDAAgB,OAAO;oDAAM,OAAO;oDAAW,MAAM;gDAAO;gDACrE;oDAAE,OAAO;oDAAU,OAAO;oDAAM,OAAO;oDAAW,MAAM;gDAAO;gDAC/D;oDAAE,OAAO;oDAAW,OAAO;oDAAM,OAAO;oDAAW,MAAM;gDAAO;gDAChE;oDAAE,OAAO;oDAAY,OAAO;oDAAM,OAAO;oDAAW,MAAM;gDAAO;6CAClE,CAAC,GAAG,CAAC,CAAC,sBACL,8OAAC;oDAEC,SAAS,IAAM,cAAc,MAAM,KAAK;oDACxC,WAAW,CAAC,0CAA0C,EACpD,eAAe,MAAM,KAAK,GACtB,mDACA,yFACJ;8DAEF,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6IAAA,CAAA,oBAAiB;gEAChB,OAAO,MAAM,KAAK;gEAClB,WAAU;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;0EAAuB,MAAM,KAAK;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EAAyB,MAAM,IAAI;;;;;;;;;;;;mDAd/C,MAAM,KAAK;;;;;;;;;;;;;;;;;;wBAwB3B,CAAC,WAAW,SAAS,WAAW,MAAM,mBACrC,8OAAC;sCACC,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;wCAClD,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAMjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;4CAAM,QAAQ,MAAM;;;;;;;kDACzB,8OAAC;;4CAAI;4CAAM,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,GAAG;;;;;;;kDACjE,8OAAC;;4CAAI;4CAAK,QAAQ,KAAK,CAAC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8BAM1C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,sIAAA,CAAA,aAAU;4BACT,SAAS;4BACT,OAAO;4BACP,MAAK;4BACL,SAAQ;4BACR,WAAU;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2930, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/ai.ts"], "sourcesContent": ["import OpenAI from 'openai';\r\n\r\nexport interface AIConfig {\r\n  apiKey: string;\r\n  baseURL?: string;\r\n  model?: string;\r\n  temperature?: number;\r\n  maxTokens?: number;\r\n}\r\n\r\nexport interface AIRequest {\r\n  type: 'grammar' | 'style' | 'expand' | 'summarize' | 'translate' | 'toc' | 'format';\r\n  content: string;\r\n  options?: {\r\n    targetLanguage?: string;\r\n    style?: string;\r\n    tone?: string;\r\n    length?: 'shorter' | 'longer' | 'same';\r\n  };\r\n}\r\n\r\nexport interface AIResponse {\r\n  success: boolean;\r\n  result?: string;\r\n  error?: string;\r\n  suggestions?: string[];\r\n}\r\n\r\nclass AIService {\r\n  private client: OpenAI | null = null;\r\n  private config: AIConfig | null = null;\r\n\r\n  // 初始化AI服务\r\n  initialize(config: AIConfig) {\r\n    try {\r\n      this.config = config;\r\n      this.client = new OpenAI({\r\n        apiKey: config.apiKey,\r\n        baseURL: config.baseURL,\r\n        dangerouslyAllowBrowser: true, // 注意：生产环境中应该通过后端代理\r\n      });\r\n      return true;\r\n    } catch (error) {\r\n      console.error('AI service initialization failed:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // 检查是否已初始化\r\n  isInitialized(): boolean {\r\n    return this.client !== null && this.config !== null;\r\n  }\r\n\r\n  // 通用AI请求方法\r\n  async makeRequest(request: AIRequest): Promise<AIResponse> {\r\n    if (!this.isInitialized()) {\r\n      return {\r\n        success: false,\r\n        error: '请先配置AI服务',\r\n      };\r\n    }\r\n\r\n    try {\r\n      const prompt = this.buildPrompt(request);\r\n      \r\n      const response = await this.client!.chat.completions.create({\r\n        model: this.config!.model || 'gpt-3.5-turbo',\r\n        messages: [\r\n          {\r\n            role: 'system',\r\n            content: this.getSystemPrompt(request.type),\r\n          },\r\n          {\r\n            role: 'user',\r\n            content: prompt,\r\n          },\r\n        ],\r\n        temperature: this.config!.temperature || 0.7,\r\n        max_tokens: this.config!.maxTokens || 2000,\r\n      });\r\n\r\n      const result = response.choices[0]?.message?.content;\r\n      \r\n      if (!result) {\r\n        return {\r\n          success: false,\r\n          error: 'AI服务返回空结果',\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        result: result.trim(),\r\n      };\r\n    } catch (error: any) {\r\n      console.error('AI request failed:', error);\r\n      return {\r\n        success: false,\r\n        error: error.message || 'AI请求失败',\r\n      };\r\n    }\r\n  }\r\n\r\n  // 构建提示词\r\n  private buildPrompt(request: AIRequest): string {\r\n    const { type, content, options } = request;\r\n\r\n    switch (type) {\r\n      case 'grammar':\r\n        return `请检查并修正以下文本的语法错误，保持原意不变：\\n\\n${content}`;\r\n      \r\n      case 'style':\r\n        const style = options?.style || '正式';\r\n        return `请将以下文本改写为${style}风格，保持核心内容不变：\\n\\n${content}`;\r\n      \r\n      case 'expand':\r\n        return `请扩展以下内容，添加更多细节和解释，使其更加丰富：\\n\\n${content}`;\r\n      \r\n      case 'summarize':\r\n        return `请总结以下内容的要点：\\n\\n${content}`;\r\n      \r\n      case 'translate':\r\n        const targetLang = options?.targetLanguage || '英文';\r\n        return `请将以下内容翻译为${targetLang}：\\n\\n${content}`;\r\n      \r\n      case 'toc':\r\n        return `请为以下Markdown文档生成目录结构，使用Markdown格式：\\n\\n${content}`;\r\n      \r\n      case 'format':\r\n        return `请优化以下Markdown文档的格式和结构，使其更加清晰易读：\\n\\n${content}`;\r\n      \r\n      default:\r\n        return content;\r\n    }\r\n  }\r\n\r\n  // 获取系统提示词\r\n  private getSystemPrompt(type: string): string {\r\n    const basePrompt = '你是一个专业的文档编辑助手，专门帮助用户改进Markdown文档。';\r\n    \r\n    switch (type) {\r\n      case 'grammar':\r\n        return `${basePrompt}你的任务是检查和修正语法错误，包括拼写、标点、语法结构等。请只返回修正后的文本，不要添加额外说明。`;\r\n      \r\n      case 'style':\r\n        return `${basePrompt}你的任务是调整文本的写作风格和语调。请保持内容的准确性，只调整表达方式。`;\r\n      \r\n      case 'expand':\r\n        return `${basePrompt}你的任务是扩展内容，添加相关的细节、例子和解释，使文档更加完整和有用。`;\r\n      \r\n      case 'summarize':\r\n        return `${basePrompt}你的任务是提取和总结关键信息，创建简洁明了的摘要。`;\r\n      \r\n      case 'translate':\r\n        return `${basePrompt}你的任务是进行准确的翻译，保持原文的语调和格式。`;\r\n      \r\n      case 'toc':\r\n        return `${basePrompt}你的任务是分析文档结构，生成清晰的目录。请使用标准的Markdown目录格式。`;\r\n      \r\n      case 'format':\r\n        return `${basePrompt}你的任务是优化Markdown文档的格式，包括标题层级、段落结构、列表格式等。`;\r\n      \r\n      default:\r\n        return basePrompt;\r\n    }\r\n  }\r\n\r\n  // 语法检查\r\n  async checkGrammar(content: string): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'grammar',\r\n      content,\r\n    });\r\n  }\r\n\r\n  // 风格调整\r\n  async adjustStyle(content: string, style: string = '正式'): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'style',\r\n      content,\r\n      options: { style },\r\n    });\r\n  }\r\n\r\n  // 内容扩展\r\n  async expandContent(content: string): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'expand',\r\n      content,\r\n    });\r\n  }\r\n\r\n  // 内容总结\r\n  async summarizeContent(content: string): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'summarize',\r\n      content,\r\n    });\r\n  }\r\n\r\n  // 翻译\r\n  async translateContent(content: string, targetLanguage: string): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'translate',\r\n      content,\r\n      options: { targetLanguage },\r\n    });\r\n  }\r\n\r\n  // 生成目录\r\n  async generateTOC(content: string): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'toc',\r\n      content,\r\n    });\r\n  }\r\n\r\n  // 格式优化\r\n  async formatDocument(content: string): Promise<AIResponse> {\r\n    return this.makeRequest({\r\n      type: 'format',\r\n      content,\r\n    });\r\n  }\r\n\r\n  // AI智能自动修复\r\n  async intelligentAutoFix(content: string, customPrompt?: string): Promise<AIResponse> {\r\n    if (!this.isInitialized()) {\r\n      return {\r\n        success: false,\r\n        error: '请先配置AI服务',\r\n      };\r\n    }\r\n\r\n    try {\r\n      // 构建基础修复要求\r\n      let fixingRequirements = `**修复要求：**\r\n1. **标题格式**：确保标题符号后有空格，移除标题结尾的标点符号\r\n2. **中英文排版**：在中文与英文字母之间添加空格\r\n3. **数字排版**：在中文与数字之间添加空格\r\n4. **空格规范**：移除多余的连续空格，清理行尾空格\r\n5. **空行处理**：移除多余的连续空行，保持适当的段落间距\r\n6. **列表格式**：统一列表缩进，使用标准的Markdown列表格式\r\n7. **标点符号**：规范中英文标点符号的使用\r\n8. **链接格式**：确保链接格式正确\r\n9. **代码块**：确保代码块格式正确\r\n10. **表格格式**：规范表格的对齐和格式`;\r\n\r\n      // 如果有自定义提示，添加到修复要求中\r\n      if (customPrompt && customPrompt.trim()) {\r\n        fixingRequirements += `\r\n\r\n**用户特殊要求：**\r\n${customPrompt.trim()}\r\n请特别关注用户的特殊要求，在执行标准修复的同时，优先满足用户的自定义指导。`;\r\n      }\r\n\r\n      const prompt = `请作为一个专业的Markdown文档格式修复专家，对以下文档进行智能自动修复。\r\n\r\n${fixingRequirements}\r\n\r\n**重要说明：**\r\n- 只修复格式问题，不要改变文档的内容和含义\r\n- 保持原有的文档结构和层次\r\n- 返回修复后的完整文档内容\r\n- 不要添加任何解释说明，只返回修复后的Markdown内容\r\n- 确保修复后的文档在语义上与原文档完全一致\r\n\r\n**待修复文档：**\r\n${content}`;\r\n\r\n      const response = await this.client!.chat.completions.create({\r\n        model: this.config!.model || 'gpt-3.5-turbo',\r\n        messages: [\r\n          {\r\n            role: 'system',\r\n            content: '你是一个专业的Markdown文档格式修复专家。你的任务是修复文档中的格式问题，但不改变内容的含义。请严格按照用户的要求进行修复，只返回修复后的文档内容，不要添加任何额外的说明。',\r\n          },\r\n          {\r\n            role: 'user',\r\n            content: prompt,\r\n          },\r\n        ],\r\n        temperature: 0.1, // 使用较低的温度以确保一致性\r\n        max_tokens: this.config!.maxTokens || 4000,\r\n      });\r\n\r\n      const result = response.choices[0]?.message?.content;\r\n\r\n      if (!result) {\r\n        return {\r\n          success: false,\r\n          error: 'AI服务返回空结果',\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        result: result.trim(),\r\n      };\r\n    } catch (error: any) {\r\n      console.error('AI智能自动修复失败:', error);\r\n      return {\r\n        success: false,\r\n        error: error.message || 'AI智能自动修复失败',\r\n      };\r\n    }\r\n  }\r\n\r\n  // 精准格式优化\r\n  async formatDocumentPrecise(content: string, formatType: string, customPrompt?: string): Promise<AIResponse> {\r\n    if (!this.isInitialized()) {\r\n      return {\r\n        success: false,\r\n        error: '请先配置AI服务',\r\n      };\r\n    }\r\n\r\n    try {\r\n      // 使用自定义提示词或构建默认提示词\r\n      if (customPrompt) {\r\n        // 使用自定义提示词直接调用OpenAI\r\n        const response = await this.client!.chat.completions.create({\r\n          model: this.config!.model || 'gpt-3.5-turbo',\r\n          messages: [\r\n            {\r\n              role: 'system',\r\n              content: '你是一个专业的文档编辑助手，专门帮助用户改进Markdown文档的格式和结构。请按照用户的要求进行精准的格式优化。',\r\n            },\r\n            {\r\n              role: 'user',\r\n              content: customPrompt,\r\n            },\r\n          ],\r\n          temperature: this.config!.temperature || 0.7,\r\n          max_tokens: this.config!.maxTokens || 2000,\r\n        });\r\n\r\n        const result = response.choices[0]?.message?.content;\r\n\r\n        if (!result) {\r\n          return {\r\n            success: false,\r\n            error: 'AI服务返回空结果',\r\n          };\r\n        }\r\n\r\n        return {\r\n          success: true,\r\n          result: result.trim(),\r\n        };\r\n      } else {\r\n        // 使用标准格式优化\r\n        return this.makeRequest({\r\n          type: 'format',\r\n          content,\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('精准格式优化失败:', error);\r\n      return {\r\n        success: false,\r\n        error: error.message || '格式优化失败'\r\n      };\r\n    }\r\n  }\r\n\r\n  // 构建精准格式优化提示词\r\n  private buildPreciseFormatPrompt(content: string, formatType: string): string {\r\n    const formatTypeNames = {\r\n      'academic': '学术论文',\r\n      'business': '商务报告',\r\n      'resume': '简历文档',\r\n      'general': '通用文档'\r\n    };\r\n\r\n    const typeName = formatTypeNames[formatType as keyof typeof formatTypeNames] || '专业文档';\r\n\r\n    return `请按照${typeName}格式标准精准优化这份文档，具体要求：\r\n\r\n**核心目标：**\r\n使文档达到清晰易读、层次分明、符合${typeName}场景规范的效果，避免格式混乱、排版松散或重点不突出的问题。\r\n\r\n**标题层级规范：**\r\n- 一级标题：# 大号字体加粗，居中或左对齐，段前距适当\r\n- 二级标题：## 中号字体加粗，左对齐，段前距1行\r\n- 三级标题：### 小号字体加粗，左对齐，段前距0.5行\r\n- 标题后不加标点符号，编号格式统一\r\n\r\n**正文格式要求：**\r\n- 字体字号统一，行间距1.5倍，段间距0.5行\r\n- 首行缩进2字符，避免单字成行\r\n- 段落长度适中（3-8行），超长段落建议拆分\r\n- 中英文混排时，中英文间空1格，数字与中文间空1格\r\n\r\n**重点内容处理：**\r\n- 关键数据用**加粗**标注\r\n- 重要信息用**加粗**突出\r\n- 引用内容用*斜体*+双引号，标注来源\r\n- 注意事项用特殊标记突出\r\n\r\n**特殊元素规范：**\r\n- 表格：整体居中，边框统一，表头加粗居中，数据对齐\r\n- 列表：有序用\"1. 2. 3.\"，无序用\"•\"，层级缩进一致\r\n- 图片：居中放置，宽度适当，下方标注说明\r\n- 代码：等宽字体，背景区分，单独成段\r\n\r\n**自查要求：**\r\n优化后请确认：\r\n1. 格式前后一致，无多余空行空格\r\n2. 标题层级清晰，编号统一\r\n3. 重点内容突出，易于识别\r\n4. 整体美观专业，符合规范\r\n\r\n文档内容：\r\n${content}\r\n\r\n请返回优化后的文档，并在最后简要说明主要修改内容。`;\r\n  }\r\n\r\n  // 格式检查\r\n  async checkDocumentFormat(content: string, formatType: string): Promise<AIResponse> {\r\n    const prompt = `请检查以下文档的格式规范性，针对${formatType}文档标准进行评估：\r\n\r\n**检查维度：**\r\n1. **标题格式**：层级是否清晰，字体字号是否统一，编号是否规范\r\n2. **正文格式**：字体、行距、段距是否一致，缩进是否正确\r\n3. **特殊元素**：表格、列表、图片格式是否规范\r\n4. **整体布局**：排版是否美观，重点是否突出\r\n5. **细节规范**：标点符号、空格、对齐等细节是否正确\r\n\r\n**评估标准：**\r\n- 优秀：格式完全符合规范，美观专业\r\n- 良好：格式基本规范，有少量细节问题\r\n- 一般：格式有明显问题，需要优化\r\n- 较差：格式混乱，需要大幅调整\r\n\r\n文档内容：\r\n${content}\r\n\r\n请提供：\r\n1. 总体评估等级\r\n2. 具体问题清单\r\n3. 改进建议\r\n4. 优化重点`;\r\n\r\n    if (!this.isInitialized()) {\r\n      return {\r\n        success: false,\r\n        error: '请先配置AI服务',\r\n      };\r\n    }\r\n\r\n    try {\r\n      // 使用OpenAI客户端进行格式检查\r\n      const response = await this.client!.chat.completions.create({\r\n        model: this.config!.model || 'gpt-3.5-turbo',\r\n        messages: [\r\n          {\r\n            role: 'system',\r\n            content: '你是一个专业的文档格式检查专家，专门评估Markdown文档的格式规范性。',\r\n          },\r\n          {\r\n            role: 'user',\r\n            content: prompt,\r\n          },\r\n        ],\r\n        temperature: this.config!.temperature || 0.7,\r\n        max_tokens: this.config!.maxTokens || 2000,\r\n      });\r\n\r\n      const result = response.choices[0]?.message?.content;\r\n\r\n      if (!result) {\r\n        return {\r\n          success: false,\r\n          error: 'AI服务返回空结果',\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        result: result.trim(),\r\n      };\r\n    } catch (error: any) {\r\n      console.error('格式检查失败:', error);\r\n      return {\r\n        success: false,\r\n        error: error.message || '格式检查失败'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// 创建单例实例\r\nexport const aiService = new AIService();\r\n\r\n// 默认配置\r\nexport const defaultAIConfig: Partial<AIConfig> = {\r\n  model: 'gpt-3.5-turbo',\r\n  temperature: 0.7,\r\n  maxTokens: 2000,\r\n};\r\n\r\n// 保存AI配置到本地存储\r\nexport const saveAIConfig = (config: AIConfig) => {\r\n  try {\r\n    localStorage.setItem('martetdown-ai-config', JSON.stringify(config));\r\n    return true;\r\n  } catch (error) {\r\n    console.error('Failed to save AI config:', error);\r\n    return false;\r\n  }\r\n};\r\n\r\n// 从本地存储加载AI配置\r\nexport const loadAIConfig = (): AIConfig | null => {\r\n  try {\r\n    const stored = localStorage.getItem('martetdown-ai-config');\r\n    if (stored) {\r\n      return JSON.parse(stored);\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to load AI config:', error);\r\n  }\r\n  return null;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AA4BA,MAAM;IACI,SAAwB,KAAK;IAC7B,SAA0B,KAAK;IAEvC,UAAU;IACV,WAAW,MAAgB,EAAE;QAC3B,IAAI;YACF,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,MAAM,GAAG,IAAI,sKAAA,CAAA,UAAM,CAAC;gBACvB,QAAQ,OAAO,MAAM;gBACrB,SAAS,OAAO,OAAO;gBACvB,yBAAyB;YAC3B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEA,WAAW;IACX,gBAAyB;QACvB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK;IACjD;IAEA,WAAW;IACX,MAAM,YAAY,OAAkB,EAAuB;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC;YAEhC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,OAAO,IAAI,CAAC,MAAM,CAAE,KAAK,IAAI;gBAC7B,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI;oBAC5C;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa,IAAI,CAAC,MAAM,CAAE,WAAW,IAAI;gBACzC,YAAY,IAAI,CAAC,MAAM,CAAE,SAAS,IAAI;YACxC;YAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAE7C,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,QAAQ,OAAO,IAAI;YACrB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF;IACF;IAEA,QAAQ;IACA,YAAY,OAAkB,EAAU;QAC9C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAEnC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,SAAS;YAEhD,KAAK;gBACH,MAAM,QAAQ,SAAS,SAAS;gBAChC,OAAO,CAAC,SAAS,EAAE,MAAM,gBAAgB,EAAE,SAAS;YAEtD,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,SAAS;YAElD,KAAK;gBACH,OAAO,CAAC,eAAe,EAAE,SAAS;YAEpC,KAAK;gBACH,MAAM,aAAa,SAAS,kBAAkB;gBAC9C,OAAO,CAAC,SAAS,EAAE,WAAW,KAAK,EAAE,SAAS;YAEhD,KAAK;gBACH,OAAO,CAAC,sCAAsC,EAAE,SAAS;YAE3D,KAAK;gBACH,OAAO,CAAC,mCAAmC,EAAE,SAAS;YAExD;gBACE,OAAO;QACX;IACF;IAEA,UAAU;IACF,gBAAgB,IAAY,EAAU;QAC5C,MAAM,aAAa;QAEnB,OAAQ;YACN,KAAK;gBACH,OAAO,GAAG,WAAW,iDAAiD,CAAC;YAEzE,KAAK;gBACH,OAAO,GAAG,WAAW,oCAAoC,CAAC;YAE5D,KAAK;gBACH,OAAO,GAAG,WAAW,mCAAmC,CAAC;YAE3D,KAAK;gBACH,OAAO,GAAG,WAAW,yBAAyB,CAAC;YAEjD,KAAK;gBACH,OAAO,GAAG,WAAW,wBAAwB,CAAC;YAEhD,KAAK;gBACH,OAAO,GAAG,WAAW,uCAAuC,CAAC;YAE/D,KAAK;gBACH,OAAO,GAAG,WAAW,uCAAuC,CAAC;YAE/D;gBACE,OAAO;QACX;IACF;IAEA,OAAO;IACP,MAAM,aAAa,OAAe,EAAuB;QACvD,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;QACF;IACF;IAEA,OAAO;IACP,MAAM,YAAY,OAAe,EAAE,QAAgB,IAAI,EAAuB;QAC5E,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;YACA,SAAS;gBAAE;YAAM;QACnB;IACF;IAEA,OAAO;IACP,MAAM,cAAc,OAAe,EAAuB;QACxD,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;QACF;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,OAAe,EAAuB;QAC3D,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;QACF;IACF;IAEA,KAAK;IACL,MAAM,iBAAiB,OAAe,EAAE,cAAsB,EAAuB;QACnF,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;YACA,SAAS;gBAAE;YAAe;QAC5B;IACF;IAEA,OAAO;IACP,MAAM,YAAY,OAAe,EAAuB;QACtD,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;QACF;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAe,EAAuB;QACzD,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,MAAM;YACN;QACF;IACF;IAEA,WAAW;IACX,MAAM,mBAAmB,OAAe,EAAE,YAAqB,EAAuB;QACpF,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,IAAI,qBAAqB,CAAC;;;;;;;;;;uBAUT,CAAC;YAElB,oBAAoB;YACpB,IAAI,gBAAgB,aAAa,IAAI,IAAI;gBACvC,sBAAsB,CAAC;;;AAG/B,EAAE,aAAa,IAAI,GAAG;qCACe,CAAC;YAChC;YAEA,MAAM,SAAS,CAAC;;AAEtB,EAAE,mBAAmB;;;;;;;;;;AAUrB,EAAE,SAAS;YAEL,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,OAAO,IAAI,CAAC,MAAM,CAAE,KAAK,IAAI;gBAC7B,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY,IAAI,CAAC,MAAM,CAAE,SAAS,IAAI;YACxC;YAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAE7C,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,QAAQ,OAAO,IAAI;YACrB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,eAAe;YAC7B,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB,OAAe,EAAE,UAAkB,EAAE,YAAqB,EAAuB;QAC3G,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI;YACF,mBAAmB;YACnB,IAAI,cAAc;gBAChB,qBAAqB;gBACrB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBAC1D,OAAO,IAAI,CAAC,MAAM,CAAE,KAAK,IAAI;oBAC7B,UAAU;wBACR;4BACE,MAAM;4BACN,SAAS;wBACX;wBACA;4BACE,MAAM;4BACN,SAAS;wBACX;qBACD;oBACD,aAAa,IAAI,CAAC,MAAM,CAAE,WAAW,IAAI;oBACzC,YAAY,IAAI,CAAC,MAAM,CAAE,SAAS,IAAI;gBACxC;gBAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;gBAE7C,IAAI,CAAC,QAAQ;oBACX,OAAO;wBACL,SAAS;wBACT,OAAO;oBACT;gBACF;gBAEA,OAAO;oBACL,SAAS;oBACT,QAAQ,OAAO,IAAI;gBACrB;YACF,OAAO;gBACL,WAAW;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC;oBACtB,MAAM;oBACN;gBACF;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF;IACF;IAEA,cAAc;IACN,yBAAyB,OAAe,EAAE,UAAkB,EAAU;QAC5E,MAAM,kBAAkB;YACtB,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,WAAW;QACb;QAEA,MAAM,WAAW,eAAe,CAAC,WAA2C,IAAI;QAEhF,OAAO,CAAC,GAAG,EAAE,SAAS;;;iBAGT,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkC5B,EAAE,QAAQ;;yBAEe,CAAC;IACxB;IAEA,OAAO;IACP,MAAM,oBAAoB,OAAe,EAAE,UAAkB,EAAuB;QAClF,MAAM,SAAS,CAAC,gBAAgB,EAAE,WAAW;;;;;;;;;;;;;;;;AAgBjD,EAAE,QAAQ;;;;;;OAMH,CAAC;QAEJ,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,OAAO,IAAI,CAAC,MAAM,CAAE,KAAK,IAAI;gBAC7B,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa,IAAI,CAAC,MAAM,CAAE,WAAW,IAAI;gBACzC,YAAY,IAAI,CAAC,MAAM,CAAE,SAAS,IAAI;YACxC;YAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAE7C,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,QAAQ,OAAO,IAAI;YACrB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF;IACF;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,MAAM,kBAAqC;IAChD,OAAO;IACP,aAAa;IACb,WAAW;AACb;AAGO,MAAM,eAAe,CAAC;IAC3B,IAAI;QACF,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;QAC5D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3383, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/indexedDB.ts"], "sourcesContent": ["'use client';\r\n\r\n// IndexedDB 数据库配置\r\nconst DB_NAME = 'MartetdownDB';\r\nconst DB_VERSION = 2; // 增加版本号以支持新字段\r\n\r\n// 数据存储对象\r\nconst STORES = {\r\n  AI_MODELS: 'aiModels',\r\n  DOCUMENTS: 'documents',\r\n  SETTINGS: 'settings',\r\n  TEMPLATES: 'templates',\r\n} as const;\r\n\r\n// AI模型接口\r\nexport interface AIModel {\r\n  id?: number;\r\n  name: string;\r\n  api_key: string;\r\n  base_url?: string;\r\n  model_name: string;\r\n  model_type: 'text' | 'image' | 'multimodal'; // 新增模型类型\r\n  temperature: number;\r\n  max_tokens: number;\r\n  description?: string;\r\n  is_default: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n// 数据库初始化\r\nclass IndexedDBManager {\r\n  private db: IDBDatabase | null = null;\r\n\r\n  async init(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to open IndexedDB'));\r\n      };\r\n\r\n      request.onsuccess = () => {\r\n        this.db = request.result;\r\n        resolve();\r\n      };\r\n\r\n      request.onupgradeneeded = (event) => {\r\n        const db = (event.target as IDBOpenDBRequest).result;\r\n        const oldVersion = event.oldVersion;\r\n\r\n        // 创建AI模型存储\r\n        if (!db.objectStoreNames.contains(STORES.AI_MODELS)) {\r\n          const aiModelsStore = db.createObjectStore(STORES.AI_MODELS, {\r\n            keyPath: 'id',\r\n            autoIncrement: true,\r\n          });\r\n          // 移除名称的唯一约束，允许重复名称\r\n          aiModelsStore.createIndex('name', 'name', { unique: false });\r\n          aiModelsStore.createIndex('is_default', 'is_default');\r\n          aiModelsStore.createIndex('model_type', 'model_type'); // 新增模型类型索引\r\n        } else if (oldVersion < 2) {\r\n          // 版本2升级：添加模型类型索引\r\n          const transaction = (event.target as IDBOpenDBRequest).transaction!;\r\n          const aiModelsStore = transaction.objectStore(STORES.AI_MODELS);\r\n          if (!aiModelsStore.indexNames.contains('model_type')) {\r\n            aiModelsStore.createIndex('model_type', 'model_type');\r\n          }\r\n        }\r\n\r\n        // 创建文档存储\r\n        if (!db.objectStoreNames.contains(STORES.DOCUMENTS)) {\r\n          const documentsStore = db.createObjectStore(STORES.DOCUMENTS, {\r\n            keyPath: 'id',\r\n          });\r\n          documentsStore.createIndex('title', 'title');\r\n          documentsStore.createIndex('updated_at', 'updated_at');\r\n        }\r\n\r\n        // 创建设置存储\r\n        if (!db.objectStoreNames.contains(STORES.SETTINGS)) {\r\n          db.createObjectStore(STORES.SETTINGS, {\r\n            keyPath: 'key',\r\n          });\r\n        }\r\n\r\n        // 创建模板存储\r\n        if (!db.objectStoreNames.contains(STORES.TEMPLATES)) {\r\n          const templatesStore = db.createObjectStore(STORES.TEMPLATES, {\r\n            keyPath: 'id',\r\n          });\r\n          templatesStore.createIndex('category', 'category');\r\n        }\r\n      };\r\n    });\r\n  }\r\n\r\n  private async ensureDB(): Promise<IDBDatabase> {\r\n    if (!this.db) {\r\n      await this.init();\r\n    }\r\n    if (!this.db) {\r\n      throw new Error('Database not initialized');\r\n    }\r\n    return this.db;\r\n  }\r\n\r\n  // AI模型操作\r\n  async getAllAIModels(): Promise<AIModel[]> {\r\n    const db = await this.ensureDB();\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.AI_MODELS], 'readonly');\r\n      const store = transaction.objectStore(STORES.AI_MODELS);\r\n      const request = store.getAll();\r\n\r\n      request.onsuccess = () => {\r\n        resolve(request.result || []);\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to get AI models'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async getAIModelById(id: number): Promise<AIModel | null> {\r\n    const db = await this.ensureDB();\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.AI_MODELS], 'readonly');\r\n      const store = transaction.objectStore(STORES.AI_MODELS);\r\n      const request = store.get(id);\r\n\r\n      request.onsuccess = () => {\r\n        resolve(request.result || null);\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to get AI model'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number> {\r\n    const db = await this.ensureDB();\r\n    \r\n    // 如果设置为默认，先取消其他默认设置\r\n    if (model.is_default) {\r\n      await this.clearDefaultAIModel();\r\n    }\r\n\r\n    const now = new Date().toISOString();\r\n    const modelWithTimestamps: Omit<AIModel, 'id'> = {\r\n      ...model,\r\n      created_at: now,\r\n      updated_at: now,\r\n    };\r\n\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.AI_MODELS], 'readwrite');\r\n      const store = transaction.objectStore(STORES.AI_MODELS);\r\n      const request = store.add(modelWithTimestamps);\r\n\r\n      request.onsuccess = () => {\r\n        resolve(request.result as number);\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to create AI model'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async updateAIModel(id: number, updates: Partial<Omit<AIModel, 'id' | 'created_at'>>): Promise<void> {\r\n    const db = await this.ensureDB();\r\n    \r\n    // 如果设置为默认，先取消其他默认设置\r\n    if (updates.is_default) {\r\n      await this.clearDefaultAIModel();\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.AI_MODELS], 'readwrite');\r\n      const store = transaction.objectStore(STORES.AI_MODELS);\r\n      const getRequest = store.get(id);\r\n\r\n      getRequest.onsuccess = () => {\r\n        const existingModel = getRequest.result;\r\n        if (!existingModel) {\r\n          reject(new Error('AI model not found'));\r\n          return;\r\n        }\r\n\r\n        const updatedModel = {\r\n          ...existingModel,\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        };\r\n\r\n        const putRequest = store.put(updatedModel);\r\n        putRequest.onsuccess = () => resolve();\r\n        putRequest.onerror = () => reject(new Error('Failed to update AI model'));\r\n      };\r\n\r\n      getRequest.onerror = () => {\r\n        reject(new Error('Failed to get AI model for update'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async deleteAIModel(id: number): Promise<void> {\r\n    const db = await this.ensureDB();\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.AI_MODELS], 'readwrite');\r\n      const store = transaction.objectStore(STORES.AI_MODELS);\r\n      const request = store.delete(id);\r\n\r\n      request.onsuccess = () => {\r\n        resolve();\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to delete AI model'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async getDefaultAIModel(): Promise<AIModel | null> {\r\n    const db = await this.ensureDB();\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.AI_MODELS], 'readonly');\r\n      const store = transaction.objectStore(STORES.AI_MODELS);\r\n      const index = store.index('is_default');\r\n      const request = index.get(true);\r\n\r\n      request.onsuccess = () => {\r\n        resolve(request.result || null);\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to get default AI model'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async setDefaultAIModel(id: number): Promise<void> {\r\n    await this.clearDefaultAIModel();\r\n    await this.updateAIModel(id, { is_default: true });\r\n  }\r\n\r\n  private async clearDefaultAIModel(): Promise<void> {\r\n    const models = await this.getAllAIModels();\r\n    const defaultModel = models.find(m => m.is_default);\r\n    if (defaultModel && defaultModel.id) {\r\n      await this.updateAIModel(defaultModel.id, { is_default: false });\r\n    }\r\n  }\r\n\r\n  // 设置操作\r\n  async getSetting(key: string): Promise<any> {\r\n    const db = await this.ensureDB();\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.SETTINGS], 'readonly');\r\n      const store = transaction.objectStore(STORES.SETTINGS);\r\n      const request = store.get(key);\r\n\r\n      request.onsuccess = () => {\r\n        const result = request.result;\r\n        resolve(result ? result.value : null);\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to get setting'));\r\n      };\r\n    });\r\n  }\r\n\r\n  async setSetting(key: string, value: any): Promise<void> {\r\n    const db = await this.ensureDB();\r\n    return new Promise((resolve, reject) => {\r\n      const transaction = db.transaction([STORES.SETTINGS], 'readwrite');\r\n      const store = transaction.objectStore(STORES.SETTINGS);\r\n      const request = store.put({\r\n        key,\r\n        value,\r\n        updated_at: new Date().toISOString(),\r\n      });\r\n\r\n      request.onsuccess = () => {\r\n        resolve();\r\n      };\r\n\r\n      request.onerror = () => {\r\n        reject(new Error('Failed to set setting'));\r\n      };\r\n    });\r\n  }\r\n}\r\n\r\n// 创建单例实例\r\nexport const dbManager = new IndexedDBManager();\r\n\r\n// 初始化默认AI模型\r\nexport const initializeDefaultModels = async () => {\r\n  try {\r\n    await dbManager.init();\r\n    \r\n    const existingModels = await dbManager.getAllAIModels();\r\n    if (existingModels.length === 0) {\r\n      // 添加默认模型\r\n      const defaultModels = [\r\n        {\r\n          name: 'OpenAI GPT-3.5 Turbo',\r\n          api_key: '',\r\n          base_url: 'https://api.openai.com/v1',\r\n          model_name: 'gpt-3.5-turbo',\r\n          model_type: 'text' as const,\r\n          temperature: 0.7,\r\n          max_tokens: 2000,\r\n          description: 'OpenAI官方GPT-3.5 Turbo模型，性价比高',\r\n          is_default: true,\r\n        },\r\n        {\r\n          name: 'OpenAI GPT-4',\r\n          api_key: '',\r\n          base_url: 'https://api.openai.com/v1',\r\n          model_name: 'gpt-4',\r\n          model_type: 'text' as const,\r\n          temperature: 0.7,\r\n          max_tokens: 2000,\r\n          description: 'OpenAI最新GPT-4模型，能力更强',\r\n          is_default: false,\r\n        },\r\n        {\r\n          name: 'OpenAI GPT-4 Vision',\r\n          api_key: '',\r\n          base_url: 'https://api.openai.com/v1',\r\n          model_name: 'gpt-4-vision-preview',\r\n          model_type: 'multimodal' as const,\r\n          temperature: 0.7,\r\n          max_tokens: 2000,\r\n          description: 'OpenAI GPT-4 Vision模型，支持图像理解',\r\n          is_default: false,\r\n        },\r\n        {\r\n          name: 'DALL-E 3',\r\n          api_key: '',\r\n          base_url: 'https://api.openai.com/v1',\r\n          model_name: 'dall-e-3',\r\n          model_type: 'image' as const,\r\n          temperature: 0.7,\r\n          max_tokens: 1000,\r\n          description: 'OpenAI DALL-E 3图像生成模型',\r\n          is_default: false,\r\n        },\r\n        {\r\n          name: 'Azure OpenAI',\r\n          api_key: '',\r\n          base_url: '',\r\n          model_name: 'gpt-35-turbo',\r\n          model_type: 'text' as const,\r\n          temperature: 0.7,\r\n          max_tokens: 2000,\r\n          description: 'Microsoft Azure OpenAI服务',\r\n          is_default: false,\r\n        },\r\n      ];\r\n\r\n      for (const model of defaultModels) {\r\n        await dbManager.createAIModel(model);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to initialize default models:', error);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA,kBAAkB;AAClB,MAAM,UAAU;AAChB,MAAM,aAAa,GAAG,cAAc;AAEpC,SAAS;AACT,MAAM,SAAS;IACb,WAAW;IACX,WAAW;IACX,UAAU;IACV,WAAW;AACb;AAkBA,SAAS;AACT,MAAM;IACI,KAAyB,KAAK;IAEtC,MAAM,OAAsB;QAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAAU,UAAU,IAAI,CAAC,SAAS;YAExC,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;YAEA,QAAQ,SAAS,GAAG;gBAClB,IAAI,CAAC,EAAE,GAAG,QAAQ,MAAM;gBACxB;YACF;YAEA,QAAQ,eAAe,GAAG,CAAC;gBACzB,MAAM,KAAK,AAAC,MAAM,MAAM,CAAsB,MAAM;gBACpD,MAAM,aAAa,MAAM,UAAU;gBAEnC,WAAW;gBACX,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,SAAS,GAAG;oBACnD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,OAAO,SAAS,EAAE;wBAC3D,SAAS;wBACT,eAAe;oBACjB;oBACA,mBAAmB;oBACnB,cAAc,WAAW,CAAC,QAAQ,QAAQ;wBAAE,QAAQ;oBAAM;oBAC1D,cAAc,WAAW,CAAC,cAAc;oBACxC,cAAc,WAAW,CAAC,cAAc,eAAe,WAAW;gBACpE,OAAO,IAAI,aAAa,GAAG;oBACzB,iBAAiB;oBACjB,MAAM,cAAc,AAAC,MAAM,MAAM,CAAsB,WAAW;oBAClE,MAAM,gBAAgB,YAAY,WAAW,CAAC,OAAO,SAAS;oBAC9D,IAAI,CAAC,cAAc,UAAU,CAAC,QAAQ,CAAC,eAAe;wBACpD,cAAc,WAAW,CAAC,cAAc;oBAC1C;gBACF;gBAEA,SAAS;gBACT,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,SAAS,GAAG;oBACnD,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,SAAS,EAAE;wBAC5D,SAAS;oBACX;oBACA,eAAe,WAAW,CAAC,SAAS;oBACpC,eAAe,WAAW,CAAC,cAAc;gBAC3C;gBAEA,SAAS;gBACT,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,QAAQ,GAAG;oBAClD,GAAG,iBAAiB,CAAC,OAAO,QAAQ,EAAE;wBACpC,SAAS;oBACX;gBACF;gBAEA,SAAS;gBACT,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,SAAS,GAAG;oBACnD,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,SAAS,EAAE;wBAC5D,SAAS;oBACX;oBACA,eAAe,WAAW,CAAC,YAAY;gBACzC;YACF;QACF;IACF;IAEA,MAAc,WAAiC;QAC7C,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACZ,MAAM,IAAI,CAAC,IAAI;QACjB;QACA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACZ,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,EAAE;IAChB;IAEA,SAAS;IACT,MAAM,iBAAqC;QACzC,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,SAAS;aAAC,EAAE;YACvD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,SAAS;YACtD,MAAM,UAAU,MAAM,MAAM;YAE5B,QAAQ,SAAS,GAAG;gBAClB,QAAQ,QAAQ,MAAM,IAAI,EAAE;YAC9B;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,eAAe,EAAU,EAA2B;QACxD,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,SAAS;aAAC,EAAE;YACvD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,SAAS;YACtD,MAAM,UAAU,MAAM,GAAG,CAAC;YAE1B,QAAQ,SAAS,GAAG;gBAClB,QAAQ,QAAQ,MAAM,IAAI;YAC5B;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,cAAc,KAAwD,EAAmB;QAC7F,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAE9B,oBAAoB;QACpB,IAAI,MAAM,UAAU,EAAE;YACpB,MAAM,IAAI,CAAC,mBAAmB;QAChC;QAEA,MAAM,MAAM,IAAI,OAAO,WAAW;QAClC,MAAM,sBAA2C;YAC/C,GAAG,KAAK;YACR,YAAY;YACZ,YAAY;QACd;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,SAAS;aAAC,EAAE;YACvD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,SAAS;YACtD,MAAM,UAAU,MAAM,GAAG,CAAC;YAE1B,QAAQ,SAAS,GAAG;gBAClB,QAAQ,QAAQ,MAAM;YACxB;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAoD,EAAiB;QACnG,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAE9B,oBAAoB;QACpB,IAAI,QAAQ,UAAU,EAAE;YACtB,MAAM,IAAI,CAAC,mBAAmB;QAChC;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,SAAS;aAAC,EAAE;YACvD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,SAAS;YACtD,MAAM,aAAa,MAAM,GAAG,CAAC;YAE7B,WAAW,SAAS,GAAG;gBACrB,MAAM,gBAAgB,WAAW,MAAM;gBACvC,IAAI,CAAC,eAAe;oBAClB,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,MAAM,eAAe;oBACnB,GAAG,aAAa;oBAChB,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,MAAM,aAAa,MAAM,GAAG,CAAC;gBAC7B,WAAW,SAAS,GAAG,IAAM;gBAC7B,WAAW,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YAC9C;YAEA,WAAW,OAAO,GAAG;gBACnB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,cAAc,EAAU,EAAiB;QAC7C,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,SAAS;aAAC,EAAE;YACvD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,SAAS;YACtD,MAAM,UAAU,MAAM,MAAM,CAAC;YAE7B,QAAQ,SAAS,GAAG;gBAClB;YACF;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,oBAA6C;QACjD,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,SAAS;aAAC,EAAE;YACvD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,SAAS;YACtD,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,MAAM,UAAU,MAAM,GAAG,CAAC;YAE1B,QAAQ,SAAS,GAAG;gBAClB,QAAQ,QAAQ,MAAM,IAAI;YAC5B;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAiB;QACjD,MAAM,IAAI,CAAC,mBAAmB;QAC9B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI;YAAE,YAAY;QAAK;IAClD;IAEA,MAAc,sBAAqC;QACjD,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;QACxC,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU;QAClD,IAAI,gBAAgB,aAAa,EAAE,EAAE;YACnC,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,EAAE;gBAAE,YAAY;YAAM;QAChE;IACF;IAEA,OAAO;IACP,MAAM,WAAW,GAAW,EAAgB;QAC1C,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,QAAQ;aAAC,EAAE;YACtD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,QAAQ;YACrD,MAAM,UAAU,MAAM,GAAG,CAAC;YAE1B,QAAQ,SAAS,GAAG;gBAClB,MAAM,SAAS,QAAQ,MAAM;gBAC7B,QAAQ,SAAS,OAAO,KAAK,GAAG;YAClC;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;IAEA,MAAM,WAAW,GAAW,EAAE,KAAU,EAAiB;QACvD,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ;QAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC;gBAAC,OAAO,QAAQ;aAAC,EAAE;YACtD,MAAM,QAAQ,YAAY,WAAW,CAAC,OAAO,QAAQ;YACrD,MAAM,UAAU,MAAM,GAAG,CAAC;gBACxB;gBACA;gBACA,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,QAAQ,SAAS,GAAG;gBAClB;YACF;YAEA,QAAQ,OAAO,GAAG;gBAChB,OAAO,IAAI,MAAM;YACnB;QACF;IACF;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,MAAM,0BAA0B;IACrC,IAAI;QACF,MAAM,UAAU,IAAI;QAEpB,MAAM,iBAAiB,MAAM,UAAU,cAAc;QACrD,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,SAAS;YACT,MAAM,gBAAgB;gBACpB;oBACE,MAAM;oBACN,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,YAAY;gBACd;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,YAAY;gBACd;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,YAAY;gBACd;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,YAAY;gBACd;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,YAAY;gBACd;aACD;YAED,KAAK,MAAM,SAAS,cAAe;gBACjC,MAAM,UAAU,aAAa,CAAC;YAChC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;IACxD;AACF", "debugId": null}}, {"offset": {"line": 3721, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/AIAssistant/ProcessingSteps.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface ProcessingStepsProps {\r\n  currentStep: string;\r\n  progress: number;\r\n  isVisible: boolean;\r\n}\r\n\r\ninterface Step {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  icon: string;\r\n  progressRange: [number, number];\r\n}\r\n\r\nconst steps: Step[] = [\r\n  {\r\n    id: 'init',\r\n    title: '初始化',\r\n    description: '正在准备AI请求和分析内容',\r\n    icon: '🔄',\r\n    progressRange: [0, 25]\r\n  },\r\n  {\r\n    id: 'analyze',\r\n    title: '内容分析',\r\n    description: '正在理解文档结构和语义',\r\n    icon: '📝',\r\n    progressRange: [25, 50]\r\n  },\r\n  {\r\n    id: 'process',\r\n    title: 'AI处理',\r\n    description: '正在生成优化建议和内容',\r\n    icon: '🤖',\r\n    progressRange: [50, 80]\r\n  },\r\n  {\r\n    id: 'apply',\r\n    title: '应用结果',\r\n    description: '正在整理和应用处理结果',\r\n    icon: '✨',\r\n    progressRange: [80, 95]\r\n  },\r\n  {\r\n    id: 'complete',\r\n    title: '完成',\r\n    description: '处理完成，内容已优化',\r\n    icon: '✅',\r\n    progressRange: [95, 100]\r\n  }\r\n];\r\n\r\nexport const ProcessingSteps: React.FC<ProcessingStepsProps> = ({\r\n  currentStep,\r\n  progress,\r\n  isVisible\r\n}) => {\r\n  if (!isVisible) return null;\r\n\r\n  const getCurrentStepIndex = () => {\r\n    return steps.findIndex(step => \r\n      progress >= step.progressRange[0] && progress < step.progressRange[1]\r\n    );\r\n  };\r\n\r\n  const currentStepIndex = getCurrentStepIndex();\r\n\r\n  return (\r\n    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4\">\r\n      {/* 标题和总进度 */}\r\n      <div className=\"flex items-center gap-3 mb-4\">\r\n        <div className=\"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"></div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-blue-700 dark:text-blue-300 font-medium\">AI正在处理中</h3>\r\n          <p className=\"text-sm text-blue-600 dark:text-blue-400\">{currentStep}</p>\r\n        </div>\r\n        <div className=\"text-right\">\r\n          <div className=\"text-lg font-bold text-blue-700 dark:text-blue-300\">{progress}%</div>\r\n          <div className=\"text-xs text-blue-600 dark:text-blue-400\">完成度</div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 总进度条 */}\r\n      <div className=\"mb-4\">\r\n        <div className=\"w-full bg-blue-100 dark:bg-blue-800 rounded-full h-3\">\r\n          <div \r\n            className=\"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-500 ease-out relative overflow-hidden\"\r\n            style={{ width: `${progress}%` }}\r\n          >\r\n            {/* 进度条动画效果 */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 步骤列表 */}\r\n      <div className=\"space-y-2\">\r\n        {steps.map((step, index) => {\r\n          const isActive = index === currentStepIndex;\r\n          const isCompleted = progress > step.progressRange[1];\r\n          const isUpcoming = progress < step.progressRange[0];\r\n\r\n          return (\r\n            <div\r\n              key={step.id}\r\n              className={`flex items-center gap-3 p-2 rounded-md transition-all duration-300 ${\r\n                isActive \r\n                  ? 'bg-blue-100 dark:bg-blue-800/50 scale-105' \r\n                  : isCompleted\r\n                  ? 'bg-green-50 dark:bg-green-900/20'\r\n                  : 'bg-gray-50 dark:bg-gray-800/50'\r\n              }`}\r\n            >\r\n              {/* 步骤图标 */}\r\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm transition-all duration-300 ${\r\n                isActive\r\n                  ? 'bg-blue-500 text-white animate-pulse'\r\n                  : isCompleted\r\n                  ? 'bg-green-500 text-white'\r\n                  : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'\r\n              }`}>\r\n                {isCompleted ? '✓' : step.icon}\r\n              </div>\r\n\r\n              {/* 步骤信息 */}\r\n              <div className=\"flex-1\">\r\n                <div className={`font-medium transition-colors duration-300 ${\r\n                  isActive\r\n                    ? 'text-blue-700 dark:text-blue-300'\r\n                    : isCompleted\r\n                    ? 'text-green-700 dark:text-green-300'\r\n                    : 'text-gray-600 dark:text-gray-400'\r\n                }`}>\r\n                  {step.title}\r\n                </div>\r\n                <div className={`text-xs transition-colors duration-300 ${\r\n                  isActive\r\n                    ? 'text-blue-600 dark:text-blue-400'\r\n                    : isCompleted\r\n                    ? 'text-green-600 dark:text-green-400'\r\n                    : 'text-gray-500 dark:text-gray-500'\r\n                }`}>\r\n                  {step.description}\r\n                </div>\r\n              </div>\r\n\r\n              {/* 步骤状态 */}\r\n              <div className=\"text-right\">\r\n                {isActive && (\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-ping\"></div>\r\n                    <span className=\"text-xs text-blue-600 dark:text-blue-400\">进行中</span>\r\n                  </div>\r\n                )}\r\n                {isCompleted && (\r\n                  <span className=\"text-xs text-green-600 dark:text-green-400\">已完成</span>\r\n                )}\r\n                {isUpcoming && (\r\n                  <span className=\"text-xs text-gray-500 dark:text-gray-500\">等待中</span>\r\n                )}\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      {/* 底部提示 */}\r\n      <div className=\"mt-4 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-md\">\r\n        <div className=\"flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300\">\r\n          <span>💡</span>\r\n          <span>\r\n            {progress < 50 \r\n              ? \"AI正在分析您的文档内容，请稍候...\"\r\n              : progress < 80\r\n              ? \"AI正在生成优化建议，马上就好...\"\r\n              : \"即将完成，正在整理结果...\"\r\n            }\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAkBA,MAAM,QAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,eAAe;YAAC;YAAG;SAAG;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,eAAe;YAAC;YAAI;SAAG;IACzB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,eAAe;YAAC;YAAI;SAAG;IACzB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,eAAe;YAAC;YAAI;SAAG;IACzB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,eAAe;YAAC;YAAI;SAAI;IAC1B;CACD;AAEM,MAAM,kBAAkD,CAAC,EAC9D,WAAW,EACX,QAAQ,EACR,SAAS,EACV;IACC,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,sBAAsB;QAC1B,OAAO,MAAM,SAAS,CAAC,CAAA,OACrB,YAAY,KAAK,aAAa,CAAC,EAAE,IAAI,WAAW,KAAK,aAAa,CAAC,EAAE;IAEzE;IAEA,MAAM,mBAAmB;IAEzB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAC7D,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAE3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAsD;oCAAS;;;;;;;0CAC9E,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAK9D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,SAAS,CAAC,CAAC;wBAAC;kCAG/B,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,MAAM,WAAW,UAAU;oBAC3B,MAAM,cAAc,WAAW,KAAK,aAAa,CAAC,EAAE;oBACpD,MAAM,aAAa,WAAW,KAAK,aAAa,CAAC,EAAE;oBAEnD,qBACE,8OAAC;wBAEC,WAAW,CAAC,mEAAmE,EAC7E,WACI,8CACA,cACA,qCACA,kCACJ;;0CAGF,8OAAC;gCAAI,WAAW,CAAC,0FAA0F,EACzG,WACI,yCACA,cACA,4BACA,iEACJ;0CACC,cAAc,MAAM,KAAK,IAAI;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,WACI,qCACA,cACA,uCACA,oCACJ;kDACC,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAW,CAAC,uCAAuC,EACtD,WACI,qCACA,cACA,uCACA,oCACJ;kDACC,KAAK,WAAW;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;;oCACZ,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;oCAG9D,6BACC,8OAAC;wCAAK,WAAU;kDAA6C;;;;;;oCAE9D,4BACC,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;uBAtD1D,KAAK,EAAE;;;;;gBA2DlB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAK;;;;;;sCACN,8OAAC;sCACE,WAAW,KACR,wBACA,WAAW,KACX,uBACA;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}, {"offset": {"line": 4032, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/formatStandards.ts"], "sourcesContent": ["// 文档格式标准配置\r\n\r\nexport interface FormatStandard {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  icon: string;\r\n  rules: {\r\n    title: TitleRules;\r\n    content: ContentRules;\r\n    special: SpecialElementRules;\r\n    layout: LayoutRules;\r\n  };\r\n  prompt: string;\r\n}\r\n\r\nexport interface TitleRules {\r\n  h1: string;\r\n  h2: string;\r\n  h3: string;\r\n  h4: string;\r\n  numbering: string;\r\n  spacing: string;\r\n}\r\n\r\nexport interface ContentRules {\r\n  font: string;\r\n  spacing: string;\r\n  paragraph: string;\r\n  emphasis: string;\r\n  mixedText: string;\r\n}\r\n\r\nexport interface SpecialElementRules {\r\n  table: string;\r\n  list: string;\r\n  quote: string;\r\n  code: string;\r\n  image: string;\r\n}\r\n\r\nexport interface LayoutRules {\r\n  margins: string;\r\n  alignment: string;\r\n  pageSetup: string;\r\n  headerFooter: string;\r\n}\r\n\r\nexport const formatStandards: FormatStandard[] = [\r\n  {\r\n    id: 'academic',\r\n    name: '学术论文',\r\n    description: '符合学术规范的严谨格式',\r\n    icon: '🎓',\r\n    rules: {\r\n      title: {\r\n        h1: '一级标题：三号宋体加粗，居中，段前距1.5行，段后距1行',\r\n        h2: '二级标题：四号黑体，左对齐，段前距1行，段后距0.5行',\r\n        h3: '三级标题：小四号楷体加粗，左对齐，段前距0.5行',\r\n        h4: '四级标题：五号宋体加粗，首行缩进2字符',\r\n        numbering: '使用阿拉伯数字编号：1. 1.1 1.1.1',\r\n        spacing: '标题后不加标点符号，与正文间距0.5行'\r\n      },\r\n      content: {\r\n        font: '正文：五号宋体，行间距1.5倍，段间距0.5行',\r\n        spacing: '首行缩进2字符，避免单字成行',\r\n        paragraph: '段落长度控制在3-8行，超过8行建议拆分',\r\n        emphasis: '关键词用【加粗】，重要数据用【加粗+红色】',\r\n        mixedText: '中英文混排：英文用Times New Roman，中英文间空1格'\r\n      },\r\n      special: {\r\n        table: '表格居中，1.5磅实线边框，表头小五号黑体加粗居中，表内小五号宋体',\r\n        list: '有序列表用\"1. 2. 3.\"，无序列表用\"•\"，列表项间距0.5行',\r\n        quote: '引用用【斜体+双引号】，句尾标注来源',\r\n        code: '代码用等宽字体，浅灰背景，单独成段',\r\n        image: '图片居中，宽度不超过页面80%，下方标注图号和标题'\r\n      },\r\n      layout: {\r\n        margins: '页边距：上下2.5cm，左右2cm',\r\n        alignment: '正文两端对齐，标题居中或左对齐',\r\n        pageSetup: 'A4纸张，纵向排版',\r\n        headerFooter: '页眉：文档标题（左）+页码（右），页脚：部门+日期'\r\n      }\r\n    },\r\n    prompt: `请按照学术论文格式标准优化这份文档，具体要求：\r\n\r\n**标题格式：**\r\n- 一级标题：# 使用三号宋体加粗，居中，段前距1.5行\r\n- 二级标题：## 使用四号黑体，左对齐，段前距1行  \r\n- 三级标题：### 使用小四号楷体加粗，左对齐，段前距0.5行\r\n- 标题编号使用阿拉伯数字：1. 1.1 1.1.1\r\n- 标题后不加标点符号\r\n\r\n**正文格式：**\r\n- 使用五号宋体，行间距1.5倍，段间距0.5行\r\n- 首行缩进2字符，段落长度控制在3-8行\r\n- 中英文混排时英文用Times New Roman，中英文间空1格\r\n- 关键词用**加粗**，重要数据用**加粗**标注\r\n\r\n**特殊元素：**\r\n- 表格居中，使用1.5磅实线边框，表头加粗居中\r\n- 有序列表用\"1. 2. 3.\"，无序列表用\"•\"\r\n- 引用用*斜体*加双引号，句尾标注来源\r\n- 图片居中，宽度不超过页面80%，下方标注图号\r\n\r\n**检查要点：**\r\n- 确保格式前后一致，无多余空行空格\r\n- 重点内容突出清晰，层级分明\r\n- 符合学术规范，专业严谨\r\n\r\n请优化后说明主要修改内容。`\r\n  },\r\n  \r\n  {\r\n    id: 'business',\r\n    name: '商务报告',\r\n    description: '专业商务文档格式',\r\n    icon: '💼',\r\n    rules: {\r\n      title: {\r\n        h1: '一级标题：二号微软雅黑加粗，居中，蓝色',\r\n        h2: '二级标题：三号微软雅黑加粗，左对齐，深蓝色',\r\n        h3: '三级标题：四号微软雅黑，左对齐，段前距0.5行',\r\n        h4: '四级标题：小四号微软雅黑，首行缩进2字符',\r\n        numbering: '使用数字编号：1. 1.1 1.1.1',\r\n        spacing: '标题与正文间距1行，突出层级'\r\n      },\r\n      content: {\r\n        font: '正文：小四号微软雅黑，行间距1.25倍',\r\n        spacing: '段间距0.5行，重要段落前后空1行',\r\n        paragraph: '段落简洁明了，每段3-5行',\r\n        emphasis: '关键数据用【加粗+蓝色】，百分比和金额突出显示',\r\n        mixedText: '数字与中文间空1格，如\"增长 25%\"'\r\n      },\r\n      special: {\r\n        table: '表格现代简洁风格，表头深蓝背景白字，数据右对齐',\r\n        list: '使用项目符号\"▪\"或编号，重点用\"★\"标记',\r\n        quote: '客户反馈或数据来源用浅蓝背景框突出',\r\n        code: '技术内容用等宽字体，浅灰背景',\r\n        image: '图表居中，配色统一，下方标注数据来源'\r\n      },\r\n      layout: {\r\n        margins: '页边距：上下2cm，左右2.5cm',\r\n        alignment: '正文左对齐，数据表格居中',\r\n        pageSetup: 'A4纸张，可考虑横向排版（图表较多时）',\r\n        headerFooter: '页眉：公司Logo+报告标题，页脚：页码+机密标识'\r\n      }\r\n    },\r\n    prompt: `请按照商务报告格式标准优化这份文档，具体要求：\r\n\r\n**标题格式：**\r\n- 一级标题：# 使用二号微软雅黑加粗，居中，蓝色\r\n- 二级标题：## 使用三号微软雅黑加粗，左对齐，深蓝色\r\n- 三级标题：### 使用四号微软雅黑，左对齐\r\n- 使用数字编号体系：1. 1.1 1.1.1\r\n\r\n**正文格式：**\r\n- 使用小四号微软雅黑，行间距1.25倍\r\n- 段落简洁明了，每段3-5行\r\n- 关键数据用**加粗**并突出显示\r\n- 数字与中文间空1格，如\"增长 25%\"\r\n\r\n**商务特色：**\r\n- 重要数据用**加粗+突出色**标注\r\n- 表格使用现代简洁风格，表头深色背景\r\n- 使用项目符号\"▪\"，重点用\"★\"标记\r\n- 客户反馈或重要信息用背景框突出\r\n\r\n**专业要求：**\r\n- 格式统一专业，体现商务正式感\r\n- 数据图表清晰易读，配色协调\r\n- 重点信息突出，便于快速浏览\r\n- 符合企业文档规范\r\n\r\n请优化后说明主要修改内容和商务化改进。`\r\n  },\r\n\r\n  {\r\n    id: 'resume',\r\n    name: '简历文档',\r\n    description: '简洁专业的简历格式',\r\n    icon: '📄',\r\n    rules: {\r\n      title: {\r\n        h1: '姓名：一号微软雅黑加粗，居中',\r\n        h2: '模块标题：四号微软雅黑加粗，左对齐，下划线',\r\n        h3: '子项目：小四号微软雅黑加粗',\r\n        h4: '详细信息：五号微软雅黑',\r\n        numbering: '避免使用编号，用分隔线区分模块',\r\n        spacing: '模块间空1.5行，内容紧凑'\r\n      },\r\n      content: {\r\n        font: '正文：小四号微软雅黑，行间距1.15倍',\r\n        spacing: '信息密度适中，避免过于拥挤',\r\n        paragraph: '每项经历2-3行，突出关键成果',\r\n        emphasis: '成果数据用【加粗】，技能用【标签样式】',\r\n        mixedText: '时间格式统一：2020.01-2023.12'\r\n      },\r\n      special: {\r\n        table: '技能表格简洁，无边框，左对齐',\r\n        list: '使用\"•\"符号，成果用\"✓\"标记',\r\n        quote: '自我评价用斜体，简洁有力',\r\n        code: '技术技能用标签形式展示',\r\n        image: '头像圆形，右上角位置，适当大小'\r\n      },\r\n      layout: {\r\n        margins: '页边距：上下1.5cm，左右2cm',\r\n        alignment: '左对齐为主，姓名居中',\r\n        pageSetup: 'A4纸张，尽量控制在1-2页',\r\n        headerFooter: '页脚：联系方式，页码（多页时）'\r\n      }\r\n    },\r\n    prompt: `请按照简历文档格式标准优化这份文档，具体要求：\r\n\r\n**个人信息：**\r\n- 姓名使用一号微软雅黑加粗，居中显示\r\n- 联系方式紧凑排列，使用统一格式\r\n- 避免过多装饰，保持专业简洁\r\n\r\n**模块结构：**\r\n- 模块标题用## 四号微软雅黑加粗，可加下划线\r\n- 模块顺序：个人信息→教育背景→工作经历→项目经验→技能特长\r\n- 模块间空1.5行，内容紧凑有序\r\n\r\n**内容格式：**\r\n- 时间格式统一：2020.01-2023.12\r\n- 每项经历2-3行，突出关键成果和数据\r\n- 成果数据用**加粗**突出\r\n- 使用\"•\"列举要点，\"✓\"标记成就\r\n\r\n**专业要求：**\r\n- 信息密度适中，重点突出\r\n- 技能用标签形式展示\r\n- 避免冗余信息，突出核心竞争力\r\n- 整体布局美观，易于阅读\r\n\r\n**检查要点：**\r\n- 确保信息准确完整\r\n- 格式统一专业\r\n- 重点信息突出\r\n- 控制在1-2页内\r\n\r\n请优化后说明主要调整内容。`\r\n  }\r\n];\r\n\r\n// 获取格式标准\r\nexport const getFormatStandard = (id: string): FormatStandard | undefined => {\r\n  return formatStandards.find(standard => standard.id === id);\r\n};\r\n\r\n// 获取所有格式标准\r\nexport const getAllFormatStandards = (): FormatStandard[] => {\r\n  return formatStandards;\r\n};\r\n\r\n// 格式检查规则\r\nexport const formatCheckRules = {\r\n  title: {\r\n    consistency: '检查标题层级是否一致',\r\n    numbering: '检查编号格式是否统一',\r\n    spacing: '检查标题间距是否合适'\r\n  },\r\n  content: {\r\n    font: '检查字体字号是否统一',\r\n    spacing: '检查行间距段间距是否合适',\r\n    alignment: '检查对齐方式是否正确'\r\n  },\r\n  special: {\r\n    table: '检查表格格式是否规范',\r\n    list: '检查列表格式是否一致',\r\n    emphasis: '检查重点标记是否突出'\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA,WAAW;;;;;;;AAgDJ,MAAM,kBAAoC;IAC/C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;YACL,OAAO;gBACL,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,WAAW;gBACX,SAAS;YACX;YACA,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,WAAW;YACb;YACA,SAAS;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,cAAc;YAChB;QACF;QACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;aA0BA,CAAC;IACZ;IAEA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;YACL,OAAO;gBACL,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,WAAW;gBACX,SAAS;YACX;YACA,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,WAAW;YACb;YACA,SAAS;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,cAAc;YAChB;QACF;QACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;mBA0BM,CAAC;IAClB;IAEA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;YACL,OAAO;gBACL,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,WAAW;gBACX,SAAS;YACX;YACA,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,WAAW;YACb;YACA,SAAS;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,cAAc;YAChB;QACF;QACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aA8BA,CAAC;IACZ;CACD;AAGM,MAAM,oBAAoB,CAAC;IAChC,OAAO,gBAAgB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC1D;AAGO,MAAM,wBAAwB;IACnC,OAAO;AACT;AAGO,MAAM,mBAAmB;IAC9B,OAAO;QACL,aAAa;QACb,WAAW;QACX,SAAS;IACX;IACA,SAAS;QACP,MAAM;QACN,SAAS;QACT,WAAW;IACb;IACA,SAAS;QACP,OAAO;QACP,MAAM;QACN,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 4261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/AIAssistant/PreciseFormatPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { formatStandards, getFormatStandard, FormatStandard } from '@/utils/formatStandards';\r\nimport { aiService } from '@/utils/ai';\r\n\r\ninterface PreciseFormatPanelProps {\r\n  content: string;\r\n  onContentChange: (content: string) => void;\r\n  onClose: () => void;\r\n}\r\n\r\nexport const PreciseFormatPanel: React.FC<PreciseFormatPanelProps> = ({\r\n  content,\r\n  onContentChange,\r\n  onClose\r\n}) => {\r\n  const [selectedFormat, setSelectedFormat] = useState<FormatStandard | null>(null);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [processingStep, setProcessingStep] = useState('');\r\n  const [formatCheckResult, setFormatCheckResult] = useState<string>('');\r\n  const [showFormatCheck, setShowFormatCheck] = useState(false);\r\n\r\n  // 选择格式标准\r\n  const handleFormatSelect = (formatId: string) => {\r\n    const format = getFormatStandard(formatId);\r\n    setSelectedFormat(format || null);\r\n    setShowFormatCheck(false);\r\n    setFormatCheckResult('');\r\n  };\r\n\r\n  // 执行精准格式优化\r\n  const handlePreciseFormat = async () => {\r\n    if (!selectedFormat || !content.trim()) {\r\n      alert('请选择格式标准并确保有内容需要优化');\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n    setProcessingStep('正在准备格式优化...');\r\n\r\n    try {\r\n      // 步骤1：格式检查\r\n      setProcessingStep('正在检查当前格式...');\r\n      const checkResponse = await aiService.checkDocumentFormat(content, selectedFormat.name);\r\n      \r\n      if (checkResponse.success && checkResponse.result) {\r\n        setFormatCheckResult(checkResponse.result);\r\n      }\r\n\r\n      // 步骤2：精准优化\r\n      setProcessingStep(`正在按照${selectedFormat.name}标准优化...`);\r\n      const formatResponse = await aiService.formatDocumentPrecise(\r\n        content, \r\n        selectedFormat.id, \r\n        selectedFormat.prompt\r\n      );\r\n\r\n      if (formatResponse.success && formatResponse.result) {\r\n        setProcessingStep('优化完成！');\r\n        onContentChange(formatResponse.result);\r\n        \r\n        // 显示格式检查结果\r\n        setShowFormatCheck(true);\r\n      } else {\r\n        alert(`格式优化失败: ${formatResponse.error || '未知错误'}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('精准格式优化失败:', error);\r\n      alert('格式优化失败，请重试');\r\n    } finally {\r\n      setIsProcessing(false);\r\n      setProcessingStep('');\r\n    }\r\n  };\r\n\r\n  // 仅执行格式检查\r\n  const handleFormatCheck = async () => {\r\n    if (!selectedFormat || !content.trim()) {\r\n      alert('请选择格式标准并确保有内容需要检查');\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n    setProcessingStep('正在检查文档格式...');\r\n\r\n    try {\r\n      const response = await aiService.checkDocumentFormat(content, selectedFormat.name);\r\n      \r\n      if (response.success && response.result) {\r\n        setFormatCheckResult(response.result);\r\n        setShowFormatCheck(true);\r\n      } else {\r\n        alert(`格式检查失败: ${response.error || '未知错误'}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('格式检查失败:', error);\r\n      alert('格式检查失败，请重试');\r\n    } finally {\r\n      setIsProcessing(false);\r\n      setProcessingStep('');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden\">\r\n        {/* 标题栏 */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\r\n            🎯 精准格式优化\r\n          </h2>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\">\r\n          {/* 功能说明 */}\r\n          <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2\">\r\n              ✨ 精准格式优化特点\r\n            </h3>\r\n            <ul className=\"text-blue-700 dark:text-blue-300 space-y-1 text-sm\">\r\n              <li>• <strong>具体明确</strong>：提供详细的格式规范，避免模糊的\"优化格式\"</li>\r\n              <li>• <strong>标准化</strong>：基于学术、商务、简历等专业标准</li>\r\n              <li>• <strong>可操作</strong>：每个要求都有具体的实施细节</li>\r\n              <li>• <strong>自检验</strong>：AI会自动检查优化结果是否符合标准</li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* 格式标准选择 */}\r\n          <div className=\"mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\r\n              选择格式标准\r\n            </h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              {formatStandards.map((format) => (\r\n                <button\r\n                  key={format.id}\r\n                  onClick={() => handleFormatSelect(format.id)}\r\n                  className={`p-4 rounded-lg border-2 transition-all text-left ${\r\n                    selectedFormat?.id === format.id\r\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\r\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\r\n                  }`}\r\n                >\r\n                  <div className=\"flex items-center gap-3 mb-2\">\r\n                    <span className=\"text-2xl\">{format.icon}</span>\r\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                      {format.name}\r\n                    </h4>\r\n                  </div>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {format.description}\r\n                  </p>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 选中格式的详细规则 */}\r\n          {selectedFormat && (\r\n            <div className=\"mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n              <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\r\n                📋 {selectedFormat.name} 格式规范\r\n              </h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\r\n                <div>\r\n                  <h5 className=\"font-medium text-gray-800 dark:text-gray-200 mb-2\">标题格式</h5>\r\n                  <ul className=\"text-gray-600 dark:text-gray-400 space-y-1\">\r\n                    <li>• {selectedFormat.rules.title.h1}</li>\r\n                    <li>• {selectedFormat.rules.title.h2}</li>\r\n                    <li>• {selectedFormat.rules.title.h3}</li>\r\n                  </ul>\r\n                </div>\r\n                <div>\r\n                  <h5 className=\"font-medium text-gray-800 dark:text-gray-200 mb-2\">正文格式</h5>\r\n                  <ul className=\"text-gray-600 dark:text-gray-400 space-y-1\">\r\n                    <li>• {selectedFormat.rules.content.font}</li>\r\n                    <li>• {selectedFormat.rules.content.spacing}</li>\r\n                    <li>• {selectedFormat.rules.content.emphasis}</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* 处理状态显示 */}\r\n          {isProcessing && (\r\n            <div className=\"mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"></div>\r\n                <span className=\"text-blue-700 dark:text-blue-300 font-medium\">\r\n                  {processingStep}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* 格式检查结果 */}\r\n          {showFormatCheck && formatCheckResult && (\r\n            <div className=\"mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4\">\r\n              <h4 className=\"font-medium text-green-800 dark:text-green-300 mb-2\">\r\n                📊 格式检查结果\r\n              </h4>\r\n              <div className=\"text-sm text-green-700 dark:text-green-300 whitespace-pre-wrap\">\r\n                {formatCheckResult}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* 操作按钮 */}\r\n          <div className=\"flex gap-3\">\r\n            <button\r\n              onClick={handleFormatCheck}\r\n              disabled={!selectedFormat || isProcessing}\r\n              className=\"flex-1 px-4 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white rounded-md transition-colors\"\r\n            >\r\n              仅检查格式\r\n            </button>\r\n            <button\r\n              onClick={handlePreciseFormat}\r\n              disabled={!selectedFormat || isProcessing}\r\n              className=\"flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-md transition-colors\"\r\n            >\r\n              精准优化格式\r\n            </button>\r\n          </div>\r\n\r\n          {/* 使用提示 */}\r\n          <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg\">\r\n            <h4 className=\"font-medium text-yellow-800 dark:text-yellow-300 mb-2\">\r\n              💡 使用提示\r\n            </h4>\r\n            <ul className=\"text-sm text-yellow-700 dark:text-yellow-300 space-y-1\">\r\n              <li>• 选择最符合您文档用途的格式标准</li>\r\n              <li>• 可以先\"仅检查格式\"了解当前问题</li>\r\n              <li>• \"精准优化格式\"会按照标准重新整理文档</li>\r\n              <li>• 优化后会显示详细的修改说明</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYO,MAAM,qBAAwD,CAAC,EACpE,OAAO,EACP,eAAe,EACf,OAAO,EACR;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE;QACjC,kBAAkB,UAAU;QAC5B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,WAAW;IACX,MAAM,sBAAsB;QAC1B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,IAAI,IAAI;YACtC,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,kBAAkB;QAElB,IAAI;YACF,WAAW;YACX,kBAAkB;YAClB,MAAM,gBAAgB,MAAM,kHAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,SAAS,eAAe,IAAI;YAEtF,IAAI,cAAc,OAAO,IAAI,cAAc,MAAM,EAAE;gBACjD,qBAAqB,cAAc,MAAM;YAC3C;YAEA,WAAW;YACX,kBAAkB,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,OAAO,CAAC;YACrD,MAAM,iBAAiB,MAAM,kHAAA,CAAA,YAAS,CAAC,qBAAqB,CAC1D,SACA,eAAe,EAAE,EACjB,eAAe,MAAM;YAGvB,IAAI,eAAe,OAAO,IAAI,eAAe,MAAM,EAAE;gBACnD,kBAAkB;gBAClB,gBAAgB,eAAe,MAAM;gBAErC,WAAW;gBACX,mBAAmB;YACrB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,eAAe,KAAK,IAAI,QAAQ;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,kBAAkB;QACpB;IACF;IAEA,UAAU;IACV,MAAM,oBAAoB;QACxB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,IAAI,IAAI;YACtC,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,SAAS,eAAe,IAAI;YAEjF,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM,EAAE;gBACvC,qBAAqB,SAAS,MAAM;gBACpC,mBAAmB;YACrB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,SAAS,KAAK,IAAI,QAAQ;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,kBAAkB;QACpB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAa;;;;;;;sDAC3B,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAY;;;;;;;sDAC1B,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAY;;;;;;;sDAC1B,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;sCAK9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;8CACZ,+HAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,uBACpB,8OAAC;4CAEC,SAAS,IAAM,mBAAmB,OAAO,EAAE;4CAC3C,WAAW,CAAC,iDAAiD,EAC3D,gBAAgB,OAAO,OAAO,EAAE,GAC5B,mDACA,yFACJ;;8DAEF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAY,OAAO,IAAI;;;;;;sEACvC,8OAAC;4DAAG,WAAU;sEACX,OAAO,IAAI;;;;;;;;;;;;8DAGhB,8OAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;2CAfhB,OAAO,EAAE;;;;;;;;;;;;;;;;wBAuBrB,gCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAiD;wCACzD,eAAe,IAAI;wCAAC;;;;;;;8CAE1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;gEAAG;gEAAG,eAAe,KAAK,CAAC,KAAK,CAAC,EAAE;;;;;;;sEACpC,8OAAC;;gEAAG;gEAAG,eAAe,KAAK,CAAC,KAAK,CAAC,EAAE;;;;;;;sEACpC,8OAAC;;gEAAG;gEAAG,eAAe,KAAK,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;;sDAGxC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;gEAAG;gEAAG,eAAe,KAAK,CAAC,OAAO,CAAC,IAAI;;;;;;;sEACxC,8OAAC;;gEAAG;gEAAG,eAAe,KAAK,CAAC,OAAO,CAAC,OAAO;;;;;;;sEAC3C,8OAAC;;gEAAG;gEAAG,eAAe,KAAK,CAAC,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQrD,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;;wBAOR,mBAAmB,mCAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;sCAMP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,kBAAkB;oCAC7B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,kBAAkB;oCAC7B,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 4859, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/AIAssistant/AIPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { aiService, AIConfig, defaultAIConfig } from '@/utils/ai';\r\nimport { dbManager, AIModel, initializeDefaultModels } from '@/utils/indexedDB';\r\nimport { ProcessingSteps } from './ProcessingSteps';\r\nimport { PreciseFormatPanel } from './PreciseFormatPanel';\r\n\r\n// AIModel接口现在从indexedDB导入\r\n\r\ninterface AIPanelProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  content: string;\r\n  onContentChange: (newContent: string) => void;\r\n}\r\n\r\nexport const AIPanel: React.FC<AIPanelProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  content,\r\n  onContentChange,\r\n}) => {\r\n  const [isConfigured, setIsConfigured] = useState(false);\r\n  const [showConfig, setShowConfig] = useState(false);\r\n  const [modelTypeFilter, setModelTypeFilter] = useState<'all' | 'text' | 'image' | 'multimodal'>('all');\r\n  const [showModelManager, setShowModelManager] = useState(false);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [processingStep, setProcessingStep] = useState('');\r\n  const [processingProgress, setProcessingProgress] = useState(0);\r\n  const [showPreciseFormat, setShowPreciseFormat] = useState(false);\r\n  const [models, setModels] = useState<AIModel[]>([]);\r\n  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);\r\n  const [newModel, setNewModel] = useState({\r\n    name: '',\r\n    api_key: '',\r\n    base_url: '',\r\n    model_name: '',\r\n    model_type: 'text' as 'text' | 'image' | 'multimodal',\r\n    temperature: 0.7,\r\n    max_tokens: 2000,\r\n    description: '',\r\n  });\r\n\r\n  // 加载AI模型列表\r\n  const loadModels = async () => {\r\n    try {\r\n      await initializeDefaultModels(); // 确保默认模型已初始化\r\n      const models = await dbManager.getAllAIModels();\r\n      setModels(models);\r\n\r\n      // 找到默认模型并初始化\r\n      const defaultModel = models.find((model: AIModel) => model.is_default);\r\n      if (defaultModel && defaultModel.api_key) {\r\n        setSelectedModel(defaultModel);\r\n        const config: AIConfig = {\r\n          apiKey: defaultModel.api_key,\r\n          baseURL: defaultModel.base_url,\r\n          model: defaultModel.model_name,\r\n          temperature: defaultModel.temperature,\r\n          maxTokens: defaultModel.max_tokens,\r\n        };\r\n        const initialized = aiService.initialize(config);\r\n        setIsConfigured(initialized);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load AI models:', error);\r\n    }\r\n  };\r\n\r\n  // 初始化时加载模型\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      loadModels();\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // 选择模型\r\n  const handleSelectModel = async (model: AIModel) => {\r\n    if (!model.api_key) {\r\n      alert('该模型未配置API密钥，请先编辑模型');\r\n      return;\r\n    }\r\n\r\n    const config: AIConfig = {\r\n      apiKey: model.api_key,\r\n      baseURL: model.base_url,\r\n      model: model.model_name,\r\n      temperature: model.temperature,\r\n      maxTokens: model.max_tokens,\r\n    };\r\n\r\n    const success = aiService.initialize(config);\r\n    if (success) {\r\n      setSelectedModel(model);\r\n      setIsConfigured(true);\r\n      setShowConfig(false);\r\n\r\n      // 设置为默认模型\r\n      try {\r\n        if (model.id) {\r\n          await dbManager.setDefaultAIModel(model.id);\r\n          await loadModels(); // 重新加载模型列表\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to set default model:', error);\r\n      }\r\n    } else {\r\n      alert('模型初始化失败，请检查配置');\r\n    }\r\n  };\r\n\r\n  // 保存新模型\r\n  const handleSaveNewModel = async () => {\r\n    if (!newModel.name || !newModel.api_key || !newModel.model_name) {\r\n      alert('请填写必要字段：名称、API密钥、模型名称');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await dbManager.createAIModel(newModel);\r\n      await loadModels();\r\n      setNewModel({\r\n        name: '',\r\n        api_key: '',\r\n        base_url: '',\r\n        model_name: '',\r\n        model_type: 'text',\r\n        temperature: 0.7,\r\n        max_tokens: 2000,\r\n        description: '',\r\n      });\r\n      setShowModelManager(false);\r\n      alert('模型保存成功');\r\n    } catch (error) {\r\n      console.error('Failed to save model:', error);\r\n      alert(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);\r\n    }\r\n  };\r\n\r\n  // 删除模型\r\n  const handleDeleteModel = async (modelId: number) => {\r\n    if (!confirm('确定要删除这个模型吗？')) return;\r\n\r\n    try {\r\n      await dbManager.deleteAIModel(modelId);\r\n      await loadModels();\r\n      if (selectedModel?.id === modelId) {\r\n        setSelectedModel(null);\r\n        setIsConfigured(false);\r\n      }\r\n      alert('模型删除成功');\r\n    } catch (error) {\r\n      console.error('Failed to delete model:', error);\r\n      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);\r\n    }\r\n  };\r\n\r\n  // AI处理函数\r\n  const handleAIAction = async (action: string) => {\r\n    if (!isConfigured) {\r\n      setShowConfig(true);\r\n      return;\r\n    }\r\n\r\n    if (!content.trim()) {\r\n      alert('请先输入一些内容');\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n    setProcessingProgress(0);\r\n    setProcessingStep('正在初始化AI请求...');\r\n    \r\n    try {\r\n      // 步骤1：准备请求\r\n      setProcessingStep('正在准备AI请求...');\r\n      setProcessingProgress(10);\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n      // 步骤2：分析内容\r\n      setProcessingStep('正在分析文档内容...');\r\n      setProcessingProgress(25);\r\n      await new Promise(resolve => setTimeout(resolve, 400));\r\n\r\n      let response;\r\n\r\n      // 步骤3：执行AI操作\r\n      const actionNames = {\r\n        'grammar': '语法检查',\r\n        'style-formal': '正式风格调整',\r\n        'style-casual': '轻松风格调整',\r\n        'expand': '内容扩展',\r\n        'summarize': '内容总结',\r\n        'translate-en': '翻译为英文',\r\n        'translate-zh': '翻译为中文',\r\n        'format': '格式优化'\r\n      };\r\n\r\n      setProcessingStep(`正在执行${actionNames[action as keyof typeof actionNames] || 'AI处理'}...`);\r\n      setProcessingProgress(50);\r\n\r\n      switch (action) {\r\n        case 'grammar':\r\n          response = await aiService.checkGrammar(content);\r\n          break;\r\n        case 'style-formal':\r\n          response = await aiService.adjustStyle(content, '正式');\r\n          break;\r\n        case 'style-casual':\r\n          response = await aiService.adjustStyle(content, '轻松');\r\n          break;\r\n        case 'expand':\r\n          response = await aiService.expandContent(content);\r\n          break;\r\n        case 'summarize':\r\n          response = await aiService.summarizeContent(content);\r\n          break;\r\n        case 'translate-en':\r\n          response = await aiService.translateContent(content, '英文');\r\n          break;\r\n        case 'translate-zh':\r\n          response = await aiService.translateContent(content, '中文');\r\n          break;\r\n        case 'format':\r\n          response = await aiService.formatDocument(content);\r\n          break;\r\n        default:\r\n          throw new Error('未知的AI操作');\r\n      }\r\n\r\n      // 步骤4：处理AI响应\r\n      setProcessingStep('正在处理AI返回结果...');\r\n      setProcessingProgress(80);\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n      if (response.success && response.result) {\r\n        // 步骤5：应用结果\r\n        setProcessingStep('正在应用处理结果...');\r\n        setProcessingProgress(95);\r\n        await new Promise(resolve => setTimeout(resolve, 200));\r\n\r\n        onContentChange(response.result);\r\n\r\n        // 完成\r\n        setProcessingStep('处理完成！');\r\n        setProcessingProgress(100);\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n      } else {\r\n        setProcessingStep('处理失败');\r\n        alert(`AI处理失败: ${response.error || '未知错误'}`);\r\n      }\r\n    } catch (error) {\r\n      setProcessingStep('处理出错');\r\n      alert(`AI处理失败: ${error instanceof Error ? error.message : '未知错误'}`);\r\n    } finally {\r\n      setIsProcessing(false);\r\n      setProcessingStep('');\r\n      setProcessingProgress(0);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-96 max-w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex justify-between items-center mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              AI助手\r\n            </h3>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n            >\r\n              ×\r\n            </button>\r\n          </div>\r\n\r\n          {showModelManager ? (\r\n            // 模型管理界面\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">AI模型管理</h4>\r\n                <button\r\n                  onClick={() => setShowModelManager(false)}\r\n                  className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n                >\r\n                  返回\r\n                </button>\r\n              </div>\r\n\r\n              {/* 现有模型列表 */}\r\n              <div className=\"max-h-40 overflow-y-auto space-y-2\">\r\n                {models.map((model) => (\r\n                  <div\r\n                    key={model.id}\r\n                    className={`p-3 border rounded-lg ${\r\n                      model.is_default\r\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\r\n                        : 'border-gray-200 dark:border-gray-600'\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <div className=\"font-medium text-sm\">{model.name}</div>\r\n                        <div className=\"text-xs text-gray-500\">{model.model_name}</div>\r\n                        {model.description && (\r\n                          <div className=\"text-xs text-gray-400 mt-1\">{model.description}</div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {model.is_default && (\r\n                          <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\r\n                            默认\r\n                          </span>\r\n                        )}\r\n                        <button\r\n                          onClick={() => handleSelectModel(model)}\r\n                          className=\"text-xs bg-green-100 hover:bg-green-200 text-green-800 px-2 py-1 rounded\"\r\n                        >\r\n                          使用\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDeleteModel(model.id)}\r\n                          className=\"text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded\"\r\n                        >\r\n                          删除\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              {/* 添加新模型表单 */}\r\n              <div className=\"border-t pt-4\">\r\n                <h5 className=\"font-medium text-gray-900 dark:text-gray-100 mb-3\">添加新模型</h5>\r\n                <div className=\"space-y-3\">\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"模型名称\"\r\n                    value={newModel.name}\r\n                    onChange={(e) => setNewModel({ ...newModel, name: e.target.value })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm\"\r\n                  />\r\n                  <input\r\n                    type=\"password\"\r\n                    placeholder=\"API密钥\"\r\n                    value={newModel.api_key}\r\n                    onChange={(e) => setNewModel({ ...newModel, api_key: e.target.value })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm\"\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"API基础URL（可选）\"\r\n                    value={newModel.base_url}\r\n                    onChange={(e) => setNewModel({ ...newModel, base_url: e.target.value })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm\"\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"模型名称（如：gpt-3.5-turbo）\"\r\n                    value={newModel.model_name}\r\n                    onChange={(e) => setNewModel({ ...newModel, model_name: e.target.value })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm\"\r\n                  />\r\n                  <select\r\n                    value={newModel.model_type}\r\n                    onChange={(e) => setNewModel({ ...newModel, model_type: e.target.value as 'text' | 'image' | 'multimodal' })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm\"\r\n                  >\r\n                    <option value=\"text\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">文本模型</option>\r\n                    <option value=\"image\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">图像模型</option>\r\n                    <option value=\"multimodal\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">多模态模型</option>\r\n                  </select>\r\n                  <textarea\r\n                    placeholder=\"描述（可选）\"\r\n                    value={newModel.description}\r\n                    onChange={(e) => setNewModel({ ...newModel, description: e.target.value })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm\"\r\n                    rows={2}\r\n                  />\r\n                  <button\r\n                    onClick={handleSaveNewModel}\r\n                    className=\"w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors text-sm\"\r\n                  >\r\n                    保存模型\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : !isConfigured || showConfig ? (\r\n            // 模型选择界面\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">选择AI模型</h4>\r\n                <button\r\n                  onClick={() => setShowModelManager(true)}\r\n                  className=\"text-sm text-blue-500 hover:text-blue-700\"\r\n                >\r\n                  管理模型\r\n                </button>\r\n              </div>\r\n\r\n              {/* 模型类型筛选 */}\r\n              <div className=\"flex items-center gap-2\">\r\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">类型:</span>\r\n                <select\r\n                  value={modelTypeFilter}\r\n                  onChange={(e) => setModelTypeFilter(e.target.value as any)}\r\n                  className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                >\r\n                  <option value=\"all\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">全部</option>\r\n                  <option value=\"text\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">文本模型</option>\r\n                  <option value=\"image\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">图像模型</option>\r\n                  <option value=\"multimodal\" className=\"text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700\">多模态模型</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"space-y-3\">\r\n                {(() => {\r\n                  const filteredModels = modelTypeFilter === 'all'\r\n                    ? models\r\n                    : models.filter(model => model.model_type === modelTypeFilter);\r\n\r\n                  return filteredModels.length === 0 ? (\r\n                    <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\r\n                      <p>{modelTypeFilter === 'all' ? '暂无可用模型' : `暂无${modelTypeFilter === 'text' ? '文本' : modelTypeFilter === 'image' ? '图像' : '多模态'}模型`}</p>\r\n                      <button\r\n                        onClick={() => setShowModelManager(true)}\r\n                        className=\"mt-2 text-blue-500 hover:text-blue-700 underline\"\r\n                      >\r\n                        添加模型\r\n                      </button>\r\n                    </div>\r\n                  ) : (\r\n                    filteredModels.map((model) => (\r\n                    <div\r\n                      key={model.id}\r\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\r\n                        selectedModel?.id === model.id\r\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\r\n                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\r\n                      }`}\r\n                      onClick={() => handleSelectModel(model)}\r\n                    >\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div>\r\n                          <div className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                            {model.name}\r\n                          </div>\r\n                          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                            {model.model_name}\r\n                          </div>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <span className={`text-xs px-2 py-1 rounded ${\r\n                              model.model_type === 'text'\r\n                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'\r\n                                : model.model_type === 'image'\r\n                                ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'\r\n                                : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'\r\n                            }`}>\r\n                              {model.model_type === 'text' ? '文本' : model.model_type === 'image' ? '图像' : '多模态'}\r\n                            </span>\r\n                          </div>\r\n                          {model.description && (\r\n                            <div className=\"text-xs text-gray-400 mt-1\">\r\n                              {model.description}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          {model.is_default && (\r\n                            <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\r\n                              默认\r\n                            </span>\r\n                          )}\r\n                          {!model.api_key && (\r\n                            <span className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\">\r\n                              未配置\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                  );\r\n                })()}\r\n              </div>\r\n\r\n              {selectedModel && (\r\n                <div className=\"pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    当前选择: {selectedModel.name}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            // AI功能界面\r\n            <div className=\"space-y-4\">\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                选择AI功能来优化您的文档：\r\n              </div>\r\n\r\n              {/* 处理过程显示 */}\r\n              <ProcessingSteps\r\n                currentStep={processingStep}\r\n                progress={processingProgress}\r\n                isVisible={isProcessing}\r\n              />\r\n\r\n              {/* 文档优化 */}\r\n              <div>\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">文档优化</h4>\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  <button\r\n                    onClick={() => handleAIAction('grammar')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-blue-700 dark:text-blue-300\">语法检查</div>\r\n                    <div className=\"text-xs text-blue-600 dark:text-blue-400\">修正语法错误</div>\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => handleAIAction('format')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-green-700 dark:text-green-300\">格式优化</div>\r\n                    <div className=\"text-xs text-green-600 dark:text-green-400\">改善文档结构</div>\r\n                  </button>\r\n                </div>\r\n\r\n                {/* 精准格式优化 */}\r\n                <div className=\"mt-3\">\r\n                  <button\r\n                    onClick={() => setShowPreciseFormat(true)}\r\n                    disabled={isProcessing}\r\n                    className=\"w-full p-3 text-left bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 hover:from-purple-100 hover:to-pink-100 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30 rounded-lg transition-colors disabled:opacity-50 border border-purple-200 dark:border-purple-700\"\r\n                  >\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span className=\"text-lg\">🎯</span>\r\n                      <div>\r\n                        <div className=\"font-medium text-purple-700 dark:text-purple-300\">精准格式优化</div>\r\n                        <div className=\"text-xs text-purple-600 dark:text-purple-400\">专业标准，具体明确，达到完美格式</div>\r\n                      </div>\r\n                    </div>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 风格调整 */}\r\n              <div>\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">风格调整</h4>\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  <button\r\n                    onClick={() => handleAIAction('style-formal')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-purple-700 dark:text-purple-300\">正式风格</div>\r\n                    <div className=\"text-xs text-purple-600 dark:text-purple-400\">商务正式</div>\r\n                  </button>\r\n                  \r\n                  <button\r\n                    onClick={() => handleAIAction('style-casual')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-orange-700 dark:text-orange-300\">轻松风格</div>\r\n                    <div className=\"text-xs text-orange-600 dark:text-orange-400\">友好亲切</div>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 内容处理 */}\r\n              <div>\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">内容处理</h4>\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  <button\r\n                    onClick={() => handleAIAction('expand')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-indigo-700 dark:text-indigo-300\">内容扩展</div>\r\n                    <div className=\"text-xs text-indigo-600 dark:text-indigo-400\">添加更多细节</div>\r\n                  </button>\r\n                  \r\n                  <button\r\n                    onClick={() => handleAIAction('summarize')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-teal-50 dark:bg-teal-900/20 hover:bg-teal-100 dark:hover:bg-teal-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-teal-700 dark:text-teal-300\">内容总结</div>\r\n                    <div className=\"text-xs text-teal-600 dark:text-teal-400\">提取关键要点</div>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 翻译 */}\r\n              <div>\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">翻译</h4>\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  <button\r\n                    onClick={() => handleAIAction('translate-en')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-red-700 dark:text-red-300\">翻译为英文</div>\r\n                    <div className=\"text-xs text-red-600 dark:text-red-400\">English</div>\r\n                  </button>\r\n                  \r\n                  <button\r\n                    onClick={() => handleAIAction('translate-zh')}\r\n                    disabled={isProcessing}\r\n                    className=\"p-3 text-left bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg transition-colors disabled:opacity-50\"\r\n                  >\r\n                    <div className=\"font-medium text-yellow-700 dark:text-yellow-300\">翻译为中文</div>\r\n                    <div className=\"text-xs text-yellow-600 dark:text-yellow-400\">中文</div>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {isProcessing && (\r\n                <div className=\"text-center py-4\">\r\n                  <div className=\"text-blue-500\">AI正在处理中...</div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"pt-4 border-t border-gray-200 dark:border-gray-600 flex justify-between\">\r\n                <button\r\n                  onClick={() => setShowConfig(true)}\r\n                  className=\"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n                >\r\n                  重新选择模型\r\n                </button>\r\n                <button\r\n                  onClick={() => setShowModelManager(true)}\r\n                  className=\"text-sm text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300\"\r\n                >\r\n                  管理模型\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 精准格式优化面板 */}\r\n      {showPreciseFormat && (\r\n        <PreciseFormatPanel\r\n          content={content}\r\n          onContentChange={onContentChange}\r\n          onClose={() => setShowPreciseFormat(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAiBO,MAAM,UAAkC,CAAC,EAC9C,MAAM,EACN,OAAO,EACP,OAAO,EACP,eAAe,EAChB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2C;IAChG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,aAAa;IACf;IAEA,WAAW;IACX,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,0BAAuB,AAAD,KAAK,aAAa;YAC9C,MAAM,SAAS,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc;YAC7C,UAAU;YAEV,aAAa;YACb,MAAM,eAAe,OAAO,IAAI,CAAC,CAAC,QAAmB,MAAM,UAAU;YACrE,IAAI,gBAAgB,aAAa,OAAO,EAAE;gBACxC,iBAAiB;gBACjB,MAAM,SAAmB;oBACvB,QAAQ,aAAa,OAAO;oBAC5B,SAAS,aAAa,QAAQ;oBAC9B,OAAO,aAAa,UAAU;oBAC9B,aAAa,aAAa,WAAW;oBACrC,WAAW,aAAa,UAAU;gBACpC;gBACA,MAAM,cAAc,kHAAA,CAAA,YAAS,CAAC,UAAU,CAAC;gBACzC,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,MAAM;YACN;QACF;QAEA,MAAM,SAAmB;YACvB,QAAQ,MAAM,OAAO;YACrB,SAAS,MAAM,QAAQ;YACvB,OAAO,MAAM,UAAU;YACvB,aAAa,MAAM,WAAW;YAC9B,WAAW,MAAM,UAAU;QAC7B;QAEA,MAAM,UAAU,kHAAA,CAAA,YAAS,CAAC,UAAU,CAAC;QACrC,IAAI,SAAS;YACX,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YAEd,UAAU;YACV,IAAI;gBACF,IAAI,MAAM,EAAE,EAAE;oBACZ,MAAM,yHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE;oBAC1C,MAAM,cAAc,WAAW;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF,OAAO;YACL,MAAM;QACR;IACF;IAEA,QAAQ;IACR,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,UAAU,EAAE;YAC/D,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;YAC9B,MAAM;YACN,YAAY;gBACV,MAAM;gBACN,SAAS;gBACT,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,YAAY;gBACZ,aAAa;YACf;YACA,oBAAoB;YACpB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,gBAAgB;QAE7B,IAAI;YACF,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;YAC9B,MAAM;YACN,IAAI,eAAe,OAAO,SAAS;gBACjC,iBAAiB;gBACjB,gBAAgB;YAClB;YACA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,cAAc;YACjB,cAAc;YACd;QACF;QAEA,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,sBAAsB;QACtB,kBAAkB;QAElB,IAAI;YACF,WAAW;YACX,kBAAkB;YAClB,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,WAAW;YACX,kBAAkB;YAClB,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI;YAEJ,aAAa;YACb,MAAM,cAAc;gBAClB,WAAW;gBACX,gBAAgB;gBAChB,gBAAgB;gBAChB,UAAU;gBACV,aAAa;gBACb,gBAAgB;gBAChB,gBAAgB;gBAChB,UAAU;YACZ;YAEA,kBAAkB,CAAC,IAAI,EAAE,WAAW,CAAC,OAAmC,IAAI,OAAO,GAAG,CAAC;YACvF,sBAAsB;YAEtB,OAAQ;gBACN,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,YAAY,CAAC;oBACxC;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,WAAW,CAAC,SAAS;oBAChD;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,WAAW,CAAC,SAAS;oBAChD;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;oBACzC;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;oBAC5C;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,SAAS;oBACrD;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,SAAS;oBACrD;gBACF,KAAK;oBACH,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;oBAC1C;gBACF;oBACE,MAAM,IAAI,MAAM;YACpB;YAEA,aAAa;YACb,kBAAkB;YAClB,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM,EAAE;gBACvC,WAAW;gBACX,kBAAkB;gBAClB,sBAAsB;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,gBAAgB,SAAS,MAAM;gBAE/B,KAAK;gBACL,kBAAkB;gBAClB,sBAAsB;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD,OAAO;gBACL,kBAAkB;gBAClB,MAAM,CAAC,QAAQ,EAAE,SAAS,KAAK,IAAI,QAAQ;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,kBAAkB;YAClB,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACpE,SAAU;YACR,gBAAgB;YAChB,kBAAkB;YAClB,sBAAsB;QACxB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAKF,mBACC,SAAS;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;4CAEC,WAAW,CAAC,sBAAsB,EAChC,MAAM,UAAU,GACZ,mDACA,wCACJ;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAuB,MAAM,IAAI;;;;;;0EAChD,8OAAC;gEAAI,WAAU;0EAAyB,MAAM,UAAU;;;;;;4DACvD,MAAM,WAAW,kBAChB,8OAAC;gEAAI,WAAU;0EAA8B,MAAM,WAAW;;;;;;;;;;;;kEAGlE,8OAAC;wDAAI,WAAU;;4DACZ,MAAM,UAAU,kBACf,8OAAC;gEAAK,WAAU;0EAAsD;;;;;;0EAIxE,8OAAC;gEACC,SAAS,IAAM,kBAAkB;gEACjC,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEACC,SAAS,IAAM,kBAAkB,MAAM,EAAE;gEACzC,WAAU;0EACX;;;;;;;;;;;;;;;;;;2CA9BA,MAAM,EAAE;;;;;;;;;;8CAwCnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACrE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACvE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAoC;oDAC1G,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;4DAAO,WAAU;sEAA6D;;;;;;sEAC5F,8OAAC;4DAAO,OAAM;4DAAQ,WAAU;sEAA6D;;;;;;sEAC7F,8OAAC;4DAAO,OAAM;4DAAa,WAAU;sEAA6D;;;;;;;;;;;;8DAEpG,8OAAC;oDACC,aAAY;oDACZ,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACxE,WAAU;oDACV,MAAM;;;;;;8DAER,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;uEAML,CAAC,gBAAgB,aACnB,SAAS;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;sDAC3D,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;oDAAM,WAAU;8DAA6D;;;;;;8DAC3F,8OAAC;oDAAO,OAAM;oDAAO,WAAU;8DAA6D;;;;;;8DAC5F,8OAAC;oDAAO,OAAM;oDAAQ,WAAU;8DAA6D;;;;;;8DAC7F,8OAAC;oDAAO,OAAM;oDAAa,WAAU;8DAA6D;;;;;;;;;;;;;;;;;;8CAItG,8OAAC;oCAAI,WAAU;8CACZ,CAAC;wCACA,MAAM,iBAAiB,oBAAoB,QACvC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,KAAK;wCAEhD,OAAO,eAAe,MAAM,KAAK,kBAC/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,oBAAoB,QAAQ,WAAW,CAAC,EAAE,EAAE,oBAAoB,SAAS,OAAO,oBAAoB,UAAU,OAAO,MAAM,EAAE,CAAC;;;;;;8DAClI,8OAAC;oDACC,SAAS,IAAM,oBAAoB;oDACnC,WAAU;8DACX;;;;;;;;;;;uFAKH,eAAe,GAAG,CAAC,CAAC,sBACpB,8OAAC;gDAEC,WAAW,CAAC,uDAAuD,EACjE,eAAe,OAAO,MAAM,EAAE,GAC1B,mDACA,yFACJ;gDACF,SAAS,IAAM,kBAAkB;0DAEjC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EACZ,MAAM,IAAI;;;;;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,MAAM,UAAU;;;;;;8EAEnB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAW,CAAC,0BAA0B,EAC1C,MAAM,UAAU,KAAK,SACjB,qEACA,MAAM,UAAU,KAAK,UACrB,6EACA,wEACJ;kFACC,MAAM,UAAU,KAAK,SAAS,OAAO,MAAM,UAAU,KAAK,UAAU,OAAO;;;;;;;;;;;gEAG/E,MAAM,WAAW,kBAChB,8OAAC;oEAAI,WAAU;8EACZ,MAAM,WAAW;;;;;;;;;;;;sEAIxB,8OAAC;4DAAI,WAAU;;gEACZ,MAAM,UAAU,kBACf,8OAAC;oEAAK,WAAU;8EAAsD;;;;;;gEAIvE,CAAC,MAAM,OAAO,kBACb,8OAAC;oEAAK,WAAU;8EAAoD;;;;;;;;;;;;;;;;;;+CAxCrE,MAAM,EAAE;;;;;oCAiDnB,CAAC;;;;;;gCAGF,+BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CAA2C;4CACjD,cAAc,IAAI;;;;;;;;;;;;;;;;;uEAMjC,SAAS;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgD;;;;;;8CAK/D,8OAAC,oJAAA,CAAA,kBAAe;oCACd,aAAa;oCACb,UAAU;oCACV,WAAW;;;;;;8CAIb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAG5D,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAAiD;;;;;;sEAChE,8OAAC;4DAAI,WAAU;sEAA6C;;;;;;;;;;;;;;;;;;sDAKhE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS,IAAM,qBAAqB;gDACpC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAmD;;;;;;8EAClE,8OAAC;oEAAI,WAAU;8EAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQxE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAAmD;;;;;;sEAClE,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;;;;;;;8DAGhE,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAAmD;;;;;;sEAClE,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;;;;;;;;;;;;;;;;;;;8CAMpE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAAmD;;;;;;sEAClE,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;;;;;;;8DAGhE,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;8CAMhE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAA6C;;;;;;sEAC5D,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;;;;;;;8DAG1D,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAAmD;;;;;;sEAClE,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;;;;;;;;;;;;;;;;;;;gCAKnE,8BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;8CAInC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,mCACC,8OAAC,uJAAA,CAAA,qBAAkB;gBACjB,SAAS;gBACT,iBAAiB;gBACjB,SAAS,IAAM,qBAAqB;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 6111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/AIAssistant/index.ts"], "sourcesContent": ["export { AIPanel } from './AIPanel';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 6125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/TranslateWidget.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\n\r\n// 声明全局translate对象\r\ndeclare global {\r\n  interface Window {\r\n    translate: {\r\n      changeLanguage: (language: string) => void;\r\n      execute: () => void;\r\n      ignore: {\r\n        tag: string[];\r\n        class: string[];\r\n      };\r\n      setUseVersion2: () => void;\r\n      setAutoDiscriminateLocalLanguage: () => void;\r\n    };\r\n  }\r\n}\r\n\r\ninterface TranslateWidgetProps {\r\n  className?: string;\r\n}\r\n\r\nexport const TranslateWidget: React.FC<TranslateWidgetProps> = ({ className = '' }) => {\r\n  const [isTranslateReady, setIsTranslateReady] = useState(false);\r\n  const [currentLanguage, setCurrentLanguage] = useState('chinese_simplified');\r\n\r\n  // 检查translate.js是否加载完成\r\n  useEffect(() => {\r\n    const checkTranslate = () => {\r\n      if (typeof window !== 'undefined' && window.translate) {\r\n        setIsTranslateReady(true);\r\n      } else {\r\n        setTimeout(checkTranslate, 100);\r\n      }\r\n    };\r\n    \r\n    checkTranslate();\r\n  }, []);\r\n\r\n  // 语言选项\r\n  const languages = [\r\n    { code: 'chinese_simplified', name: '简体中文', flag: '🇨🇳' },\r\n    { code: 'chinese_traditional', name: '繁體中文', flag: '🇹🇼' },\r\n    { code: 'english', name: 'English', flag: '🇺🇸' },\r\n    { code: 'japanese', name: '日本語', flag: '🇯🇵' },\r\n    { code: 'korean', name: '한국어', flag: '🇰🇷' },\r\n    { code: 'french', name: 'Français', flag: '🇫🇷' },\r\n    { code: 'german', name: 'Deutsch', flag: '🇩🇪' },\r\n    { code: 'spanish', name: 'Español', flag: '🇪🇸' },\r\n    { code: 'russian', name: 'Русский', flag: '🇷🇺' },\r\n    { code: 'arabic', name: 'العربية', flag: '🇸🇦' },\r\n  ];\r\n\r\n  const handleLanguageChange = (languageCode: string) => {\r\n    if (isTranslateReady && window.translate) {\r\n      window.translate.changeLanguage(languageCode);\r\n      setCurrentLanguage(languageCode);\r\n      \r\n      // 保存用户选择的语言\r\n      try {\r\n        localStorage.setItem('martetdown-translate-language', languageCode);\r\n      } catch (error) {\r\n        console.error('Failed to save translate language:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // 加载保存的语言设置\r\n  useEffect(() => {\r\n    if (isTranslateReady) {\r\n      try {\r\n        const savedLanguage = localStorage.getItem('martetdown-translate-language');\r\n        if (savedLanguage) {\r\n          setCurrentLanguage(savedLanguage);\r\n          window.translate.changeLanguage(savedLanguage);\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load translate language:', error);\r\n      }\r\n    }\r\n  }, [isTranslateReady]);\r\n\r\n  if (!isTranslateReady) {\r\n    return null;\r\n  }\r\n\r\n  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];\r\n\r\n  return (\r\n    <div className={`relative inline-block ${className}`}>\r\n      <div className=\"group\">\r\n        <button\r\n          className=\"flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors\"\r\n          title=\"页面翻译\"\r\n        >\r\n          <span>{currentLang.flag}</span>\r\n          <span className=\"hidden sm:inline\">{currentLang.name}</span>\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n          </svg>\r\n        </button>\r\n\r\n        {/* 下拉菜单 */}\r\n        <div className=\"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\r\n          <div className=\"py-1\">\r\n            <div className=\"px-3 py-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-600\">\r\n              选择翻译语言\r\n            </div>\r\n            {languages.map((language) => (\r\n              <button\r\n                key={language.code}\r\n                onClick={() => handleLanguageChange(language.code)}\r\n                className={`w-full flex items-center gap-3 px-3 py-2 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${\r\n                  currentLanguage === language.code \r\n                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' \r\n                    : 'text-gray-700 dark:text-gray-300'\r\n                }`}\r\n              >\r\n                <span>{language.flag}</span>\r\n                <span>{language.name}</span>\r\n                {currentLanguage === language.code && (\r\n                  <svg className=\"w-4 h-4 ml-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                )}\r\n              </button>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"border-t border-gray-200 dark:border-gray-600 px-3 py-2\">\r\n            <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              💡 翻译功能会自动翻译页面内容，代码和编辑器内容不会被翻译\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// 翻译工具函数\r\nexport const translateUtils = {\r\n  // 手动触发翻译（用于动态内容）\r\n  retranslate: () => {\r\n    if (typeof window !== 'undefined' && window.translate) {\r\n      window.translate.execute();\r\n    }\r\n  },\r\n  \r\n  // 切换到指定语言\r\n  changeLanguage: (languageCode: string) => {\r\n    if (typeof window !== 'undefined' && window.translate) {\r\n      window.translate.changeLanguage(languageCode);\r\n    }\r\n  },\r\n  \r\n  // 添加忽略翻译的类名\r\n  addIgnoreClass: (className: string) => {\r\n    if (typeof window !== 'undefined' && window.translate) {\r\n      if (!window.translate.ignore.class.includes(className)) {\r\n        window.translate.ignore.class.push(className);\r\n      }\r\n    }\r\n  },\r\n  \r\n  // 添加忽略翻译的标签\r\n  addIgnoreTag: (tagName: string) => {\r\n    if (typeof window !== 'undefined' && window.translate) {\r\n      if (!window.translate.ignore.tag.includes(tagName)) {\r\n        window.translate.ignore.tag.push(tagName);\r\n      }\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAwBO,MAAM,kBAAkD,CAAC,EAAE,YAAY,EAAE,EAAE;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB;;iBAEO;gBACL,WAAW,gBAAgB;YAC7B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,YAAY;QAChB;YAAE,MAAM;YAAsB,MAAM;YAAQ,MAAM;QAAO;QACzD;YAAE,MAAM;YAAuB,MAAM;YAAQ,MAAM;QAAO;QAC1D;YAAE,MAAM;YAAW,MAAM;YAAW,MAAM;QAAO;QACjD;YAAE,MAAM;YAAY,MAAM;YAAO,MAAM;QAAO;QAC9C;YAAE,MAAM;YAAU,MAAM;YAAO,MAAM;QAAO;QAC5C;YAAE,MAAM;YAAU,MAAM;YAAY,MAAM;QAAO;QACjD;YAAE,MAAM;YAAU,MAAM;YAAW,MAAM;QAAO;QAChD;YAAE,MAAM;YAAW,MAAM;YAAW,MAAM;QAAO;QACjD;YAAE,MAAM;YAAW,MAAM;YAAW,MAAM;QAAO;QACjD;YAAE,MAAM;YAAU,MAAM;YAAW,MAAM;QAAO;KACjD;IAED,MAAM,uBAAuB,CAAC;QAC5B,IAAI,oBAAoB,OAAO,SAAS,EAAE;YACxC,OAAO,SAAS,CAAC,cAAc,CAAC;YAChC,mBAAmB;YAEnB,YAAY;YACZ,IAAI;gBACF,aAAa,OAAO,CAAC,iCAAiC;YACxD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB,IAAI;gBACF,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,mBAAmB;oBACnB,OAAO,SAAS,CAAC,cAAc,CAAC;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,IAAI,CAAC,kBAAkB;QACrB,OAAO;IACT;IAEA,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,oBAAoB,SAAS,CAAC,EAAE;IAEzF,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;kBAClD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAU;oBACV,OAAM;;sCAEN,8OAAC;sCAAM,YAAY,IAAI;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAoB,YAAY,IAAI;;;;;;sCACpD,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;8BAKzE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAmG;;;;;;gCAGjH,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;wCACjD,WAAW,CAAC,sHAAsH,EAChI,oBAAoB,SAAS,IAAI,GAC7B,oEACA,oCACJ;;0DAEF,8OAAC;0DAAM,SAAS,IAAI;;;;;;0DACpB,8OAAC;0DAAM,SAAS,IAAI;;;;;;4CACnB,oBAAoB,SAAS,IAAI,kBAChC,8OAAC;gDAAI,WAAU;gDAAkB,MAAK;gDAAe,SAAQ;0DAC3D,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;uCAZxJ,SAAS,IAAI;;;;;;;;;;;sCAmBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;AAGO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,aAAa;QACX;;IAGF;IAEA,UAAU;IACV,gBAAgB,CAAC;QACf;;IAGF;IAEA,YAAY;IACZ,gBAAgB,CAAC;QACf;;IAKF;IAEA,YAAY;IACZ,cAAc,CAAC;QACb;;IAKF;AACF", "debugId": null}}, {"offset": {"line": 6404, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/AdvancedRichTextEditor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\n\r\ninterface AdvancedRichTextEditorProps {\r\n  content: string;\r\n  onChange: (content: string) => void;\r\n  className?: string;\r\n}\r\n\r\ninterface FontStyle {\r\n  fontFamily: string;\r\n  fontSize: string;\r\n  fontWeight: string;\r\n  fontStyle: string;\r\n  color: string;\r\n  backgroundColor: string;\r\n  textDecoration: string;\r\n  textAlign: string;\r\n}\r\n\r\nexport const AdvancedRichTextEditor: React.FC<AdvancedRichTextEditorProps> = ({\r\n  content,\r\n  onChange,\r\n  className = ''\r\n}) => {\r\n  const [isRichMode, setIsRichMode] = useState(false);\r\n  const [showFontPanel, setShowFontPanel] = useState(false);\r\n  const [showColorPanel, setShowColorPanel] = useState(false);\r\n  const [showTablePanel, setShowTablePanel] = useState(false);\r\n  const [currentStyle, setCurrentStyle] = useState<FontStyle>({\r\n    fontFamily: 'Microsoft YaHei',\r\n    fontSize: '14px',\r\n    fontWeight: 'normal',\r\n    fontStyle: 'normal',\r\n    color: '#000000',\r\n    backgroundColor: 'transparent',\r\n    textDecoration: 'none',\r\n    textAlign: 'left'\r\n  });\r\n\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const richEditorRef = useRef<HTMLDivElement>(null);\r\n\r\n  // 字体选项\r\n  const fontFamilies = [\r\n    { name: '微软雅黑', value: 'Microsoft YaHei' },\r\n    { name: '宋体', value: 'SimSun' },\r\n    { name: '黑体', value: 'SimHei' },\r\n    { name: '楷体', value: 'KaiTi' },\r\n    { name: 'Arial', value: 'Arial' },\r\n    { name: 'Times New Roman', value: 'Times New Roman' },\r\n    { name: 'Helvetica', value: 'Helvetica' },\r\n    { name: 'Georgia', value: 'Georgia' },\r\n    { name: 'Verdana', value: 'Verdana' },\r\n    { name: 'Courier New', value: 'Courier New' }\r\n  ];\r\n\r\n  const fontSizes = [\r\n    { name: '初号', value: '42px' },\r\n    { name: '小初', value: '36px' },\r\n    { name: '一号', value: '26px' },\r\n    { name: '小一', value: '24px' },\r\n    { name: '二号', value: '22px' },\r\n    { name: '小二', value: '18px' },\r\n    { name: '三号', value: '16px' },\r\n    { name: '小三', value: '15px' },\r\n    { name: '四号', value: '14px' },\r\n    { name: '小四', value: '12px' },\r\n    { name: '五号', value: '10.5px' },\r\n    { name: '小五', value: '9px' },\r\n    { name: '六号', value: '7.5px' },\r\n    { name: '小六', value: '6.5px' }\r\n  ];\r\n\r\n  // 颜色预设\r\n  const colorPresets = [\r\n    '#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF',\r\n    '#FF0000', '#FF6600', '#FFCC00', '#FFFF00', '#99FF00', '#00FF00',\r\n    '#00FFCC', '#00CCFF', '#0066FF', '#0000FF', '#6600FF', '#CC00FF',\r\n    '#FF0099', '#FF3366', '#FF6699', '#FF99CC', '#FFCCFF', '#CCCCFF'\r\n  ];\r\n\r\n  // 执行富文本命令\r\n  const execCommand = (command: string, value?: string) => {\r\n    if (richEditorRef.current) {\r\n      richEditorRef.current.focus();\r\n      document.execCommand(command, false, value);\r\n      updateContent();\r\n    }\r\n  };\r\n\r\n  // 更新内容\r\n  const updateContent = () => {\r\n    if (richEditorRef.current) {\r\n      const html = richEditorRef.current.innerHTML;\r\n      const markdown = htmlToMarkdown(html);\r\n      onChange(markdown);\r\n    }\r\n  };\r\n\r\n  // HTML转Markdown（增强版）\r\n  const htmlToMarkdown = (html: string): string => {\r\n    return html\r\n      // 标题\r\n      .replace(/<h1[^>]*>(.*?)<\\/h1>/gi, '# $1\\n\\n')\r\n      .replace(/<h2[^>]*>(.*?)<\\/h2>/gi, '## $1\\n\\n')\r\n      .replace(/<h3[^>]*>(.*?)<\\/h3>/gi, '### $1\\n\\n')\r\n      .replace(/<h4[^>]*>(.*?)<\\/h4>/gi, '#### $1\\n\\n')\r\n      .replace(/<h5[^>]*>(.*?)<\\/h5>/gi, '##### $1\\n\\n')\r\n      .replace(/<h6[^>]*>(.*?)<\\/h6>/gi, '###### $1\\n\\n')\r\n      // 粗体和斜体\r\n      .replace(/<(strong|b)[^>]*>(.*?)<\\/(strong|b)>/gi, '**$2**')\r\n      .replace(/<(em|i)[^>]*>(.*?)<\\/(em|i)>/gi, '*$2*')\r\n      .replace(/<u[^>]*>(.*?)<\\/u>/gi, '<u>$1</u>')\r\n      .replace(/<s[^>]*>(.*?)<\\/s>/gi, '~~$1~~')\r\n      // 上标下标\r\n      .replace(/<sup[^>]*>(.*?)<\\/sup>/gi, '^$1^')\r\n      .replace(/<sub[^>]*>(.*?)<\\/sub>/gi, '~$1~')\r\n      // 代码\r\n      .replace(/<code[^>]*>(.*?)<\\/code>/gi, '`$1`')\r\n      .replace(/<pre[^>]*><code[^>]*>(.*?)<\\/code><\\/pre>/gi, '```\\n$1\\n```')\r\n      // 链接\r\n      .replace(/<a[^>]*href=\"([^\"]*)\"[^>]*>(.*?)<\\/a>/gi, '[$2]($1)')\r\n      // 图片\r\n      .replace(/<img[^>]*src=\"([^\"]*)\"[^>]*alt=\"([^\"]*)\"[^>]*>/gi, '![$2]($1)')\r\n      .replace(/<img[^>]*src=\"([^\"]*)\"[^>]*>/gi, '![]($1)')\r\n      // 列表\r\n      .replace(/<li[^>]*>(.*?)<\\/li>/gi, '- $1\\n')\r\n      .replace(/<ol[^>]*>(.*?)<\\/ol>/gi, '$1\\n')\r\n      .replace(/<ul[^>]*>(.*?)<\\/ul>/gi, '$1\\n')\r\n      // 引用\r\n      .replace(/<blockquote[^>]*>(.*?)<\\/blockquote>/gi, '> $1\\n')\r\n      // 分隔线\r\n      .replace(/<hr[^>]*>/gi, '\\n---\\n')\r\n      // 段落\r\n      .replace(/<p[^>]*>(.*?)<\\/p>/gi, '$1\\n\\n')\r\n      .replace(/<div[^>]*>(.*?)<\\/div>/gi, '$1\\n')\r\n      // 换行\r\n      .replace(/<br[^>]*>/gi, '\\n')\r\n      // 清理HTML标签\r\n      .replace(/<[^>]*>/g, '')\r\n      // 清理多余空行\r\n      .replace(/\\n{3,}/g, '\\n\\n')\r\n      .trim();\r\n  };\r\n\r\n  // Markdown转HTML（增强版）\r\n  const markdownToHtml = (markdown: string): string => {\r\n    return markdown\r\n      // 代码块\r\n      .replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>')\r\n      // 标题\r\n      .replace(/^###### (.*$)/gim, '<h6>$1</h6>')\r\n      .replace(/^##### (.*$)/gim, '<h5>$1</h5>')\r\n      .replace(/^#### (.*$)/gim, '<h4>$1</h4>')\r\n      .replace(/^### (.*$)/gim, '<h3>$1</h3>')\r\n      .replace(/^## (.*$)/gim, '<h2>$1</h2>')\r\n      .replace(/^# (.*$)/gim, '<h1>$1</h1>')\r\n      // 粗体和斜体\r\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n      .replace(/~~(.*?)~~/g, '<s>$1</s>')\r\n      .replace(/<u>(.*?)<\\/u>/g, '<u>$1</u>')\r\n      // 上标下标\r\n      .replace(/\\^(.*?)\\^/g, '<sup>$1</sup>')\r\n      .replace(/~(.*?)~/g, '<sub>$1</sub>')\r\n      // 行内代码\r\n      .replace(/`(.*?)`/g, '<code>$1</code>')\r\n      // 链接\r\n      .replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\">$1</a>')\r\n      // 图片\r\n      .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<img src=\"$2\" alt=\"$1\">')\r\n      // 列表\r\n      .replace(/^\\* (.*$)/gim, '<li>$1</li>')\r\n      .replace(/^- (.*$)/gim, '<li>$1</li>')\r\n      .replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>')\r\n      // 引用\r\n      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')\r\n      // 分隔线\r\n      .replace(/^---$/gim, '<hr>')\r\n      // 换行\r\n      .replace(/\\n/g, '<br>');\r\n  };\r\n\r\n  // 插入表格\r\n  const insertTable = (rows: number, cols: number) => {\r\n    let tableHtml = '<table border=\"1\" style=\"border-collapse: collapse; width: 100%;\">';\r\n    \r\n    // 表头\r\n    tableHtml += '<thead><tr>';\r\n    for (let j = 0; j < cols; j++) {\r\n      tableHtml += '<th style=\"padding: 8px; background-color: #f5f5f5;\">表头' + (j + 1) + '</th>';\r\n    }\r\n    tableHtml += '</tr></thead>';\r\n    \r\n    // 表体\r\n    tableHtml += '<tbody>';\r\n    for (let i = 0; i < rows - 1; i++) {\r\n      tableHtml += '<tr>';\r\n      for (let j = 0; j < cols; j++) {\r\n        tableHtml += '<td style=\"padding: 8px;\">内容' + (i + 1) + '-' + (j + 1) + '</td>';\r\n      }\r\n      tableHtml += '</tr>';\r\n    }\r\n    tableHtml += '</tbody></table><br>';\r\n\r\n    if (richEditorRef.current) {\r\n      richEditorRef.current.focus();\r\n      document.execCommand('insertHTML', false, tableHtml);\r\n      updateContent();\r\n    }\r\n    setShowTablePanel(false);\r\n  };\r\n\r\n  // 插入链接\r\n  const insertLink = () => {\r\n    const url = prompt('请输入链接地址:');\r\n    const text = prompt('请输入链接文字:') || url;\r\n    if (url) {\r\n      if (isRichMode) {\r\n        execCommand('createLink', url);\r\n      } else {\r\n        insertMarkdownText(`[${text}](${url})`);\r\n      }\r\n    }\r\n  };\r\n\r\n  // 插入图片\r\n  const insertImage = () => {\r\n    const url = prompt('请输入图片地址:');\r\n    const alt = prompt('请输入图片描述:') || '图片';\r\n    if (url) {\r\n      if (isRichMode) {\r\n        const imgHtml = `<img src=\"${url}\" alt=\"${alt}\" style=\"max-width: 100%; height: auto;\">`;\r\n        execCommand('insertHTML', imgHtml);\r\n      } else {\r\n        insertMarkdownText(`![${alt}](${url})`);\r\n      }\r\n    }\r\n  };\r\n\r\n  // 插入Markdown文本\r\n  const insertMarkdownText = (text: string) => {\r\n    if (textareaRef.current) {\r\n      const textarea = textareaRef.current;\r\n      const start = textarea.selectionStart;\r\n      const end = textarea.selectionEnd;\r\n      const newContent = content.substring(0, start) + text + content.substring(end);\r\n      onChange(newContent);\r\n      \r\n      setTimeout(() => {\r\n        textarea.focus();\r\n        textarea.setSelectionRange(start + text.length, start + text.length);\r\n      }, 0);\r\n    }\r\n  };\r\n\r\n  // 工具栏按钮组件\r\n  const ToolbarButton: React.FC<{\r\n    onClick: () => void;\r\n    title: string;\r\n    children: React.ReactNode;\r\n    active?: boolean;\r\n    disabled?: boolean;\r\n  }> = ({ onClick, title, children, active = false, disabled = false }) => (\r\n    <button\r\n      onClick={onClick}\r\n      title={title}\r\n      disabled={disabled}\r\n      className={`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm ${\r\n        active ? 'bg-blue-100 dark:bg-blue-900' : ''\r\n      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n\r\n  return (\r\n    <div className={`border border-gray-300 dark:border-gray-600 rounded-md ${className}`}>\r\n      {/* 主工具栏 */}\r\n      <div className=\"flex items-center gap-1 p-2 border-b border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap\">\r\n        {/* 模式切换 */}\r\n        <div className=\"flex items-center gap-1 mr-4\">\r\n          <button\r\n            onClick={() => setIsRichMode(false)}\r\n            className={`px-3 py-1 text-xs rounded ${\r\n              !isRichMode \r\n                ? 'bg-blue-500 text-white' \r\n                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'\r\n            }`}\r\n          >\r\n            Markdown\r\n          </button>\r\n          <button\r\n            onClick={() => setIsRichMode(true)}\r\n            className={`px-3 py-1 text-xs rounded ${\r\n              isRichMode \r\n                ? 'bg-blue-500 text-white' \r\n                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'\r\n            }`}\r\n          >\r\n            富文本\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mr-2\"></div>\r\n\r\n        {/* 字体设置 */}\r\n        <div className=\"relative\">\r\n          <ToolbarButton\r\n            onClick={() => setShowFontPanel(!showFontPanel)}\r\n            title=\"字体设置\"\r\n          >\r\n            <span className=\"text-sm\">字体</span>\r\n          </ToolbarButton>\r\n          \r\n          {showFontPanel && (\r\n            <div className=\"absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3 min-w-[300px]\">\r\n              <div className=\"grid grid-cols-2 gap-3\">\r\n                <div>\r\n                  <label className=\"block text-xs font-medium mb-1\">字体</label>\r\n                  <select \r\n                    value={currentStyle.fontFamily}\r\n                    onChange={(e) => {\r\n                      setCurrentStyle({...currentStyle, fontFamily: e.target.value});\r\n                      if (isRichMode) execCommand('fontName', e.target.value);\r\n                    }}\r\n                    className=\"w-full text-xs border rounded px-2 py-1\"\r\n                  >\r\n                    {fontFamilies.map(font => (\r\n                      <option key={font.value} value={font.value}>{font.name}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-xs font-medium mb-1\">字号</label>\r\n                  <select \r\n                    value={currentStyle.fontSize}\r\n                    onChange={(e) => {\r\n                      setCurrentStyle({...currentStyle, fontSize: e.target.value});\r\n                      if (isRichMode) execCommand('fontSize', e.target.value);\r\n                    }}\r\n                    className=\"w-full text-xs border rounded px-2 py-1\"\r\n                  >\r\n                    {fontSizes.map(size => (\r\n                      <option key={size.value} value={size.value}>{size.name}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* 文本格式 */}\r\n        <ToolbarButton onClick={() => execCommand('bold')} title=\"粗体 (Ctrl+B)\">\r\n          <strong>B</strong>\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('italic')} title=\"斜体 (Ctrl+I)\">\r\n          <em>I</em>\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('underline')} title=\"下划线 (Ctrl+U)\">\r\n          <u>U</u>\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('strikeThrough')} title=\"删除线\">\r\n          <s>S</s>\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('superscript')} title=\"上标\">\r\n          X²\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('subscript')} title=\"下标\">\r\n          X₂\r\n        </ToolbarButton>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\r\n\r\n        {/* 颜色设置 */}\r\n        <div className=\"relative\">\r\n          <ToolbarButton\r\n            onClick={() => setShowColorPanel(!showColorPanel)}\r\n            title=\"文字颜色\"\r\n          >\r\n            <span className=\"text-sm\">🎨</span>\r\n          </ToolbarButton>\r\n          \r\n          {showColorPanel && (\r\n            <div className=\"absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3\">\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-xs font-medium mb-1\">文字颜色</label>\r\n                <div className=\"grid grid-cols-6 gap-1 mb-2\">\r\n                  {colorPresets.map(color => (\r\n                    <button\r\n                      key={color}\r\n                      onClick={() => {\r\n                        setCurrentStyle({...currentStyle, color});\r\n                        if (isRichMode) execCommand('foreColor', color);\r\n                      }}\r\n                      className=\"w-6 h-6 rounded border border-gray-300\"\r\n                      style={{ backgroundColor: color }}\r\n                      title={color}\r\n                    />\r\n                  ))}\r\n                </div>\r\n                <input\r\n                  type=\"color\"\r\n                  value={currentStyle.color}\r\n                  onChange={(e) => {\r\n                    setCurrentStyle({...currentStyle, color: e.target.value});\r\n                    if (isRichMode) execCommand('foreColor', e.target.value);\r\n                  }}\r\n                  className=\"w-full h-8 rounded border\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-xs font-medium mb-1\">背景颜色</label>\r\n                <div className=\"grid grid-cols-6 gap-1 mb-2\">\r\n                  {colorPresets.map(color => (\r\n                    <button\r\n                      key={color}\r\n                      onClick={() => {\r\n                        setCurrentStyle({...currentStyle, backgroundColor: color});\r\n                        if (isRichMode) execCommand('backColor', color);\r\n                      }}\r\n                      className=\"w-6 h-6 rounded border border-gray-300\"\r\n                      style={{ backgroundColor: color }}\r\n                      title={color}\r\n                    />\r\n                  ))}\r\n                </div>\r\n                <input\r\n                  type=\"color\"\r\n                  value={currentStyle.backgroundColor === 'transparent' ? '#ffffff' : currentStyle.backgroundColor}\r\n                  onChange={(e) => {\r\n                    setCurrentStyle({...currentStyle, backgroundColor: e.target.value});\r\n                    if (isRichMode) execCommand('backColor', e.target.value);\r\n                  }}\r\n                  className=\"w-full h-8 rounded border\"\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\r\n\r\n        {/* 对齐方式 */}\r\n        <ToolbarButton onClick={() => execCommand('justifyLeft')} title=\"左对齐\">\r\n          ⬅️\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('justifyCenter')} title=\"居中对齐\">\r\n          ↔️\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('justifyRight')} title=\"右对齐\">\r\n          ➡️\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('justifyFull')} title=\"两端对齐\">\r\n          ↕️\r\n        </ToolbarButton>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\r\n\r\n        {/* 列表 */}\r\n        <ToolbarButton onClick={() => execCommand('insertUnorderedList')} title=\"无序列表\">\r\n          •\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('insertOrderedList')} title=\"有序列表\">\r\n          1.\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('indent')} title=\"增加缩进\">\r\n          →\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('outdent')} title=\"减少缩进\">\r\n          ←\r\n        </ToolbarButton>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\r\n\r\n        {/* 插入元素 */}\r\n        <ToolbarButton onClick={insertLink} title=\"插入链接\">\r\n          🔗\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={insertImage} title=\"插入图片\">\r\n          🖼️\r\n        </ToolbarButton>\r\n        \r\n        <div className=\"relative\">\r\n          <ToolbarButton\r\n            onClick={() => setShowTablePanel(!showTablePanel)}\r\n            title=\"插入表格\"\r\n          >\r\n            ⊞\r\n          </ToolbarButton>\r\n          \r\n          {showTablePanel && (\r\n            <div className=\"absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3\">\r\n              <div className=\"text-xs font-medium mb-2\">选择表格大小</div>\r\n              <div className=\"grid grid-cols-5 gap-1\">\r\n                {Array.from({length: 25}, (_, i) => {\r\n                  const row = Math.floor(i / 5) + 1;\r\n                  const col = (i % 5) + 1;\r\n                  return (\r\n                    <button\r\n                      key={i}\r\n                      onClick={() => insertTable(row, col)}\r\n                      className=\"w-6 h-6 border border-gray-300 hover:bg-blue-100 text-xs\"\r\n                      title={`${row}×${col}`}\r\n                    >\r\n                      {row}×{col}\r\n                    </button>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <ToolbarButton onClick={() => execCommand('insertHorizontalRule')} title=\"分隔线\">\r\n          —\r\n        </ToolbarButton>\r\n\r\n        {/* 特殊格式 */}\r\n        <ToolbarButton\r\n          onClick={() => {\r\n            if (isRichMode) {\r\n              execCommand('formatBlock', 'blockquote');\r\n            } else {\r\n              insertMarkdownText('> 引用内容');\r\n            }\r\n          }}\r\n          title=\"引用\"\r\n        >\r\n          ❝\r\n        </ToolbarButton>\r\n\r\n        <ToolbarButton\r\n          onClick={() => {\r\n            if (isRichMode) {\r\n              execCommand('formatBlock', 'pre');\r\n            } else {\r\n              insertMarkdownText('```\\n代码块\\n```');\r\n            }\r\n          }}\r\n          title=\"代码块\"\r\n        >\r\n          { }\r\n        </ToolbarButton>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\r\n\r\n        {/* 标题 */}\r\n        <select\r\n          onChange={(e) => {\r\n            if (e.target.value && isRichMode) {\r\n              execCommand('formatBlock', e.target.value);\r\n            }\r\n          }}\r\n          className=\"text-xs border rounded px-2 py-1 bg-white dark:bg-gray-700\"\r\n          defaultValue=\"\"\r\n        >\r\n          <option value=\"\">标题</option>\r\n          <option value=\"h1\">一级标题</option>\r\n          <option value=\"h2\">二级标题</option>\r\n          <option value=\"h3\">三级标题</option>\r\n          <option value=\"h4\">四级标题</option>\r\n          <option value=\"h5\">五级标题</option>\r\n          <option value=\"h6\">六级标题</option>\r\n          <option value=\"p\">正文</option>\r\n        </select>\r\n\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\r\n\r\n        {/* 编辑操作 */}\r\n        <ToolbarButton onClick={() => execCommand('undo')} title=\"撤销 (Ctrl+Z)\">\r\n          ↶\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('redo')} title=\"重做 (Ctrl+Y)\">\r\n          ↷\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('removeFormat')} title=\"清除格式\">\r\n          🧹\r\n        </ToolbarButton>\r\n        <ToolbarButton onClick={() => execCommand('selectAll')} title=\"全选 (Ctrl+A)\">\r\n          📄\r\n        </ToolbarButton>\r\n\r\n        {/* 查找替换 */}\r\n        <ToolbarButton\r\n          onClick={() => {\r\n            const searchText = prompt('请输入要查找的文字:');\r\n            if (searchText) {\r\n              execCommand('findString', searchText);\r\n            }\r\n          }}\r\n          title=\"查找\"\r\n        >\r\n          🔍\r\n        </ToolbarButton>\r\n      </div>\r\n\r\n      {/* 编辑器内容 */}\r\n      {isRichMode ? (\r\n        // 富文本模式\r\n        <div\r\n          ref={richEditorRef}\r\n          contentEditable\r\n          className=\"p-4 min-h-[400px] focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 overflow-y-auto\"\r\n          dangerouslySetInnerHTML={{ __html: markdownToHtml(content) }}\r\n          onInput={updateContent}\r\n          onKeyDown={(e) => {\r\n            // 快捷键支持\r\n            if (e.ctrlKey || e.metaKey) {\r\n              switch (e.key) {\r\n                case 'b':\r\n                  e.preventDefault();\r\n                  execCommand('bold');\r\n                  break;\r\n                case 'i':\r\n                  e.preventDefault();\r\n                  execCommand('italic');\r\n                  break;\r\n                case 'u':\r\n                  e.preventDefault();\r\n                  execCommand('underline');\r\n                  break;\r\n                case 'z':\r\n                  e.preventDefault();\r\n                  execCommand('undo');\r\n                  break;\r\n                case 'y':\r\n                  e.preventDefault();\r\n                  execCommand('redo');\r\n                  break;\r\n              }\r\n            }\r\n          }}\r\n          style={{\r\n            lineHeight: '1.6',\r\n            fontFamily: currentStyle.fontFamily,\r\n            fontSize: currentStyle.fontSize\r\n          }}\r\n        />\r\n      ) : (\r\n        // Markdown模式\r\n        <textarea\r\n          ref={textareaRef}\r\n          value={content}\r\n          onChange={(e) => onChange(e.target.value)}\r\n          className=\"w-full p-4 min-h-[400px] resize-none focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono text-sm\"\r\n          placeholder=\"输入Markdown内容...\"\r\n          style={{ lineHeight: '1.6' }}\r\n        />\r\n      )}\r\n\r\n      {/* 状态栏 */}\r\n      <div className=\"flex items-center justify-between px-4 py-2 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-300 dark:border-gray-600\">\r\n        <span>\r\n          {isRichMode ? '🎨 富文本模式' : '📝 Markdown模式'} | 字符数: {content.length} | 行数: {content.split('\\n').length}\r\n        </span>\r\n        <span>\r\n          快捷键: Ctrl+B(粗体) Ctrl+I(斜体) Ctrl+U(下划线) Ctrl+Z(撤销) Ctrl+Y(重做)\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAqBO,MAAM,yBAAgE,CAAC,EAC5E,OAAO,EACP,QAAQ,EACR,YAAY,EAAE,EACf;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAC1D,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,OAAO;QACP,iBAAiB;QACjB,gBAAgB;QAChB,WAAW;IACb;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE7C,OAAO;IACP,MAAM,eAAe;QACnB;YAAE,MAAM;YAAQ,OAAO;QAAkB;QACzC;YAAE,MAAM;YAAM,OAAO;QAAS;QAC9B;YAAE,MAAM;YAAM,OAAO;QAAS;QAC9B;YAAE,MAAM;YAAM,OAAO;QAAQ;QAC7B;YAAE,MAAM;YAAS,OAAO;QAAQ;QAChC;YAAE,MAAM;YAAmB,OAAO;QAAkB;QACpD;YAAE,MAAM;YAAa,OAAO;QAAY;QACxC;YAAE,MAAM;YAAW,OAAO;QAAU;QACpC;YAAE,MAAM;YAAW,OAAO;QAAU;QACpC;YAAE,MAAM;YAAe,OAAO;QAAc;KAC7C;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAO;QAC5B;YAAE,MAAM;YAAM,OAAO;QAAS;QAC9B;YAAE,MAAM;YAAM,OAAO;QAAM;QAC3B;YAAE,MAAM;YAAM,OAAO;QAAQ;QAC7B;YAAE,MAAM;YAAM,OAAO;QAAQ;KAC9B;IAED,OAAO;IACP,MAAM,eAAe;QACnB;QAAW;QAAW;QAAW;QAAW;QAAW;QACvD;QAAW;QAAW;QAAW;QAAW;QAAW;QACvD;QAAW;QAAW;QAAW;QAAW;QAAW;QACvD;QAAW;QAAW;QAAW;QAAW;QAAW;KACxD;IAED,UAAU;IACV,MAAM,cAAc,CAAC,SAAiB;QACpC,IAAI,cAAc,OAAO,EAAE;YACzB,cAAc,OAAO,CAAC,KAAK;YAC3B,SAAS,WAAW,CAAC,SAAS,OAAO;YACrC;QACF;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,IAAI,cAAc,OAAO,EAAE;YACzB,MAAM,OAAO,cAAc,OAAO,CAAC,SAAS;YAC5C,MAAM,WAAW,eAAe;YAChC,SAAS;QACX;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB,CAAC;QACtB,OAAO,IACL,KAAK;SACJ,OAAO,CAAC,0BAA0B,YAClC,OAAO,CAAC,0BAA0B,aAClC,OAAO,CAAC,0BAA0B,cAClC,OAAO,CAAC,0BAA0B,eAClC,OAAO,CAAC,0BAA0B,gBAClC,OAAO,CAAC,0BAA0B,gBACnC,QAAQ;SACP,OAAO,CAAC,0CAA0C,UAClD,OAAO,CAAC,kCAAkC,QAC1C,OAAO,CAAC,wBAAwB,aAChC,OAAO,CAAC,wBAAwB,SACjC,OAAO;SACN,OAAO,CAAC,4BAA4B,QACpC,OAAO,CAAC,4BAA4B,OACrC,KAAK;SACJ,OAAO,CAAC,8BAA8B,QACtC,OAAO,CAAC,+CAA+C,eACxD,KAAK;SACJ,OAAO,CAAC,2CAA2C,WACpD,KAAK;SACJ,OAAO,CAAC,oDAAoD,aAC5D,OAAO,CAAC,kCAAkC,UAC3C,KAAK;SACJ,OAAO,CAAC,0BAA0B,UAClC,OAAO,CAAC,0BAA0B,QAClC,OAAO,CAAC,0BAA0B,OACnC,KAAK;SACJ,OAAO,CAAC,0CAA0C,SACnD,MAAM;SACL,OAAO,CAAC,eAAe,UACxB,KAAK;SACJ,OAAO,CAAC,wBAAwB,UAChC,OAAO,CAAC,4BAA4B,OACrC,KAAK;SACJ,OAAO,CAAC,eAAe,KACxB,WAAW;SACV,OAAO,CAAC,YAAY,GACrB,SAAS;SACR,OAAO,CAAC,WAAW,QACnB,IAAI;IACT;IAEA,qBAAqB;IACrB,MAAM,iBAAiB,CAAC;QACtB,OAAO,QACL,MAAM;SACL,OAAO,CAAC,qBAAqB,6BAC9B,KAAK;SACJ,OAAO,CAAC,oBAAoB,eAC5B,OAAO,CAAC,mBAAmB,eAC3B,OAAO,CAAC,kBAAkB,eAC1B,OAAO,CAAC,iBAAiB,eACzB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,eAAe,cACxB,QAAQ;SACP,OAAO,CAAC,kBAAkB,uBAC1B,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,cAAc,aACtB,OAAO,CAAC,kBAAkB,YAC3B,OAAO;SACN,OAAO,CAAC,cAAc,iBACtB,OAAO,CAAC,YAAY,gBACrB,OAAO;SACN,OAAO,CAAC,YAAY,kBACrB,KAAK;SACJ,OAAO,CAAC,4BAA4B,sBACrC,KAAK;SACJ,OAAO,CAAC,6BAA6B,0BACtC,KAAK;SACJ,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,mBAAmB,cAC5B,KAAK;SACJ,OAAO,CAAC,eAAe,8BACxB,MAAM;SACL,OAAO,CAAC,YAAY,OACrB,KAAK;SACJ,OAAO,CAAC,OAAO;IACpB;IAEA,OAAO;IACP,MAAM,cAAc,CAAC,MAAc;QACjC,IAAI,YAAY;QAEhB,KAAK;QACL,aAAa;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,aAAa,4DAA4D,CAAC,IAAI,CAAC,IAAI;QACrF;QACA,aAAa;QAEb,KAAK;QACL,aAAa;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,GAAG,IAAK;YACjC,aAAa;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,aAAa,iCAAiC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;YAC1E;YACA,aAAa;QACf;QACA,aAAa;QAEb,IAAI,cAAc,OAAO,EAAE;YACzB,cAAc,OAAO,CAAC,KAAK;YAC3B,SAAS,WAAW,CAAC,cAAc,OAAO;YAC1C;QACF;QACA,kBAAkB;IACpB;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,MAAM,MAAM,OAAO;QACnB,MAAM,OAAO,OAAO,eAAe;QACnC,IAAI,KAAK;YACP,IAAI,YAAY;gBACd,YAAY,cAAc;YAC5B,OAAO;gBACL,mBAAmB,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;YACxC;QACF;IACF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,MAAM,MAAM,OAAO;QACnB,MAAM,MAAM,OAAO,eAAe;QAClC,IAAI,KAAK;YACP,IAAI,YAAY;gBACd,MAAM,UAAU,CAAC,UAAU,EAAE,IAAI,OAAO,EAAE,IAAI,yCAAyC,CAAC;gBACxF,YAAY,cAAc;YAC5B,OAAO;gBACL,mBAAmB,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;YACxC;QACF;IACF;IAEA,eAAe;IACf,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,WAAW,YAAY,OAAO;YACpC,MAAM,QAAQ,SAAS,cAAc;YACrC,MAAM,MAAM,SAAS,YAAY;YACjC,MAAM,aAAa,QAAQ,SAAS,CAAC,GAAG,SAAS,OAAO,QAAQ,SAAS,CAAC;YAC1E,SAAS;YAET,WAAW;gBACT,SAAS,KAAK;gBACd,SAAS,iBAAiB,CAAC,QAAQ,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM;YACrE,GAAG;QACL;IACF;IAEA,UAAU;IACV,MAAM,gBAMD,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,KAAK,EAAE,WAAW,KAAK,EAAE,iBAClE,8OAAC;YACC,SAAS;YACT,OAAO;YACP,UAAU;YACV,WAAW,CAAC,+EAA+E,EACzF,SAAS,iCAAiC,GAC3C,CAAC,EAAE,WAAW,kCAAkC,IAAI;sBAEpD;;;;;;IAIL,qBACE,8OAAC;QAAI,WAAW,CAAC,uDAAuD,EAAE,WAAW;;0BAEnF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,0BAA0B,EACpC,CAAC,aACG,2BACA,iEACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,0BAA0B,EACpC,aACI,2BACA,iEACJ;0CACH;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,CAAC;gCACjC,OAAM;0CAEN,cAAA,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;4BAG3B,+BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAClD,8OAAC;oDACC,OAAO,aAAa,UAAU;oDAC9B,UAAU,CAAC;wDACT,gBAAgB;4DAAC,GAAG,YAAY;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;wDAC5D,IAAI,YAAY,YAAY,YAAY,EAAE,MAAM,CAAC,KAAK;oDACxD;oDACA,WAAU;8DAET,aAAa,GAAG,CAAC,CAAA,qBAChB,8OAAC;4DAAwB,OAAO,KAAK,KAAK;sEAAG,KAAK,IAAI;2DAAzC,KAAK,KAAK;;;;;;;;;;;;;;;;sDAI7B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAClD,8OAAC;oDACC,OAAO,aAAa,QAAQ;oDAC5B,UAAU,CAAC;wDACT,gBAAgB;4DAAC,GAAG,YAAY;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAA;wDAC1D,IAAI,YAAY,YAAY,YAAY,EAAE,MAAM,CAAC,KAAK;oDACxD;oDACA,WAAU;8DAET,UAAU,GAAG,CAAC,CAAA,qBACb,8OAAC;4DAAwB,OAAO,KAAK,KAAK;sEAAG,KAAK,IAAI;2DAAzC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrC,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAS,OAAM;kCACvD,cAAA,8OAAC;sCAAO;;;;;;;;;;;kCAEV,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAW,OAAM;kCACzD,cAAA,8OAAC;sCAAG;;;;;;;;;;;kCAEN,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAc,OAAM;kCAC5D,cAAA,8OAAC;sCAAE;;;;;;;;;;;kCAEL,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAkB,OAAM;kCAChE,cAAA,8OAAC;sCAAE;;;;;;;;;;;kCAEL,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAgB,OAAM;kCAAK;;;;;;kCAGrE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAc,OAAM;kCAAK;;;;;;kCAInE,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,OAAM;0CAEN,cAAA,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;4BAG3B,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,8OAAC;wDAEC,SAAS;4DACP,gBAAgB;gEAAC,GAAG,YAAY;gEAAE;4DAAK;4DACvC,IAAI,YAAY,YAAY,aAAa;wDAC3C;wDACA,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAM;wDAChC,OAAO;uDAPF;;;;;;;;;;0DAWX,8OAAC;gDACC,MAAK;gDACL,OAAO,aAAa,KAAK;gDACzB,UAAU,CAAC;oDACT,gBAAgB;wDAAC,GAAG,YAAY;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAA;oDACvD,IAAI,YAAY,YAAY,aAAa,EAAE,MAAM,CAAC,KAAK;gDACzD;gDACA,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,8OAAC;wDAEC,SAAS;4DACP,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,iBAAiB;4DAAK;4DACxD,IAAI,YAAY,YAAY,aAAa;wDAC3C;wDACA,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAM;wDAChC,OAAO;uDAPF;;;;;;;;;;0DAWX,8OAAC;gDACC,MAAK;gDACL,OAAO,aAAa,eAAe,KAAK,gBAAgB,YAAY,aAAa,eAAe;gDAChG,UAAU,CAAC;oDACT,gBAAgB;wDAAC,GAAG,YAAY;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAA;oDACjE,IAAI,YAAY,YAAY,aAAa,EAAE,MAAM,CAAC,KAAK;gDACzD;gDACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOpB,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAgB,OAAM;kCAAM;;;;;;kCAGtE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAkB,OAAM;kCAAO;;;;;;kCAGzE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAiB,OAAM;kCAAM;;;;;;kCAGvE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAgB,OAAM;kCAAO;;;;;;kCAIvE,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAwB,OAAM;kCAAO;;;;;;kCAG/E,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAsB,OAAM;kCAAO;;;;;;kCAG7E,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAW,OAAM;kCAAO;;;;;;kCAGlE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAY,OAAM;kCAAO;;;;;;kCAInE,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAc,SAAS;wBAAY,OAAM;kCAAO;;;;;;kCAGjD,8OAAC;wBAAc,SAAS;wBAAa,OAAM;kCAAO;;;;;;kCAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,OAAM;0CACP;;;;;;4BAIA,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA2B;;;;;;kDAC1C,8OAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAC,QAAQ;wCAAE,GAAG,CAAC,GAAG;4CAC5B,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK;4CAChC,MAAM,MAAM,AAAC,IAAI,IAAK;4CACtB,qBACE,8OAAC;gDAEC,SAAS,IAAM,YAAY,KAAK;gDAChC,WAAU;gDACV,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK;;oDAErB;oDAAI;oDAAE;;+CALF;;;;;wCAQX;;;;;;;;;;;;;;;;;;kCAMR,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAyB,OAAM;kCAAM;;;;;;kCAK/E,8OAAC;wBACC,SAAS;4BACP,IAAI,YAAY;gCACd,YAAY,eAAe;4BAC7B,OAAO;gCACL,mBAAmB;4BACrB;wBACF;wBACA,OAAM;kCACP;;;;;;kCAID,8OAAC;wBACC,SAAS;4BACP,IAAI,YAAY;gCACd,YAAY,eAAe;4BAC7B,OAAO;gCACL,mBAAmB;4BACrB;wBACF;wBACA,OAAM;;;;;;kCAKR,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBACC,UAAU,CAAC;4BACT,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,YAAY;gCAChC,YAAY,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC3C;wBACF;wBACA,WAAU;wBACV,cAAa;;0CAEb,8OAAC;gCAAO,OAAM;0CAAG;;;;;;0CACjB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAI;;;;;;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAS,OAAM;kCAAc;;;;;;kCAGvE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAS,OAAM;kCAAc;;;;;;kCAGvE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAiB,OAAM;kCAAO;;;;;;kCAGxE,8OAAC;wBAAc,SAAS,IAAM,YAAY;wBAAc,OAAM;kCAAc;;;;;;kCAK5E,8OAAC;wBACC,SAAS;4BACP,MAAM,aAAa,OAAO;4BAC1B,IAAI,YAAY;gCACd,YAAY,cAAc;4BAC5B;wBACF;wBACA,OAAM;kCACP;;;;;;;;;;;;YAMF,aACC,QAAQ;0BACR,8OAAC;gBACC,KAAK;gBACL,eAAe;gBACf,WAAU;gBACV,yBAAyB;oBAAE,QAAQ,eAAe;gBAAS;gBAC3D,SAAS;gBACT,WAAW,CAAC;oBACV,QAAQ;oBACR,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;wBAC1B,OAAQ,EAAE,GAAG;4BACX,KAAK;gCACH,EAAE,cAAc;gCAChB,YAAY;gCACZ;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,YAAY;gCACZ;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,YAAY;gCACZ;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,YAAY;gCACZ;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,YAAY;gCACZ;wBACJ;oBACF;gBACF;gBACA,OAAO;oBACL,YAAY;oBACZ,YAAY,aAAa,UAAU;oBACnC,UAAU,aAAa,QAAQ;gBACjC;;;;;2DAGF,aAAa;0BACb,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,WAAU;gBACV,aAAY;gBACZ,OAAO;oBAAE,YAAY;gBAAM;;;;;;0BAK/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BACE,aAAa,aAAa;4BAAgB;4BAAS,QAAQ,MAAM;4BAAC;4BAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM;;;;;;;kCAEvG,8OAAC;kCAAK;;;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 7535, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/UEditor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\r\nimport { AdvancedRichTextEditor } from './AdvancedRichTextEditor';\r\n\r\n// 声明UE全局变量\r\ndeclare global {\r\n  interface Window {\r\n    UE: any;\r\n    UEDITOR_CONFIG: any;\r\n    UEDITOR_HOME_URL: string;\r\n  }\r\n}\r\n\r\ninterface UEditorProps {\r\n  content: string;\r\n  onChange: (content: string) => void;\r\n  className?: string;\r\n  config?: any;\r\n  height?: number;\r\n  width?: string;\r\n}\r\n\r\nexport const UEditor: React.FC<UEditorProps> = ({\r\n  content,\r\n  onChange,\r\n  className = '',\r\n  config = {},\r\n  height = 400,\r\n  width = '100%'\r\n}) => {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const editorRef = useRef<any>(null);\r\n  const [isUEditorLoaded, setIsUEditorLoaded] = useState(false);\r\n  const [isUEditorReady, setIsUEditorReady] = useState(false);\r\n  const [loadError, setLoadError] = useState<string | null>(null);\r\n  const [isRichMode, setIsRichMode] = useState(false);\r\n\r\n  // 检查UEditor是否可用\r\n  const checkUEditorAvailability = useCallback(() => {\r\n    // 检查是否已经加载\r\n    if (window.UE) {\r\n      setIsUEditorLoaded(true);\r\n      return;\r\n    }\r\n\r\n    // 直接加载简化版本\r\n    loadUEditorScripts();\r\n  }, []);\r\n\r\n  // 加载UEditor脚本\r\n  const loadUEditorScripts = useCallback(() => {\r\n    if (window.UE) {\r\n      setIsUEditorLoaded(true);\r\n      return;\r\n    }\r\n\r\n    // 设置UEditor路径\r\n    window.UEDITOR_HOME_URL = '/ueditor/';\r\n\r\n    // 使用简化版本的UEditor\r\n    const script = document.createElement('script');\r\n    script.src = '/ueditor/ueditor.all.min.js';\r\n    script.onload = () => {\r\n      // 检查UE对象是否创建成功\r\n      if (window.UE && window.UE.getEditor) {\r\n        setIsUEditorLoaded(true);\r\n      } else {\r\n        setLoadError('UEditor加载失败');\r\n      }\r\n    };\r\n    script.onerror = () => {\r\n      setLoadError('UEditor加载失败');\r\n    };\r\n\r\n    document.head.appendChild(script);\r\n  }, []);\r\n\r\n  // 初始化UEditor\r\n  const initUEditor = useCallback(() => {\r\n    if (!isUEditorLoaded || !window.UE || !containerRef.current) return;\r\n\r\n    const editorId = `ueditor_${Date.now()}`;\r\n    containerRef.current.id = editorId;\r\n\r\n    const defaultConfig = {\r\n      initialFrameHeight: height,\r\n      initialFrameWidth: width,\r\n      autoHeightEnabled: false,\r\n      focus: false,\r\n      readonly: false,\r\n      enableAutoSave: false,\r\n      toolbars: [[\r\n        'fullscreen', 'source', '|', 'undo', 'redo', '|',\r\n        'bold', 'italic', 'underline', 'strikethrough', '|',\r\n        'forecolor', 'backcolor', '|',\r\n        'insertorderedlist', 'insertunorderedlist', '|',\r\n        'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',\r\n        'link', 'unlink', '|',\r\n        'simpleupload', 'insertimage', '|',\r\n        'inserttable', 'deletetable', '|',\r\n        'searchreplace', 'help'\r\n      ]],\r\n      ...config\r\n    };\r\n\r\n    try {\r\n      const editor = window.UE.getEditor(editorId, defaultConfig);\r\n\r\n      if (editor && typeof editor.ready === 'function') {\r\n        editorRef.current = editor;\r\n\r\n        editorRef.current.ready(() => {\r\n          try {\r\n            setIsUEditorReady(true);\r\n            console.log('UEditor ready回调执行');\r\n\r\n            // 设置初始内容\r\n            if (content && editorRef.current && typeof editorRef.current.setContent === 'function') {\r\n              editorRef.current.setContent(content);\r\n            }\r\n\r\n            // 监听内容变化\r\n            if (editorRef.current && typeof editorRef.current.addListener === 'function') {\r\n              editorRef.current.addListener('contentChange', () => {\r\n                try {\r\n                  if (editorRef.current && typeof editorRef.current.getContent === 'function') {\r\n                    const newContent = editorRef.current.getContent();\r\n                    onChange(newContent);\r\n                  }\r\n                } catch (error) {\r\n                  console.error('UEditor内容变化处理失败:', error);\r\n                }\r\n              });\r\n            }\r\n          } catch (error) {\r\n            console.error('UEditor ready回调执行失败:', error);\r\n            setLoadError('UEditor初始化回调失败');\r\n          }\r\n        });\r\n      } else {\r\n        console.error('UEditor实例创建失败或ready方法不存在');\r\n        setLoadError('UEditor实例创建失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('UEditor初始化失败:', error);\r\n      setLoadError('UEditor初始化失败');\r\n    }\r\n  }, [isUEditorLoaded, content, onChange, config, height, width]);\r\n\r\n  // 更新内容\r\n  useEffect(() => {\r\n    if (isUEditorReady &&\r\n        editorRef.current &&\r\n        typeof editorRef.current.getContent === 'function' &&\r\n        typeof editorRef.current.setContent === 'function') {\r\n      try {\r\n        const currentContent = editorRef.current.getContent();\r\n        if (content !== currentContent) {\r\n          editorRef.current.setContent(content);\r\n        }\r\n      } catch (error) {\r\n        console.error('UEditor内容更新失败:', error);\r\n      }\r\n    }\r\n  }, [content, isUEditorReady]);\r\n\r\n  // 组件挂载时加载UEditor\r\n  useEffect(() => {\r\n    checkUEditorAvailability();\r\n  }, [checkUEditorAvailability]);\r\n\r\n  // UEditor加载完成后初始化\r\n  useEffect(() => {\r\n    if (isUEditorLoaded) {\r\n      initUEditor();\r\n    }\r\n  }, [isUEditorLoaded, initUEditor]);\r\n\r\n  // 组件卸载时销毁编辑器\r\n  useEffect(() => {\r\n    return () => {\r\n      if (editorRef.current && typeof editorRef.current.destroy === 'function') {\r\n        try {\r\n          editorRef.current.destroy();\r\n          editorRef.current = null;\r\n        } catch (error) {\r\n          console.error('UEditor销毁失败:', error);\r\n        }\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // 如果UEditor不可用，静默降级到备用编辑器\r\n  if (loadError) {\r\n    return (\r\n      <div className={className}>\r\n        {/* 静默使用备用编辑器，不显示警告 */}\r\n        <AdvancedRichTextEditor\r\n          content={content}\r\n          onChange={onChange}\r\n          className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // UEditor加载中\r\n  if (!isUEditorLoaded) {\r\n    return (\r\n      <div className={`${className} flex items-center justify-center h-64 border border-gray-300 dark:border-gray-600 rounded-md`}>\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">正在加载UEditor...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={className}>\r\n      {/* 模式切换 */}\r\n      <div className=\"flex items-center gap-2 mb-4 p-2 bg-gray-50 dark:bg-gray-700 rounded-t-md border border-gray-300 dark:border-gray-600\">\r\n        <span className=\"text-sm text-gray-600 dark:text-gray-400\">编辑模式:</span>\r\n        <button\r\n          onClick={() => setIsRichMode(false)}\r\n          className={`px-3 py-1 text-xs rounded ${\r\n            !isRichMode \r\n              ? 'bg-blue-500 text-white' \r\n              : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'\r\n          }`}\r\n        >\r\n          Markdown\r\n        </button>\r\n        <button\r\n          onClick={() => setIsRichMode(true)}\r\n          className={`px-3 py-1 text-xs rounded ${\r\n            isRichMode \r\n              ? 'bg-blue-500 text-white' \r\n              : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'\r\n          }`}\r\n        >\r\n          UEditor富文本\r\n        </button>\r\n      </div>\r\n\r\n      {/* 编辑器内容 */}\r\n      {isRichMode ? (\r\n        // UEditor模式\r\n        <div \r\n          ref={containerRef}\r\n          className=\"border border-gray-300 dark:border-gray-600 rounded-b-md\"\r\n          style={{ minHeight: height }}\r\n        />\r\n      ) : (\r\n        // Markdown模式\r\n        <textarea\r\n          value={content}\r\n          onChange={(e) => onChange(e.target.value)}\r\n          className=\"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-b-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          style={{ height: height, lineHeight: '1.6' }}\r\n          placeholder=\"输入Markdown内容...\"\r\n        />\r\n      )}\r\n\r\n      {/* 状态栏 */}\r\n      <div className=\"flex items-center justify-between px-4 py-2 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border border-t-0 border-gray-300 dark:border-gray-600 rounded-b-md\">\r\n        <span>\r\n          {isRichMode ? '🎨 UEditor富文本模式' : '📝 Markdown模式'} | 字符数: {content.length}\r\n        </span>\r\n        <span>\r\n          {isRichMode ? 'UEditor提供强大的富文本编辑功能' : '支持标准Markdown语法'}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAuBO,MAAM,UAAkC,CAAC,EAC9C,OAAO,EACP,QAAQ,EACR,YAAY,EAAE,EACd,SAAS,CAAC,CAAC,EACX,SAAS,GAAG,EACZ,QAAQ,MAAM,EACf;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,gBAAgB;IAChB,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,WAAW;QACX,IAAI,OAAO,EAAE,EAAE;YACb,mBAAmB;YACnB;QACF;QAEA,WAAW;QACX;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,OAAO,EAAE,EAAE;YACb,mBAAmB;YACnB;QACF;QAEA,cAAc;QACd,OAAO,gBAAgB,GAAG;QAE1B,iBAAiB;QACjB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,GAAG,GAAG;QACb,OAAO,MAAM,GAAG;YACd,eAAe;YACf,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,SAAS,EAAE;gBACpC,mBAAmB;YACrB,OAAO;gBACL,aAAa;YACf;QACF;QACA,OAAO,OAAO,GAAG;YACf,aAAa;QACf;QAEA,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG,EAAE;IAEL,aAAa;IACb,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,OAAO,EAAE;QAE7D,MAAM,WAAW,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;QACxC,aAAa,OAAO,CAAC,EAAE,GAAG;QAE1B,MAAM,gBAAgB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,mBAAmB;YACnB,OAAO;YACP,UAAU;YACV,gBAAgB;YAChB,UAAU;gBAAC;oBACT;oBAAc;oBAAU;oBAAK;oBAAQ;oBAAQ;oBAC7C;oBAAQ;oBAAU;oBAAa;oBAAiB;oBAChD;oBAAa;oBAAa;oBAC1B;oBAAqB;oBAAuB;oBAC5C;oBAAe;oBAAiB;oBAAgB;oBAAkB;oBAClE;oBAAQ;oBAAU;oBAClB;oBAAgB;oBAAe;oBAC/B;oBAAe;oBAAe;oBAC9B;oBAAiB;iBAClB;aAAC;YACF,GAAG,MAAM;QACX;QAEA,IAAI;YACF,MAAM,SAAS,OAAO,EAAE,CAAC,SAAS,CAAC,UAAU;YAE7C,IAAI,UAAU,OAAO,OAAO,KAAK,KAAK,YAAY;gBAChD,UAAU,OAAO,GAAG;gBAEpB,UAAU,OAAO,CAAC,KAAK,CAAC;oBACtB,IAAI;wBACF,kBAAkB;wBAClB,QAAQ,GAAG,CAAC;wBAEZ,SAAS;wBACT,IAAI,WAAW,UAAU,OAAO,IAAI,OAAO,UAAU,OAAO,CAAC,UAAU,KAAK,YAAY;4BACtF,UAAU,OAAO,CAAC,UAAU,CAAC;wBAC/B;wBAEA,SAAS;wBACT,IAAI,UAAU,OAAO,IAAI,OAAO,UAAU,OAAO,CAAC,WAAW,KAAK,YAAY;4BAC5E,UAAU,OAAO,CAAC,WAAW,CAAC,iBAAiB;gCAC7C,IAAI;oCACF,IAAI,UAAU,OAAO,IAAI,OAAO,UAAU,OAAO,CAAC,UAAU,KAAK,YAAY;wCAC3E,MAAM,aAAa,UAAU,OAAO,CAAC,UAAU;wCAC/C,SAAS;oCACX;gCACF,EAAE,OAAO,OAAO;oCACd,QAAQ,KAAK,CAAC,oBAAoB;gCACpC;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,aAAa;oBACf;gBACF;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,aAAa;QACf;IACF,GAAG;QAAC;QAAiB;QAAS;QAAU;QAAQ;QAAQ;KAAM;IAE9D,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBACA,UAAU,OAAO,IACjB,OAAO,UAAU,OAAO,CAAC,UAAU,KAAK,cACxC,OAAO,UAAU,OAAO,CAAC,UAAU,KAAK,YAAY;YACtD,IAAI;gBACF,MAAM,iBAAiB,UAAU,OAAO,CAAC,UAAU;gBACnD,IAAI,YAAY,gBAAgB;oBAC9B,UAAU,OAAO,CAAC,UAAU,CAAC;gBAC/B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;YAClC;QACF;IACF,GAAG;QAAC;QAAS;KAAe;IAE5B,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAyB;IAE7B,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAiB;KAAY;IAEjC,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,IAAI,OAAO,UAAU,OAAO,CAAC,OAAO,KAAK,YAAY;gBACxE,IAAI;oBACF,UAAU,OAAO,CAAC,OAAO;oBACzB,UAAU,OAAO,GAAG;gBACtB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAChC;YACF;QACF;IACF,GAAG,EAAE;IAEL,0BAA0B;IAC1B,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW;sBAEd,cAAA,8OAAC,kJAAA,CAAA,yBAAsB;gBACrB,SAAS;gBACT,UAAU;gBACV,WAAU;;;;;;;;;;;IAIlB;IAEA,aAAa;IACb,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAW,GAAG,UAAU,6FAA6F,CAAC;sBACzH,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BAEd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAA2C;;;;;;kCAC3D,8OAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAW,CAAC,0BAA0B,EACpC,CAAC,aACG,2BACA,iEACJ;kCACH;;;;;;kCAGD,8OAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAW,CAAC,0BAA0B,EACpC,aACI,2BACA,iEACJ;kCACH;;;;;;;;;;;;YAMF,aACC,YAAY;0BACZ,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAO;;;;;2DAG7B,aAAa;0BACb,8OAAC;gBACC,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,WAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAQ,YAAY;gBAAM;gBAC3C,aAAY;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BACE,aAAa,oBAAoB;4BAAgB;4BAAS,QAAQ,MAAM;;;;;;;kCAE3E,8OAAC;kCACE,aAAa,wBAAwB;;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 7882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/intelligentFormatting.ts"], "sourcesContent": ["import { aiService } from './ai';\r\n\r\nexport interface TOCItem {\r\n  level: number;\r\n  title: string;\r\n  anchor: string;\r\n  line: number;\r\n}\r\n\r\nexport interface FormatSuggestion {\r\n  type: 'heading' | 'paragraph' | 'list' | 'table' | 'code' | 'quote' | 'link';\r\n  line: number;\r\n  column?: number;\r\n  message: string;\r\n  suggestion: string;\r\n  severity: 'info' | 'warning' | 'error';\r\n}\r\n\r\nexport interface DocumentStructure {\r\n  toc: TOCItem[];\r\n  wordCount: number;\r\n  readingTime: number;\r\n  headingCount: { [level: number]: number };\r\n  suggestions: FormatSuggestion[];\r\n}\r\n\r\nclass IntelligentFormattingService {\r\n  // 生成目录\r\n  generateTOC(content: string): TOCItem[] {\r\n    const lines = content.split('\\n');\r\n    const toc: TOCItem[] = [];\r\n    \r\n    lines.forEach((line, index) => {\r\n      const headingMatch = line.match(/^(#{1,6})\\s+(.+)$/);\r\n      if (headingMatch) {\r\n        const level = headingMatch[1].length;\r\n        const title = headingMatch[2].trim();\r\n        const anchor = this.generateAnchor(title);\r\n        \r\n        toc.push({\r\n          level,\r\n          title,\r\n          anchor,\r\n          line: index + 1\r\n        });\r\n      }\r\n    });\r\n    \r\n    return toc;\r\n  }\r\n\r\n  // 生成锚点\r\n  private generateAnchor(title: string): string {\r\n    return title\r\n      .toLowerCase()\r\n      .replace(/[^\\w\\s\\u4e00-\\u9fff]/g, '') // 保留中文字符\r\n      .replace(/\\s+/g, '-')\r\n      .trim();\r\n  }\r\n\r\n  // 插入目录到文档\r\n  insertTOCToDocument(content: string, tocPosition: 'top' | 'after-title' = 'after-title'): string {\r\n    const toc = this.generateTOC(content);\r\n    if (toc.length === 0) return content;\r\n\r\n    const tocMarkdown = this.generateTOCMarkdown(toc);\r\n    const lines = content.split('\\n');\r\n    \r\n    if (tocPosition === 'top') {\r\n      return tocMarkdown + '\\n\\n' + content;\r\n    } else {\r\n      // 在第一个标题后插入\r\n      const firstHeadingIndex = lines.findIndex(line => line.match(/^#\\s+/));\r\n      if (firstHeadingIndex !== -1) {\r\n        lines.splice(firstHeadingIndex + 1, 0, '', tocMarkdown, '');\r\n        return lines.join('\\n');\r\n      } else {\r\n        return tocMarkdown + '\\n\\n' + content;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 生成目录Markdown\r\n  private generateTOCMarkdown(toc: TOCItem[]): string {\r\n    const tocLines = ['## 目录', ''];\r\n    \r\n    toc.forEach(item => {\r\n      const indent = '  '.repeat(item.level - 1);\r\n      const link = `[${item.title}](#${item.anchor})`;\r\n      tocLines.push(`${indent}- ${link}`);\r\n    });\r\n    \r\n    return tocLines.join('\\n');\r\n  }\r\n\r\n  // 分析文档结构\r\n  analyzeDocumentStructure(content: string): DocumentStructure {\r\n    const toc = this.generateTOC(content);\r\n    const wordCount = this.countWords(content);\r\n    const readingTime = this.calculateReadingTime(wordCount);\r\n    const headingCount = this.countHeadings(toc);\r\n    const suggestions = this.generateFormatSuggestions(content);\r\n\r\n    return {\r\n      toc,\r\n      wordCount,\r\n      readingTime,\r\n      headingCount,\r\n      suggestions\r\n    };\r\n  }\r\n\r\n  // 计算字数\r\n  private countWords(content: string): number {\r\n    // 移除Markdown语法\r\n    const plainText = content\r\n      .replace(/```[\\s\\S]*?```/g, '') // 代码块\r\n      .replace(/`[^`]+`/g, '') // 行内代码\r\n      .replace(/!\\[.*?\\]\\(.*?\\)/g, '') // 图片\r\n      .replace(/\\[.*?\\]\\(.*?\\)/g, '') // 链接\r\n      .replace(/[#*_~`]/g, '') // Markdown符号\r\n      .replace(/\\n+/g, ' '); // 换行符\r\n\r\n    // 中文字符计数\r\n    const chineseChars = (plainText.match(/[\\u4e00-\\u9fff]/g) || []).length;\r\n    // 英文单词计数\r\n    const englishWords = plainText\r\n      .replace(/[\\u4e00-\\u9fff]/g, ' ')\r\n      .split(/\\s+/)\r\n      .filter(word => word.length > 0).length;\r\n\r\n    return chineseChars + englishWords;\r\n  }\r\n\r\n  // 计算阅读时间（分钟）\r\n  private calculateReadingTime(wordCount: number): number {\r\n    // 中文阅读速度约300字/分钟，英文约200词/分钟\r\n    // 这里简化为平均250字/分钟\r\n    return Math.ceil(wordCount / 250);\r\n  }\r\n\r\n  // 统计标题数量\r\n  private countHeadings(toc: TOCItem[]): { [level: number]: number } {\r\n    const count: { [level: number]: number } = {};\r\n    toc.forEach(item => {\r\n      count[item.level] = (count[item.level] || 0) + 1;\r\n    });\r\n    return count;\r\n  }\r\n\r\n  // 生成格式建议\r\n  generateFormatSuggestions(content: string): FormatSuggestion[] {\r\n    const suggestions: FormatSuggestion[] = [];\r\n    const lines = content.split('\\n');\r\n\r\n    lines.forEach((line, index) => {\r\n      const lineNumber = index + 1;\r\n\r\n      // 检查标题格式\r\n      if (line.match(/^#{1,6}/)) {\r\n        // 标题后应该有空格\r\n        if (!line.match(/^#{1,6}\\s+/)) {\r\n          suggestions.push({\r\n            type: 'heading',\r\n            line: lineNumber,\r\n            message: '标题符号后应该有空格',\r\n            suggestion: line.replace(/^(#{1,6})/, '$1 '),\r\n            severity: 'warning'\r\n          });\r\n        }\r\n\r\n        // 标题不应该以标点符号结尾\r\n        if (line.match(/[。！？：；，]$/)) {\r\n          suggestions.push({\r\n            type: 'heading',\r\n            line: lineNumber,\r\n            message: '标题不应该以标点符号结尾',\r\n            suggestion: line.replace(/[。！？：；，]+$/, ''),\r\n            severity: 'info'\r\n          });\r\n        }\r\n      }\r\n\r\n      // 检查列表格式\r\n      if (line.match(/^\\s*[-*+]\\s/)) {\r\n        // 列表项缩进应该一致\r\n        const indent = line.match(/^(\\s*)/)?.[1] || '';\r\n        if (indent.length % 2 !== 0) {\r\n          suggestions.push({\r\n            type: 'list',\r\n            line: lineNumber,\r\n            message: '列表缩进应该使用2个空格的倍数',\r\n            suggestion: line.replace(/^(\\s*)/, ' '.repeat(Math.floor(indent.length / 2) * 2)),\r\n            severity: 'info'\r\n          });\r\n        }\r\n      }\r\n\r\n      // 检查段落格式\r\n      if (line.trim() && !line.match(/^[#\\s*-+>|`]/)) {\r\n        // 段落过长提醒\r\n        if (line.length > 100) {\r\n          suggestions.push({\r\n            type: 'paragraph',\r\n            line: lineNumber,\r\n            message: '段落过长，建议分段或使用列表',\r\n            suggestion: '考虑在适当位置分段',\r\n            severity: 'info'\r\n          });\r\n        }\r\n\r\n        // 中英文间应该有空格\r\n        if (line.match(/[\\u4e00-\\u9fff][a-zA-Z]|[a-zA-Z][\\u4e00-\\u9fff]/)) {\r\n          const fixed = line.replace(/([\\u4e00-\\u9fff])([a-zA-Z])/g, '$1 $2')\r\n                           .replace(/([a-zA-Z])([\\u4e00-\\u9fff])/g, '$1 $2');\r\n          if (fixed !== line) {\r\n            suggestions.push({\r\n              type: 'paragraph',\r\n              line: lineNumber,\r\n              message: '中英文之间应该有空格',\r\n              suggestion: fixed,\r\n              severity: 'warning'\r\n            });\r\n          }\r\n        }\r\n\r\n        // 中文数字间应该有空格\r\n        if (line.match(/[\\u4e00-\\u9fff]\\d|\\d[\\u4e00-\\u9fff]/)) {\r\n          const fixed = line.replace(/([\\u4e00-\\u9fff])(\\d)/g, '$1 $2')\r\n                           .replace(/(\\d)([\\u4e00-\\u9fff])/g, '$1 $2');\r\n          if (fixed !== line) {\r\n            suggestions.push({\r\n              type: 'paragraph',\r\n              line: lineNumber,\r\n              message: '中文与数字之间应该有空格',\r\n              suggestion: fixed,\r\n              severity: 'warning'\r\n            });\r\n          }\r\n        }\r\n\r\n        // 检查多余的空格\r\n        if (line.match(/\\s{2,}/)) {\r\n          const fixed = line.replace(/\\s{2,}/g, ' ');\r\n          if (fixed !== line) {\r\n            suggestions.push({\r\n              type: 'paragraph',\r\n              line: lineNumber,\r\n              message: '移除多余的空格',\r\n              suggestion: fixed,\r\n              severity: 'warning'\r\n            });\r\n          }\r\n        }\r\n\r\n        // 检查行尾空格\r\n        if (line.match(/\\s+$/)) {\r\n          suggestions.push({\r\n            type: 'paragraph',\r\n            line: lineNumber,\r\n            message: '移除行尾空格',\r\n            suggestion: line.replace(/\\s+$/, ''),\r\n            severity: 'warning'\r\n          });\r\n        }\r\n      }\r\n\r\n      // 检查空行\r\n      if (index > 0 && line.trim() === '' && lines[index - 1].trim() === '') {\r\n        suggestions.push({\r\n          type: 'paragraph',\r\n          line: lineNumber,\r\n          message: '避免连续空行',\r\n          suggestion: '删除多余空行',\r\n          severity: 'info'\r\n        });\r\n      }\r\n    });\r\n\r\n    return suggestions;\r\n  }\r\n\r\n  // 自动修复格式问题\r\n  autoFixFormatting(content: string): string {\r\n    const suggestions = this.generateFormatSuggestions(content);\r\n    let lines = content.split('\\n');\r\n    let fixedCount = 0;\r\n\r\n    // 按行号倒序处理，避免行号偏移\r\n    // 修复所有可以自动修复的问题，不仅仅是警告级别的\r\n    const validSuggestions = suggestions\r\n      .filter(s => {\r\n        // 过滤掉不能自动修复的建议\r\n        return s.suggestion &&\r\n               s.suggestion !== '删除多余空行' &&\r\n               s.suggestion !== '考虑在适当位置分段' &&\r\n               !s.suggestion.includes('考虑') &&\r\n               !s.suggestion.includes('建议');\r\n      })\r\n      .sort((a, b) => b.line - a.line);\r\n\r\n    // 按行分组，避免同一行的多次修复冲突\r\n    const lineGroups = new Map();\r\n    validSuggestions.forEach(suggestion => {\r\n      const line = suggestion.line;\r\n      if (!lineGroups.has(line)) {\r\n        lineGroups.set(line, []);\r\n      }\r\n      lineGroups.get(line).push(suggestion);\r\n    });\r\n\r\n    // 对每一行应用所有修复\r\n    Array.from(lineGroups.entries())\r\n      .sort(([a], [b]) => b - a) // 按行号倒序\r\n      .forEach(([lineNumber, lineSuggestions]) => {\r\n        const lineIndex = lineNumber - 1;\r\n        if (lineIndex >= 0 && lineIndex < lines.length) {\r\n          let currentLine = lines[lineIndex];\r\n          const originalLine = currentLine;\r\n\r\n          // 按优先级应用修复（标题格式优先）\r\n          lineSuggestions\r\n            .sort((a, b) => {\r\n              if (a.type === 'heading' && b.type !== 'heading') return -1;\r\n              if (a.type !== 'heading' && b.type === 'heading') return 1;\r\n              return 0;\r\n            })\r\n            .forEach(suggestion => {\r\n              // 重新计算修复，因为前面的修复可能已经改变了内容\r\n              let newSuggestion = suggestion.suggestion;\r\n\r\n              // 对于标题格式，重新计算\r\n              if (suggestion.type === 'heading') {\r\n                if (suggestion.message.includes('空格') && !currentLine.match(/^#{1,6}\\s+/)) {\r\n                  newSuggestion = currentLine.replace(/^(#{1,6})/, '$1 ');\r\n                } else if (suggestion.message.includes('标点符号') && currentLine.match(/[。！？：；，]$/)) {\r\n                  newSuggestion = currentLine.replace(/[。！？：；，]+$/, '');\r\n                } else {\r\n                  return; // 已经修复过了\r\n                }\r\n              }\r\n\r\n              if (currentLine !== newSuggestion) {\r\n                currentLine = newSuggestion;\r\n              }\r\n            });\r\n\r\n          if (originalLine !== currentLine) {\r\n            lines[lineIndex] = currentLine;\r\n            fixedCount++;\r\n            console.log(`修复第${lineNumber}行: ${lineSuggestions.map(s => s.message).join(', ')}`);\r\n          }\r\n        }\r\n      });\r\n\r\n    // 移除连续空行\r\n    const result = [];\r\n    let lastWasEmpty = false;\r\n    let removedEmptyLines = 0;\r\n\r\n    for (const line of lines) {\r\n      if (line.trim() === '') {\r\n        if (!lastWasEmpty) {\r\n          result.push(line);\r\n          lastWasEmpty = true;\r\n        } else {\r\n          removedEmptyLines++;\r\n        }\r\n      } else {\r\n        result.push(line);\r\n        lastWasEmpty = false;\r\n      }\r\n    }\r\n\r\n    if (removedEmptyLines > 0) {\r\n      fixedCount += removedEmptyLines;\r\n      console.log(`移除了${removedEmptyLines}个多余空行`);\r\n    }\r\n\r\n    console.log(`自动修复完成，共修复${fixedCount}个问题`);\r\n    return result.join('\\n');\r\n  }\r\n\r\n  // 分析修复前后的差异\r\n  analyzeFixDifferences(originalContent: string, fixedContent: string): {\r\n    changedLines: number;\r\n    addedLines: number;\r\n    removedLines: number;\r\n    changes: Array<{ line: number; type: 'modified' | 'added' | 'removed'; original?: string; fixed?: string }>\r\n  } {\r\n    const originalLines = originalContent.split('\\n');\r\n    const fixedLines = fixedContent.split('\\n');\r\n    const changes: Array<{ line: number; type: 'modified' | 'added' | 'removed'; original?: string; fixed?: string }> = [];\r\n\r\n    let changedLines = 0;\r\n    let addedLines = 0;\r\n    let removedLines = 0;\r\n\r\n    const maxLength = Math.max(originalLines.length, fixedLines.length);\r\n\r\n    for (let i = 0; i < maxLength; i++) {\r\n      const originalLine = originalLines[i];\r\n      const fixedLine = fixedLines[i];\r\n\r\n      if (originalLine !== fixedLine) {\r\n        if (originalLine === undefined) {\r\n          // 新增行\r\n          addedLines++;\r\n          changes.push({ line: i + 1, type: 'added', fixed: fixedLine });\r\n        } else if (fixedLine === undefined) {\r\n          // 删除行\r\n          removedLines++;\r\n          changes.push({ line: i + 1, type: 'removed', original: originalLine });\r\n        } else {\r\n          // 修改行\r\n          changedLines++;\r\n          changes.push({ line: i + 1, type: 'modified', original: originalLine, fixed: fixedLine });\r\n        }\r\n      }\r\n    }\r\n\r\n    return { changedLines, addedLines, removedLines, changes };\r\n  }\r\n\r\n  // AI智能自动修复\r\n  async autoFixFormattingWithAI(content: string, customPrompt?: string): Promise<{ success: boolean; result: string; error?: string; isAIFixed?: boolean; analysis?: any }> {\r\n    try {\r\n      // 检查AI服务是否已初始化\r\n      if (!aiService.isInitialized()) {\r\n        console.log('AI服务未配置，使用本地修复');\r\n        return {\r\n          success: true,\r\n          result: this.autoFixFormatting(content),\r\n          isAIFixed: false\r\n        };\r\n      }\r\n\r\n      console.log('使用AI进行智能自动修复...');\r\n      const response = await aiService.intelligentAutoFix(content, customPrompt);\r\n\r\n      if (response.success && response.result) {\r\n        console.log('AI智能修复成功');\r\n\r\n        // 分析修复结果\r\n        const analysis = this.analyzeFixDifferences(content, response.result);\r\n        console.log('修复分析:', analysis);\r\n\r\n        return {\r\n          success: true,\r\n          result: response.result,\r\n          isAIFixed: true,\r\n          analysis\r\n        };\r\n      } else {\r\n        console.log('AI修复失败，使用本地修复作为备选:', response.error);\r\n        const localResult = this.autoFixFormatting(content);\r\n        const analysis = this.analyzeFixDifferences(content, localResult);\r\n\r\n        return {\r\n          success: true,\r\n          result: localResult,\r\n          error: response.error,\r\n          isAIFixed: false,\r\n          analysis\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('AI智能修复出错:', error);\r\n      const localResult = this.autoFixFormatting(content);\r\n      const analysis = this.analyzeFixDifferences(content, localResult);\r\n\r\n      return {\r\n        success: true,\r\n        result: localResult,\r\n        error: error instanceof Error ? error.message : '未知错误',\r\n        isAIFixed: false,\r\n        analysis\r\n      };\r\n    }\r\n  }\r\n\r\n  // 使用AI优化文档格式\r\n  async optimizeWithAI(content: string, formatType: 'academic' | 'business' | 'blog' | 'general' = 'general'): Promise<string> {\r\n    try {\r\n      // 检查AI服务是否已初始化\r\n      if (!aiService.isInitialized()) {\r\n        throw new Error('AI服务未配置，请先在AI助手中配置API密钥');\r\n      }\r\n\r\n      // 根据格式类型构建专门的提示词\r\n      const formatPrompts = {\r\n        academic: `请按照学术论文格式标准优化以下Markdown文档：\r\n1. 标题层级清晰，使用标准的学术格式\r\n2. 段落结构合理，逻辑清晰\r\n3. 引用格式规范\r\n4. 图表标注准确\r\n5. 参考文献格式统一\r\n\r\n文档内容：\r\n${content}`,\r\n        business: `请按照商务报告格式标准优化以下Markdown文档：\r\n1. 执行摘要突出\r\n2. 数据图表清晰\r\n3. 结论建议明确\r\n4. 格式专业规范\r\n5. 重点内容突出\r\n\r\n文档内容：\r\n${content}`,\r\n        blog: `请按照博客文章格式标准优化以下Markdown文档：\r\n1. 标题吸引人，层级清晰\r\n2. 段落简洁易读\r\n3. 列表和要点突出\r\n4. 适合网络阅读\r\n5. 互动性强\r\n\r\n文档内容：\r\n${content}`,\r\n        general: `请优化以下Markdown文档的格式和结构，使其更加清晰易读：\r\n\r\n文档内容：\r\n${content}`\r\n      };\r\n\r\n      // 使用精准格式优化方法\r\n      const response = await aiService.formatDocumentPrecise(content, formatType, formatPrompts[formatType]);\r\n\r\n      if (response.success && response.result) {\r\n        return response.result;\r\n      } else {\r\n        throw new Error(response.error || 'AI优化失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('AI格式优化失败:', error);\r\n\r\n      // 如果是配置问题，抛出错误让用户知道\r\n      if (error instanceof Error && error.message.includes('未配置')) {\r\n        throw error;\r\n      }\r\n\r\n      // 其他错误，使用本地自动修复作为备选\r\n      console.log('使用本地自动修复作为备选方案');\r\n      return this.autoFixFormatting(content);\r\n    }\r\n  }\r\n\r\n  // 生成文档统计报告\r\n  generateDocumentReport(content: string): string {\r\n    const structure = this.analyzeDocumentStructure(content);\r\n    \r\n    const report = [\r\n      '# 文档分析报告',\r\n      '',\r\n      '## 基本信息',\r\n      `- 总字数：${structure.wordCount}`,\r\n      `- 预计阅读时间：${structure.readingTime} 分钟`,\r\n      `- 标题数量：${Object.values(structure.headingCount).reduce((a, b) => a + b, 0)}`,\r\n      '',\r\n      '## 标题分布',\r\n    ];\r\n\r\n    Object.entries(structure.headingCount).forEach(([level, count]) => {\r\n      report.push(`- H${level}：${count} 个`);\r\n    });\r\n\r\n    if (structure.suggestions.length > 0) {\r\n      report.push('', '## 格式建议');\r\n      structure.suggestions.slice(0, 10).forEach((suggestion, index) => {\r\n        report.push(`${index + 1}. 第${suggestion.line}行：${suggestion.message}`);\r\n      });\r\n\r\n      if (structure.suggestions.length > 10) {\r\n        report.push(`... 还有 ${structure.suggestions.length - 10} 条建议`);\r\n      }\r\n    }\r\n\r\n    return report.join('\\n');\r\n  }\r\n}\r\n\r\nexport const intelligentFormatting = new IntelligentFormattingService();\r\n"], "names": [], "mappings": ";;;AAAA;;AA0BA,MAAM;IACJ,OAAO;IACP,YAAY,OAAe,EAAa;QACtC,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,MAAM,MAAiB,EAAE;QAEzB,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,IAAI,cAAc;gBAChB,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,MAAM;gBACpC,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,IAAI;gBAClC,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC;gBAEnC,IAAI,IAAI,CAAC;oBACP;oBACA;oBACA;oBACA,MAAM,QAAQ;gBAChB;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAO;IACC,eAAe,KAAa,EAAU;QAC5C,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,yBAAyB,IAAI,SAAS;SAC9C,OAAO,CAAC,QAAQ,KAChB,IAAI;IACT;IAEA,UAAU;IACV,oBAAoB,OAAe,EAAE,cAAqC,aAAa,EAAU;QAC/F,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC;QAC7B,IAAI,IAAI,MAAM,KAAK,GAAG,OAAO;QAE7B,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC;QAC7C,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,IAAI,gBAAgB,OAAO;YACzB,OAAO,cAAc,SAAS;QAChC,OAAO;YACL,YAAY;YACZ,MAAM,oBAAoB,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC;YAC7D,IAAI,sBAAsB,CAAC,GAAG;gBAC5B,MAAM,MAAM,CAAC,oBAAoB,GAAG,GAAG,IAAI,aAAa;gBACxD,OAAO,MAAM,IAAI,CAAC;YACpB,OAAO;gBACL,OAAO,cAAc,SAAS;YAChC;QACF;IACF;IAEA,eAAe;IACP,oBAAoB,GAAc,EAAU;QAClD,MAAM,WAAW;YAAC;YAAS;SAAG;QAE9B,IAAI,OAAO,CAAC,CAAA;YACV,MAAM,SAAS,KAAK,MAAM,CAAC,KAAK,KAAK,GAAG;YACxC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC;YAC/C,SAAS,IAAI,CAAC,GAAG,OAAO,EAAE,EAAE,MAAM;QACpC;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,SAAS;IACT,yBAAyB,OAAe,EAAqB;QAC3D,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC;QAC7B,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC;QAC9C,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC;QACxC,MAAM,cAAc,IAAI,CAAC,yBAAyB,CAAC;QAEnD,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,OAAO;IACC,WAAW,OAAe,EAAU;QAC1C,eAAe;QACf,MAAM,YAAY,QACf,OAAO,CAAC,mBAAmB,IAAI,MAAM;SACrC,OAAO,CAAC,YAAY,IAAI,OAAO;SAC/B,OAAO,CAAC,oBAAoB,IAAI,KAAK;SACrC,OAAO,CAAC,mBAAmB,IAAI,KAAK;SACpC,OAAO,CAAC,YAAY,IAAI,aAAa;SACrC,OAAO,CAAC,QAAQ,MAAM,MAAM;QAE/B,SAAS;QACT,MAAM,eAAe,CAAC,UAAU,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM;QACvE,SAAS;QACT,MAAM,eAAe,UAClB,OAAO,CAAC,oBAAoB,KAC5B,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;QAEzC,OAAO,eAAe;IACxB;IAEA,aAAa;IACL,qBAAqB,SAAiB,EAAU;QACtD,4BAA4B;QAC5B,iBAAiB;QACjB,OAAO,KAAK,IAAI,CAAC,YAAY;IAC/B;IAEA,SAAS;IACD,cAAc,GAAc,EAA+B;QACjE,MAAM,QAAqC,CAAC;QAC5C,IAAI,OAAO,CAAC,CAAA;YACV,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI;QACjD;QACA,OAAO;IACT;IAEA,SAAS;IACT,0BAA0B,OAAe,EAAsB;QAC7D,MAAM,cAAkC,EAAE;QAC1C,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,MAAM,aAAa,QAAQ;YAE3B,SAAS;YACT,IAAI,KAAK,KAAK,CAAC,YAAY;gBACzB,WAAW;gBACX,IAAI,CAAC,KAAK,KAAK,CAAC,eAAe;oBAC7B,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,YAAY,KAAK,OAAO,CAAC,aAAa;wBACtC,UAAU;oBACZ;gBACF;gBAEA,eAAe;gBACf,IAAI,KAAK,KAAK,CAAC,cAAc;oBAC3B,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,YAAY,KAAK,OAAO,CAAC,cAAc;wBACvC,UAAU;oBACZ;gBACF;YACF;YAEA,SAAS;YACT,IAAI,KAAK,KAAK,CAAC,gBAAgB;gBAC7B,YAAY;gBACZ,MAAM,SAAS,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI;gBAC5C,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;oBAC3B,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,YAAY,KAAK,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG,KAAK;wBAC9E,UAAU;oBACZ;gBACF;YACF;YAEA,SAAS;YACT,IAAI,KAAK,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,iBAAiB;gBAC9C,SAAS;gBACT,IAAI,KAAK,MAAM,GAAG,KAAK;oBACrB,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,YAAY;wBACZ,UAAU;oBACZ;gBACF;gBAEA,YAAY;gBACZ,IAAI,KAAK,KAAK,CAAC,oDAAoD;oBACjE,MAAM,QAAQ,KAAK,OAAO,CAAC,gCAAgC,SACzC,OAAO,CAAC,gCAAgC;oBAC1D,IAAI,UAAU,MAAM;wBAClB,YAAY,IAAI,CAAC;4BACf,MAAM;4BACN,MAAM;4BACN,SAAS;4BACT,YAAY;4BACZ,UAAU;wBACZ;oBACF;gBACF;gBAEA,aAAa;gBACb,IAAI,KAAK,KAAK,CAAC,wCAAwC;oBACrD,MAAM,QAAQ,KAAK,OAAO,CAAC,0BAA0B,SACnC,OAAO,CAAC,0BAA0B;oBACpD,IAAI,UAAU,MAAM;wBAClB,YAAY,IAAI,CAAC;4BACf,MAAM;4BACN,MAAM;4BACN,SAAS;4BACT,YAAY;4BACZ,UAAU;wBACZ;oBACF;gBACF;gBAEA,UAAU;gBACV,IAAI,KAAK,KAAK,CAAC,WAAW;oBACxB,MAAM,QAAQ,KAAK,OAAO,CAAC,WAAW;oBACtC,IAAI,UAAU,MAAM;wBAClB,YAAY,IAAI,CAAC;4BACf,MAAM;4BACN,MAAM;4BACN,SAAS;4BACT,YAAY;4BACZ,UAAU;wBACZ;oBACF;gBACF;gBAEA,SAAS;gBACT,IAAI,KAAK,KAAK,CAAC,SAAS;oBACtB,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,YAAY,KAAK,OAAO,CAAC,QAAQ;wBACjC,UAAU;oBACZ;gBACF;YACF;YAEA,OAAO;YACP,IAAI,QAAQ,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,OAAO,IAAI;gBACrE,YAAY,IAAI,CAAC;oBACf,MAAM;oBACN,MAAM;oBACN,SAAS;oBACT,YAAY;oBACZ,UAAU;gBACZ;YACF;QACF;QAEA,OAAO;IACT;IAEA,WAAW;IACX,kBAAkB,OAAe,EAAU;QACzC,MAAM,cAAc,IAAI,CAAC,yBAAyB,CAAC;QACnD,IAAI,QAAQ,QAAQ,KAAK,CAAC;QAC1B,IAAI,aAAa;QAEjB,iBAAiB;QACjB,0BAA0B;QAC1B,MAAM,mBAAmB,YACtB,MAAM,CAAC,CAAA;YACN,eAAe;YACf,OAAO,EAAE,UAAU,IACZ,EAAE,UAAU,KAAK,YACjB,EAAE,UAAU,KAAK,eACjB,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,SACvB,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC;QAChC,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;QAEjC,oBAAoB;QACpB,MAAM,aAAa,IAAI;QACvB,iBAAiB,OAAO,CAAC,CAAA;YACvB,MAAM,OAAO,WAAW,IAAI;YAC5B,IAAI,CAAC,WAAW,GAAG,CAAC,OAAO;gBACzB,WAAW,GAAG,CAAC,MAAM,EAAE;YACzB;YACA,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC;QAC5B;QAEA,aAAa;QACb,MAAM,IAAI,CAAC,WAAW,OAAO,IAC1B,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAK,IAAI,GAAG,QAAQ;SAClC,OAAO,CAAC,CAAC,CAAC,YAAY,gBAAgB;YACrC,MAAM,YAAY,aAAa;YAC/B,IAAI,aAAa,KAAK,YAAY,MAAM,MAAM,EAAE;gBAC9C,IAAI,cAAc,KAAK,CAAC,UAAU;gBAClC,MAAM,eAAe;gBAErB,mBAAmB;gBACnB,gBACG,IAAI,CAAC,CAAC,GAAG;oBACR,IAAI,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,WAAW,OAAO,CAAC;oBAC1D,IAAI,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,WAAW,OAAO;oBACzD,OAAO;gBACT,GACC,OAAO,CAAC,CAAA;oBACP,0BAA0B;oBAC1B,IAAI,gBAAgB,WAAW,UAAU;oBAEzC,cAAc;oBACd,IAAI,WAAW,IAAI,KAAK,WAAW;wBACjC,IAAI,WAAW,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,KAAK,CAAC,eAAe;4BACzE,gBAAgB,YAAY,OAAO,CAAC,aAAa;wBACnD,OAAO,IAAI,WAAW,OAAO,CAAC,QAAQ,CAAC,WAAW,YAAY,KAAK,CAAC,cAAc;4BAChF,gBAAgB,YAAY,OAAO,CAAC,cAAc;wBACpD,OAAO;4BACL,QAAQ,SAAS;wBACnB;oBACF;oBAEA,IAAI,gBAAgB,eAAe;wBACjC,cAAc;oBAChB;gBACF;gBAEF,IAAI,iBAAiB,aAAa;oBAChC,KAAK,CAAC,UAAU,GAAG;oBACnB;oBACA,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,WAAW,GAAG,EAAE,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBACpF;YACF;QACF;QAEF,SAAS;QACT,MAAM,SAAS,EAAE;QACjB,IAAI,eAAe;QACnB,IAAI,oBAAoB;QAExB,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,OAAO,IAAI;gBACtB,IAAI,CAAC,cAAc;oBACjB,OAAO,IAAI,CAAC;oBACZ,eAAe;gBACjB,OAAO;oBACL;gBACF;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;gBACZ,eAAe;YACjB;QACF;QAEA,IAAI,oBAAoB,GAAG;YACzB,cAAc;YACd,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,kBAAkB,KAAK,CAAC;QAC5C;QAEA,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,GAAG,CAAC;QACxC,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,YAAY;IACZ,sBAAsB,eAAuB,EAAE,YAAoB,EAKjE;QACA,MAAM,gBAAgB,gBAAgB,KAAK,CAAC;QAC5C,MAAM,aAAa,aAAa,KAAK,CAAC;QACtC,MAAM,UAA8G,EAAE;QAEtH,IAAI,eAAe;QACnB,IAAI,aAAa;QACjB,IAAI,eAAe;QAEnB,MAAM,YAAY,KAAK,GAAG,CAAC,cAAc,MAAM,EAAE,WAAW,MAAM;QAElE,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,MAAM,eAAe,aAAa,CAAC,EAAE;YACrC,MAAM,YAAY,UAAU,CAAC,EAAE;YAE/B,IAAI,iBAAiB,WAAW;gBAC9B,IAAI,iBAAiB,WAAW;oBAC9B,MAAM;oBACN;oBACA,QAAQ,IAAI,CAAC;wBAAE,MAAM,IAAI;wBAAG,MAAM;wBAAS,OAAO;oBAAU;gBAC9D,OAAO,IAAI,cAAc,WAAW;oBAClC,MAAM;oBACN;oBACA,QAAQ,IAAI,CAAC;wBAAE,MAAM,IAAI;wBAAG,MAAM;wBAAW,UAAU;oBAAa;gBACtE,OAAO;oBACL,MAAM;oBACN;oBACA,QAAQ,IAAI,CAAC;wBAAE,MAAM,IAAI;wBAAG,MAAM;wBAAY,UAAU;wBAAc,OAAO;oBAAU;gBACzF;YACF;QACF;QAEA,OAAO;YAAE;YAAc;YAAY;YAAc;QAAQ;IAC3D;IAEA,WAAW;IACX,MAAM,wBAAwB,OAAe,EAAE,YAAqB,EAAsG;QACxK,IAAI;YACF,eAAe;YACf,IAAI,CAAC,kHAAA,CAAA,YAAS,CAAC,aAAa,IAAI;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,OAAO;oBACL,SAAS;oBACT,QAAQ,IAAI,CAAC,iBAAiB,CAAC;oBAC/B,WAAW;gBACb;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,SAAS;YAE7D,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM,EAAE;gBACvC,QAAQ,GAAG,CAAC;gBAEZ,SAAS;gBACT,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC,SAAS,SAAS,MAAM;gBACpE,QAAQ,GAAG,CAAC,SAAS;gBAErB,OAAO;oBACL,SAAS;oBACT,QAAQ,SAAS,MAAM;oBACvB,WAAW;oBACX;gBACF;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,sBAAsB,SAAS,KAAK;gBAChD,MAAM,cAAc,IAAI,CAAC,iBAAiB,CAAC;gBAC3C,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC,SAAS;gBAErD,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR,OAAO,SAAS,KAAK;oBACrB,WAAW;oBACX;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,cAAc,IAAI,CAAC,iBAAiB,CAAC;YAC3C,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAErD,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,WAAW;gBACX;YACF;QACF;IACF;IAEA,aAAa;IACb,MAAM,eAAe,OAAe,EAAE,aAA2D,SAAS,EAAmB;QAC3H,IAAI;YACF,eAAe;YACf,IAAI,CAAC,kHAAA,CAAA,YAAS,CAAC,aAAa,IAAI;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,iBAAiB;YACjB,MAAM,gBAAgB;gBACpB,UAAU,CAAC;;;;;;;;AAQnB,EAAE,SAAS;gBACH,UAAU,CAAC;;;;;;;;AAQnB,EAAE,SAAS;gBACH,MAAM,CAAC;;;;;;;;AAQf,EAAE,SAAS;gBACH,SAAS,CAAC;;;AAGlB,EAAE,SAAS;YACL;YAEA,aAAa;YACb,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,SAAS,YAAY,aAAa,CAAC,WAAW;YAErG,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM,EAAE;gBACvC,OAAO,SAAS,MAAM;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAE3B,oBAAoB;YACpB,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBAC3D,MAAM;YACR;YAEA,oBAAoB;YACpB,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC;IACF;IAEA,WAAW;IACX,uBAAuB,OAAe,EAAU;QAC9C,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC;QAEhD,MAAM,SAAS;YACb;YACA;YACA;YACA,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE;YAC9B,CAAC,SAAS,EAAE,UAAU,WAAW,CAAC,GAAG,CAAC;YACtC,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,UAAU,YAAY,EAAE,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,IAAI;YAC5E;YACA;SACD;QAED,OAAO,OAAO,CAAC,UAAU,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;YAC5D,OAAO,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;QACtC;QAEA,IAAI,UAAU,WAAW,CAAC,MAAM,GAAG,GAAG;YACpC,OAAO,IAAI,CAAC,IAAI;YAChB,UAAU,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,YAAY;gBACtD,OAAO,IAAI,CAAC,GAAG,QAAQ,EAAE,GAAG,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,OAAO,EAAE;YACxE;YAEA,IAAI,UAAU,WAAW,CAAC,MAAM,GAAG,IAAI;gBACrC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,WAAW,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC;YAC/D;QACF;QAEA,OAAO,OAAO,IAAI,CAAC;IACrB;AACF;AAEO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 8377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/TableOfContentsPage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { intelligentFormatting } from '@/utils/intelligentFormatting';\r\n\r\ninterface TOCItem {\r\n  level: number;\r\n  title: string;\r\n  anchor: string;\r\n  line: number;\r\n}\r\n\r\ninterface TableOfContentsPageProps {\r\n  content: string;\r\n  onNavigate?: (line: number, anchor: string) => void;\r\n  onClose?: () => void;\r\n  className?: string;\r\n}\r\n\r\nexport const TableOfContentsPage: React.FC<TableOfContentsPageProps> = ({\r\n  content,\r\n  onNavigate,\r\n  onClose,\r\n  className = ''\r\n}) => {\r\n  const [toc, setToc] = useState<TOCItem[]>([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filteredToc, setFilteredToc] = useState<TOCItem[]>([]);\r\n\r\n  // 生成目录\r\n  useEffect(() => {\r\n    const generatedToc = intelligentFormatting.generateTOC(content);\r\n    setToc(generatedToc);\r\n    setFilteredToc(generatedToc);\r\n  }, [content]);\r\n\r\n  // 搜索过滤\r\n  useEffect(() => {\r\n    if (searchTerm.trim() === '') {\r\n      setFilteredToc(toc);\r\n    } else {\r\n      const filtered = toc.filter(item =>\r\n        item.title.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n      setFilteredToc(filtered);\r\n    }\r\n  }, [searchTerm, toc]);\r\n\r\n  // 处理目录项点击\r\n  const handleTocItemClick = (item: TOCItem) => {\r\n    if (onNavigate) {\r\n      onNavigate(item.line, item.anchor);\r\n    }\r\n  };\r\n\r\n  // 生成目录层级样式\r\n  const getLevelStyle = (level: number) => {\r\n    const baseIndent = (level - 1) * 20;\r\n    const colors = {\r\n      1: 'text-blue-700 dark:text-blue-300 font-bold text-lg',\r\n      2: 'text-green-700 dark:text-green-300 font-semibold text-base',\r\n      3: 'text-purple-700 dark:text-purple-300 font-medium text-sm',\r\n      4: 'text-orange-700 dark:text-orange-300 text-sm',\r\n      5: 'text-red-700 dark:text-red-300 text-sm',\r\n      6: 'text-gray-700 dark:text-gray-300 text-xs'\r\n    };\r\n    \r\n    return {\r\n      paddingLeft: `${baseIndent}px`,\r\n      className: colors[level as keyof typeof colors] || colors[6]\r\n    };\r\n  };\r\n\r\n  // 获取层级图标\r\n  const getLevelIcon = (level: number) => {\r\n    const icons = {\r\n      1: '📖',\r\n      2: '📝',\r\n      3: '📄',\r\n      4: '📋',\r\n      5: '📌',\r\n      6: '🔸'\r\n    };\r\n    return icons[level as keyof typeof icons] || '•';\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col h-full bg-white dark:bg-gray-900 ${className}`}>\r\n      {/* 页面头部 */}\r\n      <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2\">\r\n            📚 文档目录\r\n          </h1>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            共 {toc.length} 个章节 • 点击跳转到对应位置\r\n          </p>\r\n        </div>\r\n        {onClose && (\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\"\r\n            title=\"关闭目录\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* 搜索框 */}\r\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <input\r\n            type=\"text\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            placeholder=\"搜索目录项...\"\r\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          />\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        {searchTerm && (\r\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n            找到 {filteredToc.length} 个匹配项\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {/* 目录内容 */}\r\n      <div className=\"flex-1 overflow-auto p-4\">\r\n        {filteredToc.length === 0 ? (\r\n          <div className=\"flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400\">\r\n            <div className=\"text-6xl mb-4\">📄</div>\r\n            <h3 className=\"text-lg font-medium mb-2\">\r\n              {toc.length === 0 ? '文档中没有标题' : '没有找到匹配的标题'}\r\n            </h3>\r\n            <p className=\"text-sm text-center\">\r\n              {toc.length === 0 \r\n                ? '在文档中添加标题（# ## ### 等）来生成目录'\r\n                : '尝试使用不同的搜索关键词'\r\n              }\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-2\">\r\n            {filteredToc.map((item, index) => {\r\n              const levelStyle = getLevelStyle(item.level);\r\n              const icon = getLevelIcon(item.level);\r\n              \r\n              return (\r\n                <div\r\n                  key={index}\r\n                  onClick={() => handleTocItemClick(item)}\r\n                  className=\"group cursor-pointer p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 border border-transparent hover:border-gray-200 dark:hover:border-gray-700\"\r\n                  style={{ paddingLeft: `${16 + (item.level - 1) * 20}px` }}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <span className=\"text-lg\">{icon}</span>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <div className={`${levelStyle.className} group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors`}>\r\n                        {searchTerm ? (\r\n                          <span dangerouslySetInnerHTML={{\r\n                            __html: item.title.replace(\r\n                              new RegExp(`(${searchTerm})`, 'gi'),\r\n                              '<mark class=\"bg-yellow-200 dark:bg-yellow-800 px-1 rounded\">$1</mark>'\r\n                            )\r\n                          }} />\r\n                        ) : (\r\n                          item.title\r\n                        )}\r\n                      </div>\r\n                      <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-2\">\r\n                        <span>第 {item.line} 行</span>\r\n                        <span>•</span>\r\n                        <span>H{item.level} 级标题</span>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                      <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* 页面底部统计 */}\r\n      {toc.length > 0 && (\r\n        <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800\">\r\n          <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <span>📊 目录统计</span>\r\n              {[1, 2, 3, 4, 5, 6].map(level => {\r\n                const count = toc.filter(item => item.level === level).length;\r\n                return count > 0 ? (\r\n                  <span key={level} className=\"flex items-center gap-1\">\r\n                    <span className=\"text-xs\">H{level}:</span>\r\n                    <span className=\"font-medium\">{count}</span>\r\n                  </span>\r\n                ) : null;\r\n              })}\r\n            </div>\r\n            <div className=\"text-xs\">\r\n              💡 提示：点击任意标题可跳转到对应位置\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAmBO,MAAM,sBAA0D,CAAC,EACtE,OAAO,EACP,UAAU,EACV,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAE5D,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,qIAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;QACvD,OAAO;QACP,eAAe;IACjB,GAAG;QAAC;KAAQ;IAEZ,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,IAAI,OAAO,IAAI;YAC5B,eAAe;QACjB,OAAO;YACL,MAAM,WAAW,IAAI,MAAM,CAAC,CAAA,OAC1B,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAE1D,eAAe;QACjB;IACF,GAAG;QAAC;QAAY;KAAI;IAEpB,UAAU;IACV,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;YACd,WAAW,KAAK,IAAI,EAAE,KAAK,MAAM;QACnC;IACF;IAEA,WAAW;IACX,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,CAAC,QAAQ,CAAC,IAAI;QACjC,MAAM,SAAS;YACb,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QAEA,OAAO;YACL,aAAa,GAAG,WAAW,EAAE,CAAC;YAC9B,WAAW,MAAM,CAAC,MAA6B,IAAI,MAAM,CAAC,EAAE;QAC9D;IACF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ;YACZ,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,KAAK,CAAC,MAA4B,IAAI;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;;0BAE3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2E;;;;;;0CAGzF,8OAAC;gCAAE,WAAU;;oCAAgD;oCACxD,IAAI,MAAM;oCAAC;;;;;;;;;;;;;oBAGjB,yBACC,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,aAAY;gCACZ,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;oBAI1E,4BACC,8OAAC;wBAAE,WAAU;;4BAAgD;4BACvD,YAAY,MAAM;4BAAC;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,kBACtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCACX,IAAI,MAAM,KAAK,IAAI,YAAY;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCACV,IAAI,MAAM,KAAK,IACZ,8BACA;;;;;;;;;;;6EAKR,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,MAAM;wBACtB,MAAM,aAAa,cAAc,KAAK,KAAK;wBAC3C,MAAM,OAAO,aAAa,KAAK,KAAK;wBAEpC,qBACE,8OAAC;4BAEC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAO;gCAAE,aAAa,GAAG,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;4BAAC;sCAExD,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAW;;;;;;kDAC3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,GAAG,WAAW,SAAS,CAAC,2EAA2E,CAAC;0DACjH,2BACC,8OAAC;oDAAK,yBAAyB;wDAC7B,QAAQ,KAAK,KAAK,CAAC,OAAO,CACxB,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,OAC9B;oDAEJ;;;;;+FAEA,KAAK,KAAK;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAG,KAAK,IAAI;4DAAC;;;;;;;kEACnB,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAK;4DAAE,KAAK,KAAK;4DAAC;;;;;;;;;;;;;;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;2BA5BtE;;;;;oBAkCX;;;;;;;;;;;YAML,IAAI,MAAM,GAAG,mBACZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;gCACL;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAA;oCACtB,MAAM,QAAQ,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,OAAO,MAAM;oCAC7D,OAAO,QAAQ,kBACb,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAK,WAAU;;oDAAU;oDAAE;oDAAM;;;;;;;0DAClC,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;uCAFtB;;;;mFAIT;gCACN;;;;;;;sCAEF,8OAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAQrC", "debugId": null}}, {"offset": {"line": 8840, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/components/ui/IntelligentFormatting.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { intelligentFormatting, DocumentStructure, FormatSuggestion } from '@/utils/intelligentFormatting';\r\nimport { aiService } from '@/utils/ai';\r\nimport { TableOfContentsPage } from './TableOfContentsPage';\r\n\r\ninterface IntelligentFormattingProps {\r\n  content: string;\r\n  onContentChange: (content: string) => void;\r\n  className?: string;\r\n}\r\n\r\nexport const IntelligentFormatting: React.FC<IntelligentFormattingProps> = ({\r\n  content,\r\n  onContentChange,\r\n  className = ''\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [activeTab, setActiveTab] = useState<'toc' | 'suggestions' | 'analysis' | 'optimize'>('toc');\r\n  const [documentStructure, setDocumentStructure] = useState<DocumentStructure | null>(null);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [isAIConfigured, setIsAIConfigured] = useState(false);\r\n  const [fixingProgress, setFixingProgress] = useState('');\r\n  const [fixingResults, setFixingResults] = useState<string[]>([]);\r\n  const [showFixingProcess, setShowFixingProcess] = useState(false);\r\n  const [detailedAnalysis, setDetailedAnalysis] = useState<any>(null);\r\n  const [showDetailedReport, setShowDetailedReport] = useState(false);\r\n  const [customPrompt, setCustomPrompt] = useState('');\r\n  const [showPromptInput, setShowPromptInput] = useState(false);\r\n  const [showTocPage, setShowTocPage] = useState(false);\r\n\r\n  // 分析文档结构\r\n  const analyzeDocument = () => {\r\n    setIsAnalyzing(true);\r\n    try {\r\n      const structure = intelligentFormatting.analyzeDocumentStructure(content);\r\n      setDocumentStructure(structure);\r\n    } catch (error) {\r\n      console.error('文档分析失败:', error);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  // 插入目录\r\n  const insertTOC = () => {\r\n    const newContent = intelligentFormatting.insertTOCToDocument(content);\r\n    onContentChange(newContent);\r\n  };\r\n\r\n  // 处理目录页面导航\r\n  const handleTocNavigation = (line: number, anchor: string) => {\r\n    // 关闭目录页面\r\n    setShowTocPage(false);\r\n\r\n    // 触发导航事件（可以通过props传递给父组件）\r\n    if (onContentChange) {\r\n      // 这里可以添加滚动到指定行的逻辑\r\n      console.log(`导航到第 ${line} 行，锚点: ${anchor}`);\r\n\r\n      // 可以通过自定义事件通知父组件\r\n      const event = new CustomEvent('tocNavigate', {\r\n        detail: { line, anchor }\r\n      });\r\n      window.dispatchEvent(event);\r\n    }\r\n  };\r\n\r\n  // 本地自动修复格式\r\n  const autoFixFormatting = () => {\r\n    const originalContent = content;\r\n    const fixedContent = intelligentFormatting.autoFixFormatting(content);\r\n\r\n    if (fixedContent !== originalContent) {\r\n      onContentChange(fixedContent);\r\n      analyzeDocument(); // 重新分析\r\n\r\n      // 显示修复结果\r\n      setTimeout(() => {\r\n        alert('本地自动修复完成！已修复格式问题，请查看控制台了解详细修复信息。');\r\n      }, 100);\r\n    } else {\r\n      alert('未发现需要修复的格式问题。');\r\n    }\r\n  };\r\n\r\n  // AI智能自动修复\r\n  const aiAutoFixFormatting = async (useCustomPrompt = false) => {\r\n    setIsAnalyzing(true);\r\n    setShowFixingProcess(true);\r\n    setFixingResults([]);\r\n\r\n    try {\r\n      const originalContent = content;\r\n\r\n      // 步骤1：分析文档\r\n      setFixingProgress('🔍 正在分析文档结构和格式问题...');\r\n      await new Promise(resolve => setTimeout(resolve, 800));\r\n\r\n      // 生成本地建议用于对比\r\n      const localSuggestions = intelligentFormatting.generateFormatSuggestions(content);\r\n      setFixingResults(prev => [...prev, `📊 检测到 ${localSuggestions.length} 个潜在格式问题`]);\r\n\r\n      if (localSuggestions.length > 0) {\r\n        const problemTypes = [...new Set(localSuggestions.map(s => s.type))];\r\n        setFixingResults(prev => [...prev, `🎯 问题类型：${problemTypes.join('、')}`]);\r\n      }\r\n\r\n      // 显示自定义提示信息\r\n      if (useCustomPrompt && customPrompt.trim()) {\r\n        setFixingResults(prev => [...prev, `💬 用户指导：${customPrompt.trim()}`]);\r\n      }\r\n\r\n      // 步骤2：AI分析\r\n      setFixingProgress('🤖 AI正在深度分析文档格式...');\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      setFixingResults(prev => [...prev, '🧠 AI正在理解文档上下文和语义结构...']);\r\n\r\n      // 步骤3：执行修复\r\n      setFixingProgress('⚡ AI正在执行智能修复...');\r\n      const customPromptText = useCustomPrompt && customPrompt.trim() ? customPrompt.trim() : undefined;\r\n      const result = await intelligentFormatting.autoFixFormattingWithAI(content, customPromptText);\r\n\r\n      // 步骤4：分析修复结果\r\n      setFixingProgress('📝 正在分析修复结果...');\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n      if (result.success && result.result !== originalContent) {\r\n        // 使用详细的分析数据\r\n        const analysis = result.analysis;\r\n\r\n        setFixingResults(prev => [...prev,\r\n          result.isAIFixed\r\n            ? '✅ AI智能修复成功完成！'\r\n            : '⚠️ AI修复失败，已使用本地修复',\r\n        ]);\r\n\r\n        if (analysis) {\r\n          const { changedLines, addedLines, removedLines, changes } = analysis;\r\n          const totalChanges = changedLines + addedLines + removedLines;\r\n\r\n          setFixingResults(prev => [...prev,\r\n            `📊 修复统计：共处理 ${totalChanges} 处变更`,\r\n            ...(changedLines > 0 ? [`  • 修改了 ${changedLines} 行`] : []),\r\n            ...(addedLines > 0 ? [`  • 新增了 ${addedLines} 行`] : []),\r\n            ...(removedLines > 0 ? [`  • 删除了 ${removedLines} 行`] : [])\r\n          ]);\r\n\r\n          // 显示具体的修复类型\r\n          const changeTypes = new Set();\r\n          changes.forEach(change => {\r\n            if (change.type === 'modified' && change.original && change.fixed) {\r\n              // 分析修改类型\r\n              if (change.original.match(/^#{1,6}[^\\s]/) && change.fixed.match(/^#{1,6}\\s/)) {\r\n                changeTypes.add('标题格式');\r\n              }\r\n              if (change.original.match(/[\\u4e00-\\u9fff][a-zA-Z]|[a-zA-Z][\\u4e00-\\u9fff]/) &&\r\n                  change.fixed.includes(' ')) {\r\n                changeTypes.add('中英文排版');\r\n              }\r\n              if (change.original.match(/\\s{2,}/) && !change.fixed.match(/\\s{2,}/)) {\r\n                changeTypes.add('空格规范');\r\n              }\r\n              if (change.original.match(/\\s+$/) && !change.fixed.match(/\\s+$/)) {\r\n                changeTypes.add('行尾清理');\r\n              }\r\n            }\r\n          });\r\n\r\n          if (changeTypes.size > 0) {\r\n            setFixingResults(prev => [...prev, `🎯 修复类型：${Array.from(changeTypes).join('、')}`]);\r\n          }\r\n        }\r\n\r\n        setFixingResults(prev => [...prev, '🎉 文档格式已优化完成']);\r\n\r\n        // 保存详细分析数据\r\n        setDetailedAnalysis(analysis);\r\n\r\n        onContentChange(result.result);\r\n\r\n        // 延迟重新分析，让用户看到过程\r\n        setTimeout(() => {\r\n          analyzeDocument();\r\n        }, 1000);\r\n\r\n        // 显示最终结果\r\n        setTimeout(() => {\r\n          if (result.isAIFixed) {\r\n            alert('🤖 AI智能修复完成！\\n\\n✨ 修复内容包括：\\n• 标题格式规范化\\n• 中英文排版优化\\n• 空格和标点规范\\n• 列表和代码块格式\\n• 表格对齐优化\\n\\n请查看修复过程了解详细信息。');\r\n          } else {\r\n            const message = result.error\r\n              ? `AI修复遇到问题：${result.error}\\n\\n已使用本地修复作为备选方案，基础格式问题已修复。`\r\n              : 'AI服务未配置，已使用本地修复完成基础格式优化。';\r\n            alert(message);\r\n          }\r\n        }, 1500);\r\n\r\n      } else if (result.result === originalContent) {\r\n        setFixingResults(prev => [...prev, '✅ 文档格式已经很规范，无需修复']);\r\n        setTimeout(() => {\r\n          alert('✅ 恭喜！您的文档格式已经很规范，未发现需要修复的问题。');\r\n        }, 1000);\r\n      } else {\r\n        setFixingResults(prev => [...prev, '❌ 修复过程中出现错误']);\r\n        alert('修复失败，请重试。');\r\n      }\r\n    } catch (error) {\r\n      console.error('AI自动修复失败:', error);\r\n      setFixingResults(prev => [...prev, `❌ 修复失败：${error instanceof Error ? error.message : '未知错误'}`]);\r\n      alert('AI自动修复失败，请重试。');\r\n    } finally {\r\n      setFixingProgress('');\r\n      setTimeout(() => {\r\n        setIsAnalyzing(false);\r\n      }, 2000); // 让用户有时间看到完整的过程\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // 应用建议\r\n  const applySuggestion = (suggestion: FormatSuggestion) => {\r\n    if (!suggestion.suggestion || suggestion.suggestion.includes('删除') || suggestion.suggestion.includes('考虑')) {\r\n      return;\r\n    }\r\n\r\n    const lines = content.split('\\n');\r\n    lines[suggestion.line - 1] = suggestion.suggestion;\r\n    onContentChange(lines.join('\\n'));\r\n    analyzeDocument(); // 重新分析\r\n  };\r\n\r\n  // 检查AI配置状态\r\n  const checkAIConfiguration = () => {\r\n    setIsAIConfigured(aiService.isInitialized());\r\n  };\r\n\r\n  // 组件打开时自动分析\r\n  useEffect(() => {\r\n    if (isOpen && content) {\r\n      analyzeDocument();\r\n      checkAIConfiguration();\r\n    }\r\n  }, [isOpen, content]);\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <button\r\n        onClick={() => setIsOpen(true)}\r\n        className={`px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium ${className}`}\r\n        title=\"智能排版\"\r\n      >\r\n        智能排版\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col\">\r\n        {/* 头部 */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600\">\r\n          <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">智能排版助手</h2>\r\n          <button\r\n            onClick={() => setIsOpen(false)}\r\n            className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* 标签页 */}\r\n        <div className=\"flex border-b border-gray-200 dark:border-gray-600\">\r\n          {[\r\n            { key: 'toc', label: '目录生成', icon: '📋' },\r\n            { key: 'suggestions', label: '格式建议', icon: '💡' },\r\n            { key: 'analysis', label: '文档分析', icon: '📊' }\r\n          ].map(tab => (\r\n            <button\r\n              key={tab.key}\r\n              onClick={() => setActiveTab(tab.key as any)}\r\n              className={`flex items-center gap-2 px-4 py-2 text-sm font-medium ${\r\n                activeTab === tab.key\r\n                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-400'\r\n                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'\r\n              }`}\r\n            >\r\n              <span>{tab.icon}</span>\r\n              {tab.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* 内容区域 */}\r\n        <div className=\"flex-1 p-4 overflow-auto\">\r\n          {isAnalyzing && (\r\n            <div className=\"flex items-center justify-center h-32\">\r\n              <div className=\"text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\r\n                <p className=\"text-gray-600 dark:text-gray-400\">正在分析文档...</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!isAnalyzing && documentStructure && (\r\n            <>\r\n              {/* 目录生成 */}\r\n              {activeTab === 'toc' && (\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">文档目录</h3>\r\n                    <div className=\"flex gap-2\">\r\n                      <button\r\n                        onClick={() => setShowTocPage(true)}\r\n                        className=\"px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium\"\r\n                        disabled={documentStructure.toc.length === 0}\r\n                        title=\"打开独立目录页面\"\r\n                      >\r\n                        📚 目录页\r\n                      </button>\r\n                      <button\r\n                        onClick={insertTOC}\r\n                        className=\"px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm\"\r\n                        disabled={documentStructure.toc.length === 0}\r\n                        title=\"在文档中插入目录\"\r\n                      >\r\n                        插入目录\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {documentStructure.toc.length === 0 ? (\r\n                    <p className=\"text-gray-500 dark:text-gray-400\">文档中没有找到标题</p>\r\n                  ) : (\r\n                    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                      <h4 className=\"font-medium mb-2 text-gray-900 dark:text-white\">目录预览：</h4>\r\n                      <div className=\"space-y-1\">\r\n                        {documentStructure.toc.map((item, index) => (\r\n                          <div\r\n                            key={index}\r\n                            className=\"flex items-center text-sm\"\r\n                            style={{ paddingLeft: `${(item.level - 1) * 16}px` }}\r\n                          >\r\n                            <span className=\"text-gray-600 dark:text-gray-400 mr-2\">\r\n                              H{item.level}\r\n                            </span>\r\n                            <span className=\"text-gray-900 dark:text-white\">{item.title}</span>\r\n                            <span className=\"text-gray-400 ml-auto\">第{item.line}行</span>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* 格式建议 */}\r\n              {activeTab === 'suggestions' && (\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">格式建议</h3>\r\n                    <div className=\"flex gap-2\">\r\n                      <div className=\"relative\">\r\n                        <button\r\n                          onClick={() => aiAutoFixFormatting(false)}\r\n                          disabled={isAnalyzing}\r\n                          className={`px-3 py-1 text-white rounded text-sm font-medium ${\r\n                            isAIConfigured\r\n                              ? 'bg-purple-500 hover:bg-purple-600 disabled:opacity-50'\r\n                              : 'bg-gray-400 cursor-not-allowed'\r\n                          }`}\r\n                          title={isAIConfigured ? 'AI智能修复' : '请先配置AI服务'}\r\n                        >\r\n                          {isAnalyzing ? '🤖 修复中...' : '🤖 AI修复'}\r\n                        </button>\r\n\r\n                        {/* 自定义提示按钮 */}\r\n                        <button\r\n                          onClick={() => setShowPromptInput(!showPromptInput)}\r\n                          disabled={isAnalyzing}\r\n                          className={`ml-1 px-2 py-1 text-white rounded text-sm ${\r\n                            isAIConfigured\r\n                              ? 'bg-purple-400 hover:bg-purple-500 disabled:opacity-50'\r\n                              : 'bg-gray-400 cursor-not-allowed'\r\n                          }`}\r\n                          title=\"自定义AI修复指导\"\r\n                        >\r\n                          💬\r\n                        </button>\r\n                      </div>\r\n\r\n                      <button\r\n                        onClick={autoFixFormatting}\r\n                        disabled={isAnalyzing}\r\n                        className=\"px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm disabled:opacity-50\"\r\n                      >\r\n                        本地修复\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 自定义提示输入框 */}\r\n                  {showPromptInput && (\r\n                    <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <div className=\"text-purple-500 text-lg mt-1\">💬</div>\r\n                        <div className=\"flex-1\">\r\n                          <h4 className=\"font-medium text-purple-900 dark:text-purple-100 mb-2\">自定义AI修复指导</h4>\r\n\r\n                          {/* 预设提示词选择 */}\r\n                          <div className=\"mb-3\">\r\n                            <label className=\"text-xs text-purple-700 dark:text-purple-300 mb-2 block\">\r\n                              📋 选择预设提示词（可选）：\r\n                            </label>\r\n                            <div className=\"grid grid-cols-2 gap-2\">\r\n                              {[\r\n                                {\r\n                                  type: 'general',\r\n                                  label: '通用文档',\r\n                                  prompt: '按照通用文档标准进行格式优化，确保标题层级清晰、段落结构合理、标点符号规范。'\r\n                                },\r\n                                {\r\n                                  type: 'academic',\r\n                                  label: '学术论文',\r\n                                  prompt: '按照学术写作规范进行格式优化，统一引用格式、规范图表标题、确保专业术语一致性、优化章节结构。'\r\n                                },\r\n                                {\r\n                                  type: 'business',\r\n                                  label: '商务报告',\r\n                                  prompt: '按照商务文档标准进行格式优化，突出重点信息、规范数据表格、统一专业术语、优化可读性。'\r\n                                },\r\n                                {\r\n                                  type: 'blog',\r\n                                  label: '博客文章',\r\n                                  prompt: '按照网络阅读习惯进行格式优化，增强可读性、优化段落长度、规范链接格式、突出关键信息。'\r\n                                }\r\n                              ].map(option => (\r\n                                <button\r\n                                  key={option.type}\r\n                                  onClick={() => setCustomPrompt(option.prompt)}\r\n                                  className=\"p-2 text-left border border-purple-200 dark:border-purple-600 rounded text-xs hover:bg-purple-100 dark:hover:bg-purple-800/30 transition-colors\"\r\n                                  disabled={isAnalyzing}\r\n                                >\r\n                                  <div className=\"font-medium text-purple-800 dark:text-purple-200\">{option.label}</div>\r\n                                  <div className=\"text-purple-600 dark:text-purple-400 mt-1 line-clamp-2\">\r\n                                    {option.prompt.substring(0, 50)}...\r\n                                  </div>\r\n                                </button>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n\r\n                          <textarea\r\n                            value={customPrompt}\r\n                            onChange={(e) => setCustomPrompt(e.target.value)}\r\n                            placeholder=\"请输入您希望AI特别关注的修复要求，例如：&#10;• 保持技术术语的英文原文&#10;• 统一使用中文标点符号&#10;• 调整标题层级结构&#10;• 优化代码块的语言标识&#10;&#10;或点击上方预设提示词快速填入\"\r\n                            className=\"w-full h-24 px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-md text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                            disabled={isAnalyzing}\r\n                          />\r\n                          <div className=\"flex items-center justify-between mt-3\">\r\n                            <div className=\"text-xs text-purple-700 dark:text-purple-300\">\r\n                              💡 提示：AI会根据您的指导进行更精准的格式修复\r\n                            </div>\r\n                            <div className=\"flex gap-2\">\r\n                              <button\r\n                                onClick={() => {\r\n                                  setCustomPrompt('');\r\n                                  setShowPromptInput(false);\r\n                                }}\r\n                                disabled={isAnalyzing}\r\n                                className=\"px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded disabled:opacity-50\"\r\n                              >\r\n                                取消\r\n                              </button>\r\n                              <button\r\n                                onClick={() => {\r\n                                  aiAutoFixFormatting(true);\r\n                                  setShowPromptInput(false);\r\n                                }}\r\n                                disabled={isAnalyzing || !isAIConfigured}\r\n                                className={`px-3 py-1 text-xs text-white rounded disabled:opacity-50 ${\r\n                                  isAIConfigured\r\n                                    ? 'bg-purple-500 hover:bg-purple-600'\r\n                                    : 'bg-gray-400 cursor-not-allowed'\r\n                                }`}\r\n                              >\r\n                                🚀 开始AI修复\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* 主内容区域 - 显示修复过程或格式建议 */}\r\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg min-h-[300px]\">\r\n                    {/* 修复过程显示 */}\r\n                    {isAnalyzing || (showFixingProcess && fixingResults.length > 0) ? (\r\n                      <div className=\"p-4 h-full\">\r\n                        <div className=\"flex items-center gap-2 mb-4\">\r\n                          <div className=\"text-blue-500\">🔧</div>\r\n                          <h4 className=\"font-medium text-gray-900 dark:text-white\">AI智能修复过程</h4>\r\n                        </div>\r\n\r\n                        {/* 当前进度 */}\r\n                        {fixingProgress && (\r\n                          <div className=\"flex items-center gap-3 mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\r\n                            <div className=\"animate-spin text-lg\">⚙️</div>\r\n                            <span className=\"text-blue-700 dark:text-blue-300 font-medium\">{fixingProgress}</span>\r\n                          </div>\r\n                        )}\r\n\r\n                        {/* 修复日志 */}\r\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\r\n                          {fixingResults.map((result, index) => (\r\n                            <div key={index} className=\"flex items-start gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded\">\r\n                              <span className=\"text-xs text-gray-400 mt-1 font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">\r\n                                {String(index + 1).padStart(2, '0')}\r\n                              </span>\r\n                              <span className=\"text-gray-700 dark:text-gray-300 flex-1\">{result}</span>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n\r\n                        {/* 操作按钮 */}\r\n                        {!isAnalyzing && fixingResults.length > 0 && (\r\n                          <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600 flex gap-2\">\r\n                            {detailedAnalysis && (\r\n                              <button\r\n                                onClick={() => setShowDetailedReport(true)}\r\n                                className=\"px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium\"\r\n                              >\r\n                                📊 查看详细报告\r\n                              </button>\r\n                            )}\r\n                            <button\r\n                              onClick={() => {\r\n                                setShowFixingProcess(false);\r\n                                setFixingResults([]);\r\n                                setFixingProgress('');\r\n                              }}\r\n                              className=\"px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm\"\r\n                            >\r\n                              关闭日志\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      /* 格式建议列表 */\r\n                      <div className=\"p-4\">\r\n                        {!documentStructure ? (\r\n                          <div className=\"flex items-center justify-center h-48 text-gray-500 dark:text-gray-400\">\r\n                            <div className=\"text-center\">\r\n                              <div className=\"text-4xl mb-2\">📝</div>\r\n                              <p>点击上方按钮开始分析文档格式</p>\r\n                            </div>\r\n                          </div>\r\n                        ) : documentStructure.suggestions.length === 0 ? (\r\n                          <div className=\"flex items-center justify-center h-48 text-green-600 dark:text-green-400\">\r\n                            <div className=\"text-center\">\r\n                              <div className=\"text-4xl mb-2\">✅</div>\r\n                              <p className=\"font-medium\">文档格式良好，没有发现问题</p>\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"space-y-3\">\r\n                            <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\r\n                              发现 {documentStructure.suggestions.length} 个格式问题\r\n                            </h4>\r\n                            {documentStructure.suggestions.map((suggestion, index) => (\r\n                              <div\r\n                                key={index}\r\n                                className={`p-3 rounded-lg border-l-4 ${\r\n                                  suggestion.severity === 'error'\r\n                                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'\r\n                                    : suggestion.severity === 'warning'\r\n                                    ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'\r\n                                    : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\r\n                                }`}\r\n                              >\r\n                                <div className=\"flex items-start justify-between\">\r\n                                  <div className=\"flex-1\">\r\n                                    <div className=\"flex items-center gap-2 mb-1\">\r\n                                      <span className={`px-2 py-1 rounded text-xs font-medium ${\r\n                                        suggestion.severity === 'error'\r\n                                          ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'\r\n                                          : suggestion.severity === 'warning'\r\n                                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'\r\n                                          : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'\r\n                                      }`}>\r\n                                        第{suggestion.line}行\r\n                                      </span>\r\n                                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                                        {suggestion.type}\r\n                                      </span>\r\n                                    </div>\r\n                                    <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-2\">\r\n                                      {suggestion.message}\r\n                                    </p>\r\n                                    {suggestion.suggestion &&\r\n                                     !suggestion.suggestion.includes('删除') &&\r\n                                     !suggestion.suggestion.includes('考虑') && (\r\n                                      <div className=\"bg-white dark:bg-gray-800 p-2 rounded border text-sm font-mono\">\r\n                                        {suggestion.suggestion}\r\n                                      </div>\r\n                                    )}\r\n                                  </div>\r\n                                  {suggestion.suggestion &&\r\n                                   !suggestion.suggestion.includes('删除') &&\r\n                                   !suggestion.suggestion.includes('考虑') && (\r\n                                    <button\r\n                                      onClick={() => applySuggestion(suggestion)}\r\n                                      className=\"ml-3 px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs\"\r\n                                    >\r\n                                      应用\r\n                                    </button>\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* 修复方式说明 - 只在不显示修复过程时显示 */}\r\n                  {!isAnalyzing && !showFixingProcess && (\r\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3\">\r\n                      <div className=\"flex items-start gap-2\">\r\n                        <div className=\"text-blue-500 mt-0.5\">💡</div>\r\n                        <div className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                          <div className=\"font-medium mb-1\">修复方式对比：</div>\r\n                          <div className=\"space-y-1\">\r\n                            <div><strong>🤖 AI修复</strong>：使用人工智能进行全面的格式优化，包括复杂的排版规则和上下文理解</div>\r\n                            <div><strong>🔧 本地修复</strong>：使用预定义规则进行基础格式修复，速度快但功能有限</div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {documentStructure.suggestions.length === 0 ? (\r\n                    <p className=\"text-green-600 dark:text-green-400\">✅ 文档格式良好，没有发现问题</p>\r\n                  ) : (\r\n                    <div className=\"space-y-2\">\r\n                      {documentStructure.suggestions.map((suggestion, index) => (\r\n                        <div\r\n                          key={index}\r\n                          className={`p-3 rounded-lg border-l-4 ${\r\n                            suggestion.severity === 'error'\r\n                              ? 'bg-red-50 border-red-400 dark:bg-red-900/20'\r\n                              : suggestion.severity === 'warning'\r\n                              ? 'bg-yellow-50 border-yellow-400 dark:bg-yellow-900/20'\r\n                              : 'bg-blue-50 border-blue-400 dark:bg-blue-900/20'\r\n                          }`}\r\n                        >\r\n                          <div className=\"flex items-start justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n                                第{suggestion.line}行：{suggestion.message}\r\n                              </p>\r\n                              {suggestion.suggestion && !suggestion.suggestion.includes('删除') && !suggestion.suggestion.includes('考虑') && (\r\n                                <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">\r\n                                  建议：{suggestion.suggestion}\r\n                                </p>\r\n                              )}\r\n                            </div>\r\n                            {suggestion.suggestion && !suggestion.suggestion.includes('删除') && !suggestion.suggestion.includes('考虑') && (\r\n                              <button\r\n                                onClick={() => applySuggestion(suggestion)}\r\n                                className=\"ml-2 px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs\"\r\n                              >\r\n                                应用\r\n                              </button>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* 文档分析 */}\r\n              {activeTab === 'analysis' && (\r\n                <div className=\"space-y-4\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">文档分析</h3>\r\n                  \r\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg\">\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">总字数</p>\r\n                      <p className=\"text-xl font-bold text-blue-600 dark:text-blue-400\">\r\n                        {documentStructure.wordCount}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-green-50 dark:bg-green-900/20 p-3 rounded-lg\">\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">阅读时间</p>\r\n                      <p className=\"text-xl font-bold text-green-600 dark:text-green-400\">\r\n                        {documentStructure.readingTime}分钟\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg\">\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">标题数量</p>\r\n                      <p className=\"text-xl font-bold text-purple-600 dark:text-purple-400\">\r\n                        {Object.values(documentStructure.headingCount).reduce((a, b) => a + b, 0)}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg\">\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">格式问题</p>\r\n                      <p className=\"text-xl font-bold text-orange-600 dark:text-orange-400\">\r\n                        {documentStructure.suggestions.length}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                    <h4 className=\"font-medium mb-2 text-gray-900 dark:text-white\">标题分布：</h4>\r\n                    <div className=\"space-y-1\">\r\n                      {Object.entries(documentStructure.headingCount).map(([level, count]) => (\r\n                        <div key={level} className=\"flex items-center justify-between text-sm\">\r\n                          <span className=\"text-gray-600 dark:text-gray-400\">H{level} 标题</span>\r\n                          <span className=\"text-gray-900 dark:text-white\">{count} 个</span>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        {/* 底部操作 */}\r\n        <div className=\"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-600\">\r\n          <button\r\n            onClick={analyzeDocument}\r\n            disabled={isAnalyzing}\r\n            className=\"px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm disabled:opacity-50\"\r\n          >\r\n            {isAnalyzing ? '分析中...' : '重新分析'}\r\n          </button>\r\n          \r\n          <button\r\n            onClick={() => setIsOpen(false)}\r\n            className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm\"\r\n          >\r\n            完成\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 详细修复报告模态框 */}\r\n      {showDetailedReport && detailedAnalysis && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60\">\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl max-h-5/6 flex flex-col\">\r\n            {/* 报告头部 */}\r\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 dark:text-white\">📊 详细修复报告</h3>\r\n              <button\r\n                onClick={() => setShowDetailedReport(false)}\r\n                className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n              >\r\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n\r\n            {/* 报告内容 */}\r\n            <div className=\"p-4 overflow-y-auto flex-1\">\r\n              {/* 统计概览 */}\r\n              <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 mb-4\">\r\n                <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">📈 修复统计</h4>\r\n                <div className=\"grid grid-cols-3 gap-4 text-center\">\r\n                  <div>\r\n                    <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\r\n                      {detailedAnalysis.changedLines}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">修改行数</div>\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\r\n                      {detailedAnalysis.addedLines}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">新增行数</div>\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"text-2xl font-bold text-red-600 dark:text-red-400\">\r\n                      {detailedAnalysis.removedLines}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">删除行数</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 详细变更列表 */}\r\n              {detailedAnalysis.changes && detailedAnalysis.changes.length > 0 && (\r\n                <div>\r\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">🔍 详细变更</h4>\r\n                  <div className=\"space-y-2 max-h-64 overflow-y-auto\">\r\n                    {detailedAnalysis.changes.slice(0, 20).map((change: any, index: number) => (\r\n                      <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded p-3\">\r\n                        <div className=\"flex items-center gap-2 mb-2\">\r\n                          <span className={`px-2 py-1 rounded text-xs font-medium ${\r\n                            change.type === 'modified'\r\n                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'\r\n                              : change.type === 'added'\r\n                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'\r\n                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'\r\n                          }`}>\r\n                            {change.type === 'modified' ? '修改' : change.type === 'added' ? '新增' : '删除'}\r\n                          </span>\r\n                          <span className=\"text-sm text-gray-500 dark:text-gray-400\">第 {change.line} 行</span>\r\n                        </div>\r\n\r\n                        {change.original && (\r\n                          <div className=\"mb-1\">\r\n                            <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">原文：</div>\r\n                            <div className=\"bg-red-50 dark:bg-red-900/20 border-l-2 border-red-300 dark:border-red-700 pl-2 py-1 text-sm font-mono\">\r\n                              {change.original}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        {change.fixed && (\r\n                          <div>\r\n                            <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">修复后：</div>\r\n                            <div className=\"bg-green-50 dark:bg-green-900/20 border-l-2 border-green-300 dark:border-green-700 pl-2 py-1 text-sm font-mono\">\r\n                              {change.fixed}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n\r\n                    {detailedAnalysis.changes.length > 20 && (\r\n                      <div className=\"text-center text-sm text-gray-500 dark:text-gray-400 py-2\">\r\n                        还有 {detailedAnalysis.changes.length - 20} 个变更未显示...\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 报告底部 */}\r\n            <div className=\"p-4 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                  修复完成时间：{new Date().toLocaleString()}\r\n                </div>\r\n                <button\r\n                  onClick={() => setShowDetailedReport(false)}\r\n                  className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm\"\r\n                >\r\n                  关闭报告\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 独立目录页面模态框 */}\r\n      {showTocPage && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60\">\r\n          <div className=\"bg-white dark:bg-gray-900 rounded-lg shadow-2xl w-full max-w-4xl h-5/6 flex flex-col\">\r\n            <TableOfContentsPage\r\n              content={content}\r\n              onNavigate={handleTocNavigation}\r\n              onClose={() => setShowTocPage(false)}\r\n              className=\"flex-1\"\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,MAAM,wBAA8D,CAAC,EAC1E,OAAO,EACP,eAAe,EACf,YAAY,EAAE,EACf;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD;IAC5F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACrF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,SAAS;IACT,MAAM,kBAAkB;QACtB,eAAe;QACf,IAAI;YACF,MAAM,YAAY,qIAAA,CAAA,wBAAqB,CAAC,wBAAwB,CAAC;YACjE,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,MAAM,aAAa,qIAAA,CAAA,wBAAqB,CAAC,mBAAmB,CAAC;QAC7D,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,sBAAsB,CAAC,MAAc;QACzC,SAAS;QACT,eAAe;QAEf,0BAA0B;QAC1B,IAAI,iBAAiB;YACnB,kBAAkB;YAClB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE,QAAQ;YAE1C,iBAAiB;YACjB,MAAM,QAAQ,IAAI,YAAY,eAAe;gBAC3C,QAAQ;oBAAE;oBAAM;gBAAO;YACzB;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,oBAAoB;QACxB,MAAM,kBAAkB;QACxB,MAAM,eAAe,qIAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC;QAE7D,IAAI,iBAAiB,iBAAiB;YACpC,gBAAgB;YAChB,mBAAmB,OAAO;YAE1B,SAAS;YACT,WAAW;gBACT,MAAM;YACR,GAAG;QACL,OAAO;YACL,MAAM;QACR;IACF;IAEA,WAAW;IACX,MAAM,sBAAsB,OAAO,kBAAkB,KAAK;QACxD,eAAe;QACf,qBAAqB;QACrB,iBAAiB,EAAE;QAEnB,IAAI;YACF,MAAM,kBAAkB;YAExB,WAAW;YACX,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,aAAa;YACb,MAAM,mBAAmB,qIAAA,CAAA,wBAAqB,CAAC,yBAAyB,CAAC;YACzE,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM,CAAC,OAAO,EAAE,iBAAiB,MAAM,CAAC,QAAQ,CAAC;iBAAC;YAE/E,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,MAAM,eAAe;uBAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;iBAAG;gBACpE,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM,CAAC,QAAQ,EAAE,aAAa,IAAI,CAAC,MAAM;qBAAC;YACzE;YAEA,YAAY;YACZ,IAAI,mBAAmB,aAAa,IAAI,IAAI;gBAC1C,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM,CAAC,QAAQ,EAAE,aAAa,IAAI,IAAI;qBAAC;YACtE;YAEA,WAAW;YACX,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM;iBAAyB;YAE5D,WAAW;YACX,kBAAkB;YAClB,MAAM,mBAAmB,mBAAmB,aAAa,IAAI,KAAK,aAAa,IAAI,KAAK;YACxF,MAAM,SAAS,MAAM,qIAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,SAAS;YAE5E,aAAa;YACb,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,KAAK,iBAAiB;gBACvD,YAAY;gBACZ,MAAM,WAAW,OAAO,QAAQ;gBAEhC,iBAAiB,CAAA,OAAQ;2BAAI;wBAC3B,OAAO,SAAS,GACZ,kBACA;qBACL;gBAED,IAAI,UAAU;oBACZ,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG;oBAC5D,MAAM,eAAe,eAAe,aAAa;oBAEjD,iBAAiB,CAAA,OAAQ;+BAAI;4BAC3B,CAAC,YAAY,EAAE,aAAa,IAAI,CAAC;+BAC7B,eAAe,IAAI;gCAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;6BAAC,GAAG,EAAE;+BACrD,aAAa,IAAI;gCAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;6BAAC,GAAG,EAAE;+BACjD,eAAe,IAAI;gCAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;6BAAC,GAAG,EAAE;yBAC1D;oBAED,YAAY;oBACZ,MAAM,cAAc,IAAI;oBACxB,QAAQ,OAAO,CAAC,CAAA;wBACd,IAAI,OAAO,IAAI,KAAK,cAAc,OAAO,QAAQ,IAAI,OAAO,KAAK,EAAE;4BACjE,SAAS;4BACT,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,mBAAmB,OAAO,KAAK,CAAC,KAAK,CAAC,cAAc;gCAC5E,YAAY,GAAG,CAAC;4BAClB;4BACA,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,sDACtB,OAAO,KAAK,CAAC,QAAQ,CAAC,MAAM;gCAC9B,YAAY,GAAG,CAAC;4BAClB;4BACA,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW;gCACpE,YAAY,GAAG,CAAC;4BAClB;4BACA,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS;gCAChE,YAAY,GAAG,CAAC;4BAClB;wBACF;oBACF;oBAEA,IAAI,YAAY,IAAI,GAAG,GAAG;wBACxB,iBAAiB,CAAA,OAAQ;mCAAI;gCAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM;6BAAC;oBACpF;gBACF;gBAEA,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAe;gBAElD,WAAW;gBACX,oBAAoB;gBAEpB,gBAAgB,OAAO,MAAM;gBAE7B,iBAAiB;gBACjB,WAAW;oBACT;gBACF,GAAG;gBAEH,SAAS;gBACT,WAAW;oBACT,IAAI,OAAO,SAAS,EAAE;wBACpB,MAAM;oBACR,OAAO;wBACL,MAAM,UAAU,OAAO,KAAK,GACxB,CAAC,SAAS,EAAE,OAAO,KAAK,CAAC,4BAA4B,CAAC,GACtD;wBACJ,MAAM;oBACR;gBACF,GAAG;YAEL,OAAO,IAAI,OAAO,MAAM,KAAK,iBAAiB;gBAC5C,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAmB;gBACtD,WAAW;oBACT,MAAM;gBACR,GAAG;YACL,OAAO;gBACL,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAc;gBACjD,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM,CAAC,OAAO,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;iBAAC;YAC/F,MAAM;QACR,SAAU;YACR,kBAAkB;YAClB,WAAW;gBACT,eAAe;YACjB,GAAG,OAAO,gBAAgB;QAC5B;IACF;IAIA,OAAO;IACP,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,WAAW,UAAU,IAAI,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS,WAAW,UAAU,CAAC,QAAQ,CAAC,OAAO;YAC1G;QACF;QAEA,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,KAAK,CAAC,WAAW,IAAI,GAAG,EAAE,GAAG,WAAW,UAAU;QAClD,gBAAgB,MAAM,IAAI,CAAC;QAC3B,mBAAmB,OAAO;IAC5B;IAEA,WAAW;IACX,MAAM,uBAAuB;QAC3B,kBAAkB,kHAAA,CAAA,YAAS,CAAC,aAAa;IAC3C;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,SAAS;YACrB;YACA;QACF;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAW,CAAC,mFAAmF,EAAE,WAAW;YAC5G,OAAM;sBACP;;;;;;IAIL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,KAAK;gCAAO,OAAO;gCAAQ,MAAM;4BAAK;4BACxC;gCAAE,KAAK;gCAAe,OAAO;gCAAQ,MAAM;4BAAK;4BAChD;gCAAE,KAAK;gCAAY,OAAO;gCAAQ,MAAM;4BAAK;yBAC9C,CAAC,GAAG,CAAC,CAAA,oBACJ,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,GAAG;gCACnC,WAAW,CAAC,sDAAsD,EAChE,cAAc,IAAI,GAAG,GACjB,gEACA,iFACJ;;kDAEF,8OAAC;kDAAM,IAAI,IAAI;;;;;;oCACd,IAAI,KAAK;;+BATL,IAAI,GAAG;;;;;;;;;;kCAelB,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;4BAKrD,CAAC,eAAe,mCACf;;oCAEG,cAAc,uBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,eAAe;gEAC9B,WAAU;gEACV,UAAU,kBAAkB,GAAG,CAAC,MAAM,KAAK;gEAC3C,OAAM;0EACP;;;;;;0EAGD,8OAAC;gEACC,SAAS;gEACT,WAAU;gEACV,UAAU,kBAAkB,GAAG,CAAC,MAAM,KAAK;gEAC3C,OAAM;0EACP;;;;;;;;;;;;;;;;;;4CAMJ,kBAAkB,GAAG,CAAC,MAAM,KAAK,kBAChC,8OAAC;gDAAE,WAAU;0DAAmC;;;;;yGAEhD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEACZ,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;gEAEC,WAAU;gEACV,OAAO;oEAAE,aAAa,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;gEAAC;;kFAEnD,8OAAC;wEAAK,WAAU;;4EAAwC;4EACpD,KAAK,KAAK;;;;;;;kFAEd,8OAAC;wEAAK,WAAU;kFAAiC,KAAK,KAAK;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;;4EAAwB;4EAAE,KAAK,IAAI;4EAAC;;;;;;;;+DAR/C;;;;;;;;;;;;;;;;;;;;;;oCAkBlB,cAAc,+BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS,IAAM,oBAAoB;wEACnC,UAAU;wEACV,WAAW,CAAC,iDAAiD,EAC3D,iBACI,0DACA,kCACJ;wEACF,OAAO,iBAAiB,WAAW;kFAElC,cAAc,cAAc;;;;;;kFAI/B,8OAAC;wEACC,SAAS,IAAM,mBAAmB,CAAC;wEACnC,UAAU;wEACV,WAAW,CAAC,0CAA0C,EACpD,iBACI,0DACA,kCACJ;wEACF,OAAM;kFACP;;;;;;;;;;;;0EAKH,8OAAC;gEACC,SAAS;gEACT,UAAU;gEACV,WAAU;0EACX;;;;;;;;;;;;;;;;;;4CAOJ,iCACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+B;;;;;;sEAC9C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAwD;;;;;;8EAGtE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA0D;;;;;;sFAG3E,8OAAC;4EAAI,WAAU;sFACZ;gFACC;oFACE,MAAM;oFACN,OAAO;oFACP,QAAQ;gFACV;gFACA;oFACE,MAAM;oFACN,OAAO;oFACP,QAAQ;gFACV;gFACA;oFACE,MAAM;oFACN,OAAO;oFACP,QAAQ;gFACV;gFACA;oFACE,MAAM;oFACN,OAAO;oFACP,QAAQ;gFACV;6EACD,CAAC,GAAG,CAAC,CAAA,uBACJ,8OAAC;oFAEC,SAAS,IAAM,gBAAgB,OAAO,MAAM;oFAC5C,WAAU;oFACV,UAAU;;sGAEV,8OAAC;4FAAI,WAAU;sGAAoD,OAAO,KAAK;;;;;;sGAC/E,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG;gGAAI;;;;;;;;mFAP7B,OAAO,IAAI;;;;;;;;;;;;;;;;8EAcxB,8OAAC;oEACC,OAAO;oEACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAC/C,aAAY;oEACZ,WAAU;oEACV,UAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA+C;;;;;;sFAG9D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,SAAS;wFACP,gBAAgB;wFAChB,mBAAmB;oFACrB;oFACA,UAAU;oFACV,WAAU;8FACX;;;;;;8FAGD,8OAAC;oFACC,SAAS;wFACP,oBAAoB;wFACpB,mBAAmB;oFACrB;oFACA,UAAU,eAAe,CAAC;oFAC1B,WAAW,CAAC,yDAAyD,EACnE,iBACI,sCACA,kCACJ;8FACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAWb,8OAAC;gDAAI,WAAU;0DAEZ,eAAgB,qBAAqB,cAAc,MAAM,GAAG,kBAC3D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,8OAAC;oEAAG,WAAU;8EAA4C;;;;;;;;;;;;wDAI3D,gCACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAuB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAAgD;;;;;;;;;;;;sEAKpE,8OAAC;4DAAI,WAAU;sEACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAK,WAAU;sFACb,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;sFAEjC,8OAAC;4EAAK,WAAU;sFAA2C;;;;;;;mEAJnD;;;;;;;;;;wDAUb,CAAC,eAAe,cAAc,MAAM,GAAG,mBACtC,8OAAC;4DAAI,WAAU;;gEACZ,kCACC,8OAAC;oEACC,SAAS,IAAM,sBAAsB;oEACrC,WAAU;8EACX;;;;;;8EAIH,8OAAC;oEACC,SAAS;wEACP,qBAAqB;wEACrB,iBAAiB,EAAE;wEACnB,kBAAkB;oEACpB;oEACA,WAAU;8EACX;;;;;;;;;;;;;;;;;+FAOP,UAAU,iBACV,8OAAC;oDAAI,WAAU;8DACZ,CAAC,kCACA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,8OAAC;8EAAE;;;;;;;;;;;;;;;;mGAGL,kBAAkB,WAAW,CAAC,MAAM,KAAK,kBAC3C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,8OAAC;oEAAE,WAAU;8EAAc;;;;;;;;;;;;;;;;iHAI/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;;oEAAiD;oEACzD,kBAAkB,WAAW,CAAC,MAAM;oEAAC;;;;;;;4DAE1C,kBAAkB,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBAC9C,8OAAC;oEAEC,WAAW,CAAC,0BAA0B,EACpC,WAAW,QAAQ,KAAK,UACpB,gDACA,WAAW,QAAQ,KAAK,YACxB,yDACA,kDACJ;8EAEF,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAK,WAAW,CAAC,sCAAsC,EACtD,WAAW,QAAQ,KAAK,UACpB,8DACA,WAAW,QAAQ,KAAK,YACxB,0EACA,iEACJ;;oGAAE;oGACA,WAAW,IAAI;oGAAC;;;;;;;0GAEpB,8OAAC;gGAAK,WAAU;0GACb,WAAW,IAAI;;;;;;;;;;;;kGAGpB,8OAAC;wFAAE,WAAU;kGACV,WAAW,OAAO;;;;;;oFAEpB,WAAW,UAAU,IACrB,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,SAChC,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,uBAC/B,8OAAC;wFAAI,WAAU;kGACZ,WAAW,UAAU;;;;;;;;;;;;4EAI3B,WAAW,UAAU,IACrB,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,SAChC,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,uBAC/B,8OAAC;gFACC,SAAS,IAAM,gBAAgB;gFAC/B,WAAU;0FACX;;;;;;;;;;;;mEA1CA;;;;;;;;;;;;;;;;;;;;;4CAwDlB,CAAC,eAAe,CAAC,mCAChB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAuB;;;;;;sEACtC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAmB;;;;;;8EAClC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FAAI,8OAAC;8FAAO;;;;;;gFAAgB;;;;;;;sFAC7B,8OAAC;;8FAAI,8OAAC;8FAAO;;;;;;gFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAOtC,kBAAkB,WAAW,CAAC,MAAM,KAAK,kBACxC,8OAAC;gDAAE,WAAU;0DAAqC;;;;;yGAElD,8OAAC;gDAAI,WAAU;0DACZ,kBAAkB,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBAC9C,8OAAC;wDAEC,WAAW,CAAC,0BAA0B,EACpC,WAAW,QAAQ,KAAK,UACpB,gDACA,WAAW,QAAQ,KAAK,YACxB,yDACA,kDACJ;kEAEF,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;gFAAoD;gFAC7D,WAAW,IAAI;gFAAC;gFAAG,WAAW,OAAO;;;;;;;wEAExC,WAAW,UAAU,IAAI,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,uBACjG,8OAAC;4EAAE,WAAU;;gFAAgD;gFACvD,WAAW,UAAU;;;;;;;;;;;;;gEAI9B,WAAW,UAAU,IAAI,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,uBACjG,8OAAC;oEACC,SAAS,IAAM,gBAAgB;oEAC/B,WAAU;8EACX;;;;;;;;;;;;uDAxBA;;;;;;;;;;;;;;;;oCAqChB,cAAc,4BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAEpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;0EACxD,8OAAC;gEAAE,WAAU;0EACV,kBAAkB,SAAS;;;;;;;;;;;;kEAGhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;0EACxD,8OAAC;gEAAE,WAAU;;oEACV,kBAAkB,WAAW;oEAAC;;;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;0EACxD,8OAAC;gEAAE,WAAU;0EACV,OAAO,MAAM,CAAC,kBAAkB,YAAY,EAAE,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;;;;;;;;;;;;kEAG3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;0EACxD,8OAAC;gEAAE,WAAU;0EACV,kBAAkB,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,kBAAkB,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACjE,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAK,WAAU;;4EAAmC;4EAAE;4EAAM;;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;;4EAAiC;4EAAM;;;;;;;;+DAF/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgB1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,cAAc,WAAW;;;;;;0CAG5B,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOJ,sBAAsB,kCACrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAChE,8OAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDACjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,YAAY;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAE5D,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,UAAU;;;;;;sEAE9B,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAE5D,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,YAAY;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;gCAM/D,iBAAiB,OAAO,IAAI,iBAAiB,OAAO,CAAC,MAAM,GAAG,mBAC7D,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDACjE,8OAAC;4CAAI,WAAU;;gDACZ,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAa,sBACvD,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,sCAAsC,EACtD,OAAO,IAAI,KAAK,aACZ,qEACA,OAAO,IAAI,KAAK,UAChB,yEACA,gEACJ;kFACC,OAAO,IAAI,KAAK,aAAa,OAAO,OAAO,IAAI,KAAK,UAAU,OAAO;;;;;;kFAExE,8OAAC;wEAAK,WAAU;;4EAA2C;4EAAG,OAAO,IAAI;4EAAC;;;;;;;;;;;;;4DAG3E,OAAO,QAAQ,kBACd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAgD;;;;;;kFAC/D,8OAAC;wEAAI,WAAU;kFACZ,OAAO,QAAQ;;;;;;;;;;;;4DAKrB,OAAO,KAAK,kBACX,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAgD;;;;;;kFAC/D,8OAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK;;;;;;;;;;;;;uDA3BX;;;;;gDAkCX,iBAAiB,OAAO,CAAC,MAAM,GAAG,oBACjC,8OAAC;oDAAI,WAAU;;wDAA4D;wDACrE,iBAAiB,OAAO,CAAC,MAAM,GAAG;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;sCASrD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAA2C;4CAChD,IAAI,OAAO,cAAc;;;;;;;kDAEnC,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,+IAAA,CAAA,sBAAmB;wBAClB,SAAS;wBACT,YAAY;wBACZ,SAAS,IAAM,eAAe;wBAC9B,WAAU;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 10652, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/utils/index.ts"], "sourcesContent": ["// 通用工具函数\r\nexport const generateId = (): string => {\r\n  return Math.random().toString(36).substr(2, 9);\r\n};\r\n\r\nexport const formatDate = (date: Date): string => {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n  }).format(date);\r\n};\r\n\r\nexport const debounce = <T extends (...args: any[]) => any>(\r\n  func: T,\r\n  wait: number\r\n): ((...args: Parameters<T>) => void) => {\r\n  let timeout: NodeJS.Timeout;\r\n  return (...args: Parameters<T>) => {\r\n    clearTimeout(timeout);\r\n    timeout = setTimeout(() => func(...args), wait);\r\n  };\r\n};\r\n\r\nexport const throttle = <T extends (...args: any[]) => any>(\r\n  func: T,\r\n  limit: number\r\n): ((...args: Parameters<T>) => void) => {\r\n  let inThrottle: boolean;\r\n  return (...args: Parameters<T>) => {\r\n    if (!inThrottle) {\r\n      func(...args);\r\n      inThrottle = true;\r\n      setTimeout(() => (inThrottle = false), limit);\r\n    }\r\n  };\r\n};\r\n\r\n// 文件相关工具函数\r\nexport const downloadFile = (content: string, filename: string, type: string) => {\r\n  const blob = new Blob([content], { type });\r\n  const url = URL.createObjectURL(blob);\r\n  const link = document.createElement('a');\r\n  link.href = url;\r\n  link.download = filename;\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  URL.revokeObjectURL(url);\r\n};\r\n\r\nexport const readFileAsText = (file: File): Promise<string> => {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader();\r\n    reader.onload = () => resolve(reader.result as string);\r\n    reader.onerror = () => reject(reader.error);\r\n    reader.readAsText(file);\r\n  });\r\n};\r\n\r\n// 本地存储工具函数\r\nexport const storage = {\r\n  get: <T>(key: string, defaultValue: T): T => {\r\n    if (typeof window === 'undefined') return defaultValue;\r\n    try {\r\n      const item = localStorage.getItem(key);\r\n      return item ? JSON.parse(item) : defaultValue;\r\n    } catch {\r\n      return defaultValue;\r\n    }\r\n  },\r\n  set: <T>(key: string, value: T): void => {\r\n    if (typeof window === 'undefined') return;\r\n    try {\r\n      localStorage.setItem(key, JSON.stringify(value));\r\n    } catch (error) {\r\n      console.error('Failed to save to localStorage:', error);\r\n    }\r\n  },\r\n  remove: (key: string): void => {\r\n    if (typeof window === 'undefined') return;\r\n    localStorage.removeItem(key);\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;;;;;AACF,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAGO,MAAM,eAAe,CAAC,SAAiB,UAAkB;IAC9D,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE;IAAK;IACxC,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;QAC3C,OAAO,OAAO,GAAG,IAAM,OAAO,OAAO,KAAK;QAC1C,OAAO,UAAU,CAAC;IACpB;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO;;;IAO5C;IACA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;;IAMrC;IACA,QAAQ,CAAC;QACP,wCAAmC;;;IAErC;AACF", "debugId": null}}, {"offset": {"line": 10735, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/hooks/useAutoSave.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useState } from 'react';\r\nimport { debounce } from '@/utils';\r\n\r\ninterface AutoSaveOptions {\r\n  key: string;\r\n  delay?: number;\r\n  enabled?: boolean;\r\n}\r\n\r\nexport const useAutoSave = <T>(\r\n  data: T,\r\n  options: AutoSaveOptions\r\n) => {\r\n  const { key, delay = 2000, enabled = true } = options;\r\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\r\n  const initialLoadRef = useRef(true);\r\n\r\n  // 保存到本地存储\r\n  const saveToStorage = (value: T) => {\r\n    if (!enabled) return;\r\n    \r\n    try {\r\n      setIsSaving(true);\r\n      localStorage.setItem(key, JSON.stringify({\r\n        data: value,\r\n        timestamp: new Date().toISOString(),\r\n      }));\r\n      setLastSaved(new Date());\r\n      setHasUnsavedChanges(false);\r\n    } catch (error) {\r\n      console.error('Auto-save failed:', error);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // 从本地存储加载\r\n  const loadFromStorage = (): T | null => {\r\n    if (!enabled) return null;\r\n    \r\n    try {\r\n      const stored = localStorage.getItem(key);\r\n      if (stored) {\r\n        const parsed = JSON.parse(stored);\r\n        setLastSaved(new Date(parsed.timestamp));\r\n        return parsed.data;\r\n      }\r\n    } catch (error) {\r\n      console.error('Auto-load failed:', error);\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // 防抖保存函数\r\n  const debouncedSave = useRef(\r\n    debounce((value: T) => {\r\n      saveToStorage(value);\r\n    }, delay)\r\n  ).current;\r\n\r\n  // 监听数据变化\r\n  useEffect(() => {\r\n    if (initialLoadRef.current) {\r\n      initialLoadRef.current = false;\r\n      return;\r\n    }\r\n\r\n    if (enabled) {\r\n      setHasUnsavedChanges(true);\r\n      debouncedSave(data);\r\n    }\r\n  }, [data, enabled, debouncedSave]);\r\n\r\n  // 手动保存\r\n  const save = () => {\r\n    saveToStorage(data);\r\n  };\r\n\r\n  // 清除保存的数据\r\n  const clear = () => {\r\n    try {\r\n      localStorage.removeItem(key);\r\n      setLastSaved(null);\r\n      setHasUnsavedChanges(false);\r\n    } catch (error) {\r\n      console.error('Clear auto-save failed:', error);\r\n    }\r\n  };\r\n\r\n  return {\r\n    lastSaved,\r\n    isSaving,\r\n    hasUnsavedChanges,\r\n    save,\r\n    clear,\r\n    loadFromStorage,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAWO,MAAM,cAAc,CACzB,MACA;IAEA,MAAM,EAAE,GAAG,EAAE,QAAQ,IAAI,EAAE,UAAU,IAAI,EAAE,GAAG;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,UAAU;IACV,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,YAAY;YACZ,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBACvC,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,aAAa,IAAI;YACjB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,YAAY;QACd;IACF;IAEA,UAAU;IACV,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,aAAa,IAAI,KAAK,OAAO,SAAS;gBACtC,OAAO,OAAO,IAAI;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC;QACA,OAAO;IACT;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EACzB,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,cAAc;IAChB,GAAG,QACH,OAAO;IAET,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,GAAG;YACzB;QACF;QAEA,IAAI,SAAS;YACX,qBAAqB;YACrB,cAAc;QAChB;IACF,GAAG;QAAC;QAAM;QAAS;KAAc;IAEjC,OAAO;IACP,MAAM,OAAO;QACX,cAAc;IAChB;IAEA,UAAU;IACV,MAAM,QAAQ;QACZ,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,aAAa;YACb,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 10827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/hooks/useTheme.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\n\r\nexport type Theme = 'light' | 'dark' | 'system';\r\n\r\nexport const useTheme = () => {\r\n  const [theme, setTheme] = useState<Theme>('system');\r\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');\r\n\r\n  // 获取系统主题偏好\r\n  const getSystemTheme = (): 'light' | 'dark' => {\r\n    if (typeof window === 'undefined') return 'light';\r\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\r\n  };\r\n\r\n  // 应用主题到DOM\r\n  const applyTheme = (newTheme: 'light' | 'dark') => {\r\n    if (typeof window === 'undefined') return;\r\n    \r\n    const root = document.documentElement;\r\n    if (newTheme === 'dark') {\r\n      root.classList.add('dark');\r\n    } else {\r\n      root.classList.remove('dark');\r\n    }\r\n    setResolvedTheme(newTheme);\r\n  };\r\n\r\n  // 解析主题（处理system主题）\r\n  const resolveTheme = (themeValue: Theme): 'light' | 'dark' => {\r\n    if (themeValue === 'system') {\r\n      return getSystemTheme();\r\n    }\r\n    return themeValue;\r\n  };\r\n\r\n  // 设置主题\r\n  const changeTheme = (newTheme: Theme) => {\r\n    setTheme(newTheme);\r\n    const resolved = resolveTheme(newTheme);\r\n    applyTheme(resolved);\r\n    \r\n    // 保存到localStorage\r\n    try {\r\n      localStorage.setItem('martetdown-theme', newTheme);\r\n    } catch (error) {\r\n      console.error('Failed to save theme preference:', error);\r\n    }\r\n  };\r\n\r\n  // 切换主题（在light和dark之间切换）\r\n  const toggleTheme = () => {\r\n    const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\r\n    changeTheme(newTheme);\r\n  };\r\n\r\n  // 初始化主题\r\n  useEffect(() => {\r\n    // 从localStorage读取保存的主题\r\n    let savedTheme: Theme = 'system';\r\n    try {\r\n      const stored = localStorage.getItem('martetdown-theme');\r\n      if (stored && ['light', 'dark', 'system'].includes(stored)) {\r\n        savedTheme = stored as Theme;\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load theme preference:', error);\r\n    }\r\n\r\n    setTheme(savedTheme);\r\n    const resolved = resolveTheme(savedTheme);\r\n    applyTheme(resolved);\r\n\r\n    // 监听系统主题变化\r\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n    const handleSystemThemeChange = () => {\r\n      if (theme === 'system') {\r\n        const newResolved = getSystemTheme();\r\n        applyTheme(newResolved);\r\n      }\r\n    };\r\n\r\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\r\n    return () => {\r\n      mediaQuery.removeEventListener('change', handleSystemThemeChange);\r\n    };\r\n  }, []);\r\n\r\n  // 当主题改变时重新解析\r\n  useEffect(() => {\r\n    const resolved = resolveTheme(theme);\r\n    applyTheme(resolved);\r\n  }, [theme]);\r\n\r\n  return {\r\n    theme,\r\n    resolvedTheme,\r\n    setTheme: changeTheme,\r\n    toggleTheme,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAMO,MAAM,WAAW;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErE,WAAW;IACX,MAAM,iBAAiB;QACrB,wCAAmC,OAAO;;;IAE5C;IAEA,WAAW;IACX,MAAM,aAAa,CAAC;QAClB,wCAAmC;;;QAEnC,MAAM;IAOR;IAEA,mBAAmB;IACnB,MAAM,eAAe,CAAC;QACpB,IAAI,eAAe,UAAU;YAC3B,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO;IACP,MAAM,cAAc,CAAC;QACnB,SAAS;QACT,MAAM,WAAW,aAAa;QAC9B,WAAW;QAEX,kBAAkB;QAClB,IAAI;YACF,aAAa,OAAO,CAAC,oBAAoB;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc;QAClB,MAAM,WAAW,kBAAkB,UAAU,SAAS;QACtD,YAAY;IACd;IAEA,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB;QACvB,IAAI,aAAoB;QACxB,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,UAAU;gBAAC;gBAAS;gBAAQ;aAAS,CAAC,QAAQ,CAAC,SAAS;gBAC1D,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;QAEA,SAAS;QACT,MAAM,WAAW,aAAa;QAC9B,WAAW;QAEX,WAAW;QACX,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,0BAA0B;YAC9B,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc;gBACpB,WAAW;YACb;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO;YACL,WAAW,mBAAmB,CAAC,UAAU;QAC3C;IACF,GAAG,EAAE;IAEL,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,aAAa;QAC9B,WAAW;IACb,GAAG;QAAC;KAAM;IAEV,OAAO;QACL;QACA;QACA,UAAU;QACV;IACF;AACF", "debugId": null}}, {"offset": {"line": 10923, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/hooks/useEditorNavigation.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef } from 'react';\r\n\r\ninterface NavigationOptions {\r\n  textareaRef: React.RefObject<HTMLTextAreaElement>;\r\n  content: string;\r\n}\r\n\r\nexport const useEditorNavigation = ({ textareaRef, content }: NavigationOptions) => {\r\n  const lastNavigationRef = useRef<{ line: number; anchor: string } | null>(null);\r\n\r\n  // 跳转到指定行\r\n  const navigateToLine = (lineNumber: number) => {\r\n    if (!textareaRef.current) return;\r\n\r\n    const textarea = textareaRef.current;\r\n    const lines = content.split('\\n');\r\n    \r\n    // 计算目标行的字符位置\r\n    let position = 0;\r\n    for (let i = 0; i < Math.min(lineNumber - 1, lines.length - 1); i++) {\r\n      position += lines[i].length + 1; // +1 for newline character\r\n    }\r\n\r\n    // 设置光标位置\r\n    textarea.focus();\r\n    textarea.setSelectionRange(position, position);\r\n    \r\n    // 滚动到可见区域\r\n    scrollToPosition(textarea, position);\r\n  };\r\n\r\n  // 跳转到锚点\r\n  const navigateToAnchor = (anchor: string) => {\r\n    if (!textareaRef.current) return;\r\n\r\n    const textarea = textareaRef.current;\r\n    const lines = content.split('\\n');\r\n    \r\n    // 查找匹配的标题行\r\n    let targetLine = -1;\r\n    for (let i = 0; i < lines.length; i++) {\r\n      const line = lines[i];\r\n      const headingMatch = line.match(/^(#{1,6})\\s+(.+)$/);\r\n      if (headingMatch) {\r\n        const title = headingMatch[2].trim();\r\n        const lineAnchor = generateAnchor(title);\r\n        if (lineAnchor === anchor) {\r\n          targetLine = i + 1;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (targetLine > 0) {\r\n      navigateToLine(targetLine);\r\n    }\r\n  };\r\n\r\n  // 生成锚点（与智能格式化服务保持一致）\r\n  const generateAnchor = (title: string): string => {\r\n    return title\r\n      .toLowerCase()\r\n      .replace(/[^\\w\\s\\u4e00-\\u9fff]/g, '') // 保留中文字符\r\n      .replace(/\\s+/g, '-')\r\n      .trim();\r\n  };\r\n\r\n  // 滚动到指定位置\r\n  const scrollToPosition = (textarea: HTMLTextAreaElement, position: number) => {\r\n    // 创建一个临时的测量元素\r\n    const measureElement = document.createElement('div');\r\n    measureElement.style.cssText = `\r\n      position: absolute;\r\n      visibility: hidden;\r\n      white-space: pre-wrap;\r\n      word-wrap: break-word;\r\n      font-family: ${getComputedStyle(textarea).fontFamily};\r\n      font-size: ${getComputedStyle(textarea).fontSize};\r\n      line-height: ${getComputedStyle(textarea).lineHeight};\r\n      padding: ${getComputedStyle(textarea).padding};\r\n      border: ${getComputedStyle(textarea).border};\r\n      width: ${textarea.clientWidth}px;\r\n    `;\r\n    \r\n    document.body.appendChild(measureElement);\r\n    \r\n    // 计算到目标位置的文本高度\r\n    const textBeforePosition = content.substring(0, position);\r\n    measureElement.textContent = textBeforePosition;\r\n    const targetHeight = measureElement.offsetHeight;\r\n    \r\n    document.body.removeChild(measureElement);\r\n    \r\n    // 滚动到目标位置\r\n    const scrollTop = Math.max(0, targetHeight - textarea.clientHeight / 2);\r\n    textarea.scrollTop = scrollTop;\r\n  };\r\n\r\n  // 监听目录导航事件\r\n  useEffect(() => {\r\n    const handleTocNavigate = (event: CustomEvent) => {\r\n      const { line, anchor } = event.detail;\r\n      lastNavigationRef.current = { line, anchor };\r\n      \r\n      if (line) {\r\n        navigateToLine(line);\r\n      } else if (anchor) {\r\n        navigateToAnchor(anchor);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('tocNavigate', handleTocNavigate as EventListener);\r\n    \r\n    return () => {\r\n      window.removeEventListener('tocNavigate', handleTocNavigate as EventListener);\r\n    };\r\n  }, [content]);\r\n\r\n  // 高亮当前行\r\n  const highlightCurrentLine = () => {\r\n    if (!textareaRef.current || !lastNavigationRef.current) return;\r\n\r\n    const textarea = textareaRef.current;\r\n    const { line } = lastNavigationRef.current;\r\n    \r\n    if (line) {\r\n      const lines = content.split('\\n');\r\n      let position = 0;\r\n      \r\n      // 计算目标行的开始和结束位置\r\n      for (let i = 0; i < Math.min(line - 1, lines.length - 1); i++) {\r\n        position += lines[i].length + 1;\r\n      }\r\n      \r\n      const lineLength = lines[line - 1]?.length || 0;\r\n      const endPosition = position + lineLength;\r\n      \r\n      // 选中整行\r\n      textarea.setSelectionRange(position, endPosition);\r\n      \r\n      // 短暂高亮后清除选择\r\n      setTimeout(() => {\r\n        textarea.setSelectionRange(position, position);\r\n      }, 1500);\r\n    }\r\n  };\r\n\r\n  // 获取当前光标所在的标题信息\r\n  const getCurrentHeading = () => {\r\n    if (!textareaRef.current) return null;\r\n\r\n    const textarea = textareaRef.current;\r\n    const cursorPosition = textarea.selectionStart;\r\n    const lines = content.split('\\n');\r\n    \r\n    let currentPosition = 0;\r\n    let currentLine = 0;\r\n    \r\n    // 找到光标所在行\r\n    for (let i = 0; i < lines.length; i++) {\r\n      const lineLength = lines[i].length + 1; // +1 for newline\r\n      if (currentPosition + lineLength > cursorPosition) {\r\n        currentLine = i;\r\n        break;\r\n      }\r\n      currentPosition += lineLength;\r\n    }\r\n    \r\n    // 向上查找最近的标题\r\n    for (let i = currentLine; i >= 0; i--) {\r\n      const line = lines[i];\r\n      const headingMatch = line.match(/^(#{1,6})\\s+(.+)$/);\r\n      if (headingMatch) {\r\n        const level = headingMatch[1].length;\r\n        const title = headingMatch[2].trim();\r\n        const anchor = generateAnchor(title);\r\n        return {\r\n          level,\r\n          title,\r\n          anchor,\r\n          line: i + 1\r\n        };\r\n      }\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  return {\r\n    navigateToLine,\r\n    navigateToAnchor,\r\n    highlightCurrentLine,\r\n    getCurrentHeading,\r\n    lastNavigation: lastNavigationRef.current\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AASO,MAAM,sBAAsB,CAAC,EAAE,WAAW,EAAE,OAAO,EAAqB;IAC7E,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2C;IAE1E,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,aAAa;QACb,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,aAAa,GAAG,MAAM,MAAM,GAAG,IAAI,IAAK;YACnE,YAAY,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG,2BAA2B;QAC9D;QAEA,SAAS;QACT,SAAS,KAAK;QACd,SAAS,iBAAiB,CAAC,UAAU;QAErC,UAAU;QACV,iBAAiB,UAAU;IAC7B;IAEA,QAAQ;IACR,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,WAAW;QACX,IAAI,aAAa,CAAC;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,IAAI,cAAc;gBAChB,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,IAAI;gBAClC,MAAM,aAAa,eAAe;gBAClC,IAAI,eAAe,QAAQ;oBACzB,aAAa,IAAI;oBACjB;gBACF;YACF;QACF;QAEA,IAAI,aAAa,GAAG;YAClB,eAAe;QACjB;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB,CAAC;QACtB,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,yBAAyB,IAAI,SAAS;SAC9C,OAAO,CAAC,QAAQ,KAChB,IAAI;IACT;IAEA,UAAU;IACV,MAAM,mBAAmB,CAAC,UAA+B;QACvD,cAAc;QACd,MAAM,iBAAiB,SAAS,aAAa,CAAC;QAC9C,eAAe,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;mBAKjB,EAAE,iBAAiB,UAAU,UAAU,CAAC;iBAC1C,EAAE,iBAAiB,UAAU,QAAQ,CAAC;mBACpC,EAAE,iBAAiB,UAAU,UAAU,CAAC;eAC5C,EAAE,iBAAiB,UAAU,OAAO,CAAC;cACtC,EAAE,iBAAiB,UAAU,MAAM,CAAC;aACrC,EAAE,SAAS,WAAW,CAAC;IAChC,CAAC;QAED,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,eAAe;QACf,MAAM,qBAAqB,QAAQ,SAAS,CAAC,GAAG;QAChD,eAAe,WAAW,GAAG;QAC7B,MAAM,eAAe,eAAe,YAAY;QAEhD,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,UAAU;QACV,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,eAAe,SAAS,YAAY,GAAG;QACrE,SAAS,SAAS,GAAG;IACvB;IAEA,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM;YACrC,kBAAkB,OAAO,GAAG;gBAAE;gBAAM;YAAO;YAE3C,IAAI,MAAM;gBACR,eAAe;YACjB,OAAO,IAAI,QAAQ;gBACjB,iBAAiB;YACnB;QACF;QAEA,OAAO,gBAAgB,CAAC,eAAe;QAEvC,OAAO;YACL,OAAO,mBAAmB,CAAC,eAAe;QAC5C;IACF,GAAG;QAAC;KAAQ;IAEZ,QAAQ;IACR,MAAM,uBAAuB;QAC3B,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAExD,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,OAAO;QAE1C,IAAI,MAAM;YACR,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,WAAW;YAEf,gBAAgB;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,OAAO,GAAG,MAAM,MAAM,GAAG,IAAI,IAAK;gBAC7D,YAAY,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;YAChC;YAEA,MAAM,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,UAAU;YAC9C,MAAM,cAAc,WAAW;YAE/B,OAAO;YACP,SAAS,iBAAiB,CAAC,UAAU;YAErC,YAAY;YACZ,WAAW;gBACT,SAAS,iBAAiB,CAAC,UAAU;YACvC,GAAG;QACL;IACF;IAEA,gBAAgB;IAChB,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,OAAO,EAAE,OAAO;QAEjC,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,iBAAiB,SAAS,cAAc;QAC9C,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,IAAI,kBAAkB;QACtB,IAAI,cAAc;QAElB,UAAU;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,aAAa,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG,iBAAiB;YACzD,IAAI,kBAAkB,aAAa,gBAAgB;gBACjD,cAAc;gBACd;YACF;YACA,mBAAmB;QACrB;QAEA,YAAY;QACZ,IAAK,IAAI,IAAI,aAAa,KAAK,GAAG,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,IAAI,cAAc;gBAChB,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,MAAM;gBACpC,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,IAAI;gBAClC,MAAM,SAAS,eAAe;gBAC9B,OAAO;oBACL;oBACA;oBACA;oBACA,MAAM,IAAI;gBACZ;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,gBAAgB,kBAAkB,OAAO;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 11091, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/marketdown/marketdown-editor/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { useKeyboardShortcuts, createEditorShortcuts } from '@/hooks/useKeyboardShortcuts';\r\nimport { FindReplaceDialog } from '@/components/ui/FindReplaceDialog';\r\nimport { ExportDialog } from '@/components/ui/ExportDialog';\r\nimport { AIPanel } from '@/components/AIAssistant';\r\nimport { TranslateWidget } from '@/components/ui/TranslateWidget';\r\nimport { CopyButton, QuickCopyButton } from '@/components/ui/CopyButton';\r\nimport { AdvancedRichTextEditor } from '@/components/ui/AdvancedRichTextEditor';\r\nimport { UEditor } from '@/components/ui/UEditor';\r\nimport { IntelligentFormatting } from '@/components/ui/IntelligentFormatting';\r\nimport { useAutoSave } from '@/hooks/useAutoSave';\r\nimport { useTheme } from '@/hooks/useTheme';\r\nimport { useEditorNavigation } from '@/hooks/useEditorNavigation';\r\n\r\n\r\nexport default function Home() {\r\n  const [content, setContent] = useState('# Welcome to Martetdown\\n\\nStart typing your markdown here...\\n\\n## Features\\n\\n- **Real-time preview**\\n- *Markdown syntax support*\\n- `Code highlighting`\\n\\n### Try it out!\\n\\nEdit the text on the left and see the preview on the right.');\r\n  const [showShortcuts, setShowShortcuts] = useState(false);\r\n  const [showFindReplace, setShowFindReplace] = useState(false);\r\n  const [showExportDialog, setShowExportDialog] = useState(false);\r\n  const [showAIPanel, setShowAIPanel] = useState(false);\r\n  const [showLineNumbers, setShowLineNumbers] = useState(true);\r\n  const [fontSize, setFontSize] = useState(14);\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const [isFocusMode, setIsFocusMode] = useState(false);\r\n  const [useRichEditor, setUseRichEditor] = useState(false);\r\n  const [viewMode, setViewMode] = useState<'split' | 'edit' | 'preview'>('split');\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const lineNumbersRef = useRef<HTMLDivElement>(null);\r\n\r\n  // 主题功能\r\n  const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();\r\n\r\n  // 自动保存功能\r\n  const autoSave = useAutoSave(content, {\r\n    key: 'martetdown-content',\r\n    delay: 2000,\r\n    enabled: true,\r\n  });\r\n\r\n  // 编辑器导航功能\r\n  const editorNavigation = useEditorNavigation({\r\n    textareaRef,\r\n    content\r\n  });\r\n\r\n  // 页面加载时恢复内容\r\n  useEffect(() => {\r\n    const savedContent = autoSave.loadFromStorage();\r\n    if (savedContent && savedContent !== content) {\r\n      const shouldRestore = confirm('检测到未保存的内容，是否恢复？');\r\n      if (shouldRestore) {\r\n        setContent(savedContent);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // 同步行号滚动\r\n  const syncLineNumbers = () => {\r\n    if (!textareaRef.current || !lineNumbersRef.current) return;\r\n\r\n    const textarea = textareaRef.current;\r\n    const lineNumbers = lineNumbersRef.current;\r\n\r\n    lineNumbers.scrollTop = textarea.scrollTop;\r\n  };\r\n\r\n  // 更新行号\r\n  const updateLineNumbers = () => {\r\n    const lines = content.split('\\n').length;\r\n    return Array.from({ length: lines }, (_, i) => i + 1);\r\n  };\r\n\r\n  // 生成HTML内容用于导出\r\n  const generateHTMLContent = () => {\r\n    // 简单的Markdown到HTML转换（避免SSR问题）\r\n    return content\r\n      .replace(/^# (.*$)/gim, '<h1>$1</h1>')\r\n      .replace(/^## (.*$)/gim, '<h2>$1</h2>')\r\n      .replace(/^### (.*$)/gim, '<h3>$1</h3>')\r\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n      .replace(/`(.*?)`/g, '<code>$1</code>')\r\n      .replace(/\\n/g, '<br>');\r\n  };\r\n\r\n  const insertText = (before: string, after: string = '') => {\r\n    const textarea = textareaRef.current;\r\n    if (!textarea) return;\r\n\r\n    const start = textarea.selectionStart;\r\n    const end = textarea.selectionEnd;\r\n    const selectedText = content.substring(start, end);\r\n    const newContent = content.substring(0, start) + before + selectedText + after + content.substring(end);\r\n\r\n    setContent(newContent);\r\n\r\n    // 重新设置光标位置\r\n    setTimeout(() => {\r\n      textarea.focus();\r\n      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);\r\n    }, 0);\r\n  };\r\n\r\n  const downloadFile = () => {\r\n    const blob = new Blob([content], { type: 'text/markdown' });\r\n    const url = URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = 'document.md';\r\n    document.body.appendChild(a);\r\n    a.click();\r\n    document.body.removeChild(a);\r\n    URL.revokeObjectURL(url);\r\n  };\r\n\r\n  const loadFile = (event?: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event?.target.files?.[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const text = e.target?.result as string;\r\n        setContent(text);\r\n      };\r\n      reader.readAsText(file);\r\n    }\r\n  };\r\n\r\n  const triggerFileOpen = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const newDocument = () => {\r\n    if (confirm('创建新文档？未保存的更改将丢失。')) {\r\n      setContent('# 新文档\\n\\n开始编写您的内容...');\r\n    }\r\n  };\r\n\r\n  // 快捷键配置\r\n  const shortcuts = createEditorShortcuts({\r\n    bold: () => insertText('**', '**'),\r\n    italic: () => insertText('*', '*'),\r\n    save: downloadFile,\r\n    open: triggerFileOpen,\r\n    newDocument,\r\n    find: () => setShowFindReplace(true),\r\n    replace: () => setShowFindReplace(true),\r\n    undo: () => {}, // TODO: 实现撤销功能\r\n    redo: () => {}, // TODO: 实现重做功能\r\n    selectAll: () => textareaRef.current?.select(),\r\n    copy: () => navigator.clipboard?.writeText(textareaRef.current?.value || ''),\r\n    paste: () => {}, // 浏览器默认处理\r\n    cut: () => {}, // 浏览器默认处理\r\n  });\r\n\r\n  useKeyboardShortcuts(shortcuts);\r\n\r\n  return (\r\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>\r\n      {/* Header */}\r\n      {!isFocusMode && (\r\n        <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            Martetdown\r\n          </h1>\r\n\r\n          {/* Toolbar */}\r\n          <div className=\"flex items-center gap-2\">\r\n            {/* 主题切换 */}\r\n            <div className=\"flex items-center gap-1 mr-4\">\r\n              <button\r\n                onClick={() => setTheme('light')}\r\n                className={`px-2 py-1 rounded text-sm ${\r\n                  theme === 'light'\r\n                    ? 'bg-yellow-200 text-yellow-800'\r\n                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n                }`}\r\n                title=\"亮色主题\"\r\n              >\r\n                ☀️\r\n              </button>\r\n              <button\r\n                onClick={() => setTheme('dark')}\r\n                className={`px-2 py-1 rounded text-sm ${\r\n                  theme === 'dark'\r\n                    ? 'bg-blue-200 text-blue-800'\r\n                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n                }`}\r\n                title=\"暗色主题\"\r\n              >\r\n                🌙\r\n              </button>\r\n              <button\r\n                onClick={() => setTheme('system')}\r\n                className={`px-2 py-1 rounded text-sm ${\r\n                  theme === 'system'\r\n                    ? 'bg-green-200 text-green-800'\r\n                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n                }`}\r\n                title=\"跟随系统\"\r\n              >\r\n                🖥️\r\n              </button>\r\n            </div>\r\n\r\n            {/* 翻译组件 */}\r\n            <TranslateWidget className=\"mr-4\" />\r\n            {/* File operations */}\r\n            <label className=\"px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium cursor-pointer\">\r\n              打开\r\n              <input\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\".md,.txt,.markdown\"\r\n                onChange={loadFile}\r\n                className=\"hidden\"\r\n              />\r\n            </label>\r\n            <button\r\n              onClick={downloadFile}\r\n              className=\"px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm font-medium\"\r\n              title=\"保存 (Ctrl+S)\"\r\n            >\r\n              保存\r\n            </button>\r\n            <button\r\n              onClick={newDocument}\r\n              className=\"px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium\"\r\n              title=\"新建 (Ctrl+N)\"\r\n            >\r\n              新建\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => setShowExportDialog(true)}\r\n              className=\"px-3 py-1 bg-indigo-500 hover:bg-indigo-600 text-white rounded text-sm font-medium\"\r\n              title=\"导出文档\"\r\n            >\r\n              导出\r\n            </button>\r\n\r\n            {/* 复制到Word按钮 */}\r\n            <CopyButton\r\n              content={content}\r\n              theme=\"modern\"\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              className=\"border-green-500 text-green-600 hover:bg-green-500 hover:text-white\"\r\n            />\r\n\r\n            {/* 编辑器模式切换 */}\r\n            <button\r\n              onClick={() => setUseRichEditor(!useRichEditor)}\r\n              className={`px-3 py-1 text-sm font-medium rounded transition-colors ${\r\n                useRichEditor\r\n                  ? 'bg-purple-500 hover:bg-purple-600 text-white'\r\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300'\r\n              }`}\r\n              title={useRichEditor ? '切换到Markdown编辑器' : '切换到富文本编辑器'}\r\n            >\r\n              {useRichEditor ? '🎨 富文本' : '📝 Markdown'}\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => setShowAIPanel(true)}\r\n              className=\"px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded text-sm font-medium\"\r\n              title=\"AI助手\"\r\n            >\r\n              🤖 AI\r\n            </button>\r\n\r\n            <IntelligentFormatting\r\n              content={content}\r\n              onContentChange={setContent}\r\n              className=\"ml-2\"\r\n            />\r\n\r\n            <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\" />\r\n\r\n            {/* Formatting */}\r\n            <button\r\n              onClick={() => insertText('**', '**')}\r\n              className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium\"\r\n              title=\"粗体 (Ctrl+B)\"\r\n            >\r\n              B\r\n            </button>\r\n            <button\r\n              onClick={() => insertText('*', '*')}\r\n              className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium\"\r\n              title=\"斜体 (Ctrl+I)\"\r\n            >\r\n              I\r\n            </button>\r\n            <button\r\n              onClick={() => insertText('`', '`')}\r\n              className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium\"\r\n              title=\"代码\"\r\n            >\r\n              代码\r\n            </button>\r\n            <button\r\n              onClick={() => insertText('\\n## ', '')}\r\n              className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium\"\r\n              title=\"标题\"\r\n            >\r\n              H2\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => setShowFindReplace(true)}\r\n              className=\"px-3 py-1 bg-orange-100 hover:bg-orange-200 dark:bg-orange-800 dark:hover:bg-orange-700 rounded text-sm font-medium\"\r\n              title=\"查找替换 (Ctrl+F)\"\r\n            >\r\n              查找\r\n            </button>\r\n\r\n            <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\" />\r\n\r\n            {/* 编辑器设置 */}\r\n            <button\r\n              onClick={() => setShowLineNumbers(!showLineNumbers)}\r\n              className={`px-3 py-1 rounded text-sm font-medium ${\r\n                showLineNumbers\r\n                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'\r\n                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n              }`}\r\n              title=\"切换行号\"\r\n            >\r\n              行号\r\n            </button>\r\n\r\n            <div className=\"flex items-center gap-1\">\r\n              <button\r\n                onClick={() => setFontSize(Math.max(10, fontSize - 1))}\r\n                className=\"px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm\"\r\n                title=\"减小字体\"\r\n              >\r\n                A-\r\n              </button>\r\n              <span className=\"text-sm text-gray-600 dark:text-gray-400 min-w-[2rem] text-center\">\r\n                {fontSize}\r\n              </span>\r\n              <button\r\n                onClick={() => setFontSize(Math.min(24, fontSize + 1))}\r\n                className=\"px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm\"\r\n                title=\"增大字体\"\r\n              >\r\n                A+\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\" />\r\n\r\n            {/* 视图模式 */}\r\n            <div className=\"flex items-center gap-1\">\r\n              <button\r\n                onClick={() => setViewMode('edit')}\r\n                className={`px-3 py-1 rounded text-sm font-medium ${\r\n                  viewMode === 'edit'\r\n                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'\r\n                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n                }`}\r\n                title=\"仅编辑\"\r\n              >\r\n                编辑\r\n              </button>\r\n              <button\r\n                onClick={() => setViewMode('split')}\r\n                className={`px-3 py-1 rounded text-sm font-medium ${\r\n                  viewMode === 'split'\r\n                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'\r\n                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n                }`}\r\n                title=\"分屏\"\r\n              >\r\n                分屏\r\n              </button>\r\n              <button\r\n                onClick={() => setViewMode('preview')}\r\n                className={`px-3 py-1 rounded text-sm font-medium ${\r\n                  viewMode === 'preview'\r\n                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'\r\n                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n                }`}\r\n                title=\"仅预览\"\r\n              >\r\n                预览\r\n              </button>\r\n            </div>\r\n\r\n            <button\r\n              onClick={() => setIsFocusMode(!isFocusMode)}\r\n              className={`px-3 py-1 rounded text-sm font-medium ${\r\n                isFocusMode\r\n                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'\r\n                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n              }`}\r\n              title=\"专注模式\"\r\n            >\r\n              专注\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => setIsFullscreen(!isFullscreen)}\r\n              className={`px-3 py-1 rounded text-sm font-medium ${\r\n                isFullscreen\r\n                  ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'\r\n                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'\r\n              }`}\r\n              title=\"全屏模式\"\r\n            >\r\n              {isFullscreen ? '退出' : '全屏'}\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => setShowShortcuts(!showShortcuts)}\r\n              className=\"px-3 py-1 bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-800 dark:hover:bg-yellow-700 rounded text-sm font-medium\"\r\n              title=\"快捷键帮助\"\r\n            >\r\n              ?\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </header>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className={`flex ${isFocusMode ? 'h-screen' : 'h-[calc(100vh-73px)]'}`}>\r\n        {/* Editor */}\r\n        {(viewMode === 'edit' || viewMode === 'split') && (\r\n          <div className={`${viewMode === 'split' ? 'w-1/2' : 'w-full'} ${viewMode === 'split' ? 'border-r border-gray-200 dark:border-gray-700' : ''} relative flex`}>\r\n          {/* 行号 */}\r\n          {showLineNumbers && (\r\n            <div\r\n              ref={lineNumbersRef}\r\n              className=\"w-12 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-hidden\"\r\n              style={{ fontSize: `${fontSize}px` }}\r\n            >\r\n              <div className=\"py-6 px-2 font-mono text-gray-500 dark:text-gray-400 text-right leading-relaxed select-none\">\r\n                {updateLineNumbers().map((lineNum) => (\r\n                  <div key={lineNum} className=\"whitespace-nowrap\">\r\n                    {lineNum}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {useRichEditor ? (\r\n            <UEditor\r\n              content={content}\r\n              onChange={setContent}\r\n              className=\"flex-1 h-full\"\r\n              height={600}\r\n            />\r\n          ) : (\r\n            <textarea\r\n              ref={textareaRef}\r\n              value={content}\r\n              onChange={(e) => setContent(e.target.value)}\r\n              onScroll={syncLineNumbers}\r\n              className={`flex-1 h-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono resize-none border-none outline-none leading-relaxed no-translate ${\r\n                showLineNumbers ? 'pl-4 pr-6 py-6' : 'p-6'\r\n              }`}\r\n              style={{ fontSize: `${fontSize}px` }}\r\n              placeholder=\"开始输入您的Markdown内容...\"\r\n            />\r\n          )}\r\n\r\n          {/* 快捷键帮助面板 */}\r\n          {showShortcuts && (\r\n            <div className=\"absolute top-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-4 max-w-xs z-10\">\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <h3 className=\"font-semibold text-gray-900 dark:text-gray-100\">快捷键</h3>\r\n                <button\r\n                  onClick={() => setShowShortcuts(false)}\r\n                  className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n                >\r\n                  ×\r\n                </button>\r\n              </div>\r\n              <div className=\"space-y-2 text-sm\">\r\n                {shortcuts.map((shortcut, index) => (\r\n                  <div key={index} className=\"flex justify-between\">\r\n                    <span className=\"text-gray-600 dark:text-gray-400\">{shortcut.description}</span>\r\n                    <span className=\"font-mono text-gray-800 dark:text-gray-200\">\r\n                      Ctrl+{shortcut.key.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n        )}\r\n\r\n        {/* Preview */}\r\n        {(viewMode === 'preview' || viewMode === 'split') && (\r\n          <div className={`${viewMode === 'split' ? 'w-1/2' : 'w-full'} bg-white dark:bg-gray-800 p-6 overflow-auto`}>\r\n            <div className=\"prose dark:prose-invert max-w-none\">\r\n              <ReactMarkdown remarkPlugins={[remarkGfm]}>\r\n                {content}\r\n              </ReactMarkdown>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Status Bar */}\r\n      {!isFocusMode && (\r\n        <div className=\"bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-6 py-2\">\r\n        <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400\">\r\n          <div className=\"flex items-center gap-4\">\r\n            <span>字符: {content.length}</span>\r\n            <span>单词: {content.trim() ? content.trim().split(/\\s+/).length : 0}</span>\r\n            <span>行数: {content.split('\\n').length}</span>\r\n            {autoSave.isSaving && (\r\n              <span className=\"text-blue-500\">保存中...</span>\r\n            )}\r\n            {autoSave.hasUnsavedChanges && !autoSave.isSaving && (\r\n              <span className=\"text-orange-500\">有未保存更改</span>\r\n            )}\r\n            {autoSave.lastSaved && !autoSave.hasUnsavedChanges && (\r\n              <span className=\"text-green-500\">\r\n                已保存 {autoSave.lastSaved.toLocaleTimeString()}\r\n              </span>\r\n            )}\r\n          </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <button\r\n              onClick={autoSave.save}\r\n              className=\"text-blue-500 hover:text-blue-700 underline\"\r\n              title=\"手动保存\"\r\n            >\r\n              立即保存\r\n            </button>\r\n            <span>Martetdown Editor v1.0</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      )}\r\n\r\n      {/* 查找替换对话框 */}\r\n      <FindReplaceDialog\r\n        isOpen={showFindReplace}\r\n        onClose={() => setShowFindReplace(false)}\r\n        content={content}\r\n        onContentChange={setContent}\r\n        textareaRef={textareaRef}\r\n      />\r\n\r\n      {/* 导出对话框 */}\r\n      <ExportDialog\r\n        isOpen={showExportDialog}\r\n        onClose={() => setShowExportDialog(false)}\r\n        content={content}\r\n        htmlContent={generateHTMLContent()}\r\n      />\r\n\r\n      {/* AI助手面板 */}\r\n      <AIPanel\r\n        isOpen={showAIPanel}\r\n        onClose={() => setShowAIPanel(false)}\r\n        content={content}\r\n        onContentChange={setContent}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACvE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,OAAO;IACP,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/D,SAAS;IACT,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,SAAS;QACpC,KAAK;QACL,OAAO;QACP,SAAS;IACX;IAEA,UAAU;IACV,MAAM,mBAAmB,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;QAC3C;QACA;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,SAAS,eAAe;QAC7C,IAAI,gBAAgB,iBAAiB,SAAS;YAC5C,MAAM,gBAAgB,QAAQ;YAC9B,IAAI,eAAe;gBACjB,WAAW;YACb;QACF;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,kBAAkB;QACtB,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,eAAe,OAAO,EAAE;QAErD,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,cAAc,eAAe,OAAO;QAE1C,YAAY,SAAS,GAAG,SAAS,SAAS;IAC5C;IAEA,OAAO;IACP,MAAM,oBAAoB;QACxB,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM;QACxC,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI;IACrD;IAEA,eAAe;IACf,MAAM,sBAAsB;QAC1B,8BAA8B;QAC9B,OAAO,QACJ,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,iBAAiB,eACzB,OAAO,CAAC,kBAAkB,uBAC1B,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,YAAY,mBACpB,OAAO,CAAC,OAAO;IACpB;IAEA,MAAM,aAAa,CAAC,QAAgB,QAAgB,EAAE;QACpD,MAAM,WAAW,YAAY,OAAO;QACpC,IAAI,CAAC,UAAU;QAEf,MAAM,QAAQ,SAAS,cAAc;QACrC,MAAM,MAAM,SAAS,YAAY;QACjC,MAAM,eAAe,QAAQ,SAAS,CAAC,OAAO;QAC9C,MAAM,aAAa,QAAQ,SAAS,CAAC,GAAG,SAAS,SAAS,eAAe,QAAQ,QAAQ,SAAS,CAAC;QAEnG,WAAW;QAEX,WAAW;QACX,WAAW;YACT,SAAS,KAAK;YACd,SAAS,iBAAiB,CAAC,QAAQ,OAAO,MAAM,EAAE,QAAQ,OAAO,MAAM,GAAG,aAAa,MAAM;QAC/F,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAgB;QACzD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,EAAE;QACrC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,OAAO,EAAE,MAAM,EAAE;gBACvB,WAAW;YACb;YACA,OAAO,UAAU,CAAC;QACpB;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,cAAc;QAClB,IAAI,QAAQ,qBAAqB;YAC/B,WAAW;QACb;IACF;IAEA,QAAQ;IACR,MAAM,YAAY,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE;QACtC,MAAM,IAAM,WAAW,MAAM;QAC7B,QAAQ,IAAM,WAAW,KAAK;QAC9B,MAAM;QACN,MAAM;QACN;QACA,MAAM,IAAM,mBAAmB;QAC/B,SAAS,IAAM,mBAAmB;QAClC,MAAM,KAAO;QACb,MAAM,KAAO;QACb,WAAW,IAAM,YAAY,OAAO,EAAE;QACtC,MAAM,IAAM,UAAU,SAAS,EAAE,UAAU,YAAY,OAAO,EAAE,SAAS;QACzE,OAAO,KAAO;QACd,KAAK,KAAO;IACd;IAEA,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE;IAErB,qBACE,8OAAC;QAAI,WAAW,CAAC,yCAAyC,EAAE,eAAe,uBAAuB,IAAI;;YAEnG,CAAC,6BACA,8OAAC;gBAAO,WAAU;0BAClB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,SAAS;4CACxB,WAAW,CAAC,0BAA0B,EACpC,UAAU,UACN,kCACA,yEACJ;4CACF,OAAM;sDACP;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,SAAS;4CACxB,WAAW,CAAC,0BAA0B,EACpC,UAAU,SACN,8BACA,yEACJ;4CACF,OAAM;sDACP;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,SAAS;4CACxB,WAAW,CAAC,0BAA0B,EACpC,UAAU,WACN,gCACA,yEACJ;4CACF,OAAM;sDACP;;;;;;;;;;;;8CAMH,8OAAC,2IAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAE3B,8OAAC;oCAAM,WAAU;;wCAAgG;sDAE/G,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CACP;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CACP;;;;;;8CAID,8OAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,WAAU;oCACV,OAAM;8CACP;;;;;;8CAKD,8OAAC,sIAAA,CAAA,aAAU;oCACT,SAAS;oCACT,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;;;;;;8CAIZ,8OAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAW,CAAC,wDAAwD,EAClE,gBACI,iDACA,0GACJ;oCACF,OAAO,gBAAgB,mBAAmB;8CAEzC,gBAAgB,WAAW;;;;;;8CAG9B,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;oCACV,OAAM;8CACP;;;;;;8CAID,8OAAC,iJAAA,CAAA,wBAAqB;oCACpB,SAAS;oCACT,iBAAiB;oCACjB,WAAU;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCACC,SAAS,IAAM,WAAW,MAAM;oCAChC,WAAU;oCACV,OAAM;8CACP;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,WAAW,KAAK;oCAC/B,WAAU;oCACV,OAAM;8CACP;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,WAAW,KAAK;oCAC/B,WAAU;oCACV,OAAM;8CACP;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,WAAW,SAAS;oCACnC,WAAU;oCACV,OAAM;8CACP;;;;;;8CAID,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;oCACV,OAAM;8CACP;;;;;;8CAID,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCACC,SAAS,IAAM,mBAAmB,CAAC;oCACnC,WAAW,CAAC,sCAAsC,EAChD,kBACI,kEACA,yEACJ;oCACF,OAAM;8CACP;;;;;;8CAID,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,IAAI,WAAW;4CACnD,WAAU;4CACV,OAAM;sDACP;;;;;;sDAGD,8OAAC;4CAAK,WAAU;sDACb;;;;;;sDAEH,8OAAC;4CACC,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,IAAI,WAAW;4CACnD,WAAU;4CACV,OAAM;sDACP;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,YAAY;4CAC3B,WAAW,CAAC,sCAAsC,EAChD,aAAa,SACT,kEACA,yEACJ;4CACF,OAAM;sDACP;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,YAAY;4CAC3B,WAAW,CAAC,sCAAsC,EAChD,aAAa,UACT,kEACA,yEACJ;4CACF,OAAM;sDACP;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,YAAY;4CAC3B,WAAW,CAAC,sCAAsC,EAChD,aAAa,YACT,kEACA,yEACJ;4CACF,OAAM;sDACP;;;;;;;;;;;;8CAKH,8OAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAW,CAAC,sCAAsC,EAChD,cACI,0EACA,yEACJ;oCACF,OAAM;8CACP;;;;;;8CAID,8OAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAW,CAAC,sCAAsC,EAChD,eACI,sEACA,yEACJ;oCACF,OAAM;8CAEL,eAAe,OAAO;;;;;;8CAGzB,8OAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;oCACV,OAAM;8CACP;;;;;;;;;;;;;;;;;;;;;;;0BASP,8OAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,cAAc,aAAa,wBAAwB;;oBAExE,CAAC,aAAa,UAAU,aAAa,OAAO,mBAC3C,8OAAC;wBAAI,WAAW,GAAG,aAAa,UAAU,UAAU,SAAS,CAAC,EAAE,aAAa,UAAU,kDAAkD,GAAG,cAAc,CAAC;;4BAE1J,iCACC,8OAAC;gCACC,KAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,UAAU,GAAG,SAAS,EAAE,CAAC;gCAAC;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,wBACxB,8OAAC;4CAAkB,WAAU;sDAC1B;2CADO;;;;;;;;;;;;;;;4BAQjB,8BACC,8OAAC,mIAAA,CAAA,UAAO;gCACN,SAAS;gCACT,UAAU;gCACV,WAAU;gCACV,QAAQ;;;;;qDAGV,8OAAC;gCACC,KAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,kBAAkB,mBAAmB,OACrC;gCACF,OAAO;oCAAE,UAAU,GAAG,SAAS,EAAE,CAAC;gCAAC;gCACnC,aAAY;;;;;;4BAKf,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DACX;;;;;;;;;;;;kDAIH,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAK,WAAU;kEAAoC,SAAS,WAAW;;;;;;kEACxE,8OAAC;wDAAK,WAAU;;4DAA6C;4DACrD,SAAS,GAAG,CAAC,WAAW;;;;;;;;+CAHxB;;;;;;;;;;;;;;;;;;;;;;oBAcnB,CAAC,aAAa,aAAa,aAAa,OAAO,mBAC9C,8OAAC;wBAAI,WAAW,GAAG,aAAa,UAAU,UAAU,SAAS,4CAA4C,CAAC;kCACxG,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;gCAAC,eAAe;oCAAC,6IAAA,CAAA,UAAS;iCAAC;0CACtC;;;;;;;;;;;;;;;;;;;;;;YAQV,CAAC,6BACA,8OAAC;gBAAI,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAK,QAAQ,MAAM;;;;;;;8CACzB,8OAAC;;wCAAK;wCAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,GAAG;;;;;;;8CACjE,8OAAC;;wCAAK;wCAAK,QAAQ,KAAK,CAAC,MAAM,MAAM;;;;;;;gCACpC,SAAS,QAAQ,kBAChB,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAEjC,SAAS,iBAAiB,IAAI,CAAC,SAAS,QAAQ,kBAC/C,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;gCAEnC,SAAS,SAAS,IAAI,CAAC,SAAS,iBAAiB,kBAChD,8OAAC;oCAAK,WAAU;;wCAAiB;wCAC1B,SAAS,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;sCAIhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,SAAS,IAAI;oCACtB,WAAU;oCACV,OAAM;8CACP;;;;;;8CAGD,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC,6IAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,SAAS;gBACT,iBAAiB;gBACjB,aAAa;;;;;;0BAIf,8OAAC,wIAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,SAAS;gBACT,aAAa;;;;;;0BAIf,8OAAC,4IAAA,CAAA,UAAO;gBACN,QAAQ;gBACR,SAAS,IAAM,eAAe;gBAC9B,SAAS;gBACT,iBAAiB;;;;;;;;;;;;AAIzB", "debugId": null}}]}