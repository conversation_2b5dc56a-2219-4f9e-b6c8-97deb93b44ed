'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { AdvancedRichTextEditor } from './AdvancedRichTextEditor';

// 声明UE全局变量
declare global {
  interface Window {
    UE: any;
    UEDITOR_CONFIG: any;
    UEDITOR_HOME_URL: string;
  }
}

interface UEditorProps {
  content: string;
  onChange: (content: string) => void;
  className?: string;
  config?: any;
  height?: number;
  width?: string;
}

export const UEditor: React.FC<UEditorProps> = ({
  content,
  onChange,
  className = '',
  config = {},
  height = 400,
  width = '100%'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<any>(null);
  const [isUEditorLoaded, setIsUEditorLoaded] = useState(false);
  const [isUEditorReady, setIsUEditorReady] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [isRichMode, setIsRichMode] = useState(false);

  // 检查UEditor是否可用
  const checkUEditorAvailability = useCallback(() => {
    // 检查是否已经加载
    if (window.UE) {
      setIsUEditorLoaded(true);
      return;
    }

    // 直接加载简化版本
    loadUEditorScripts();
  }, []);

  // 加载UEditor脚本
  const loadUEditorScripts = useCallback(() => {
    if (window.UE) {
      setIsUEditorLoaded(true);
      return;
    }

    // 设置UEditor路径
    window.UEDITOR_HOME_URL = '/ueditor/';

    // 使用简化版本的UEditor
    const script = document.createElement('script');
    script.src = '/ueditor/ueditor.all.min.js';
    script.onload = () => {
      // 检查UE对象是否创建成功
      if (window.UE && window.UE.getEditor) {
        setIsUEditorLoaded(true);
      } else {
        setLoadError('UEditor加载失败');
      }
    };
    script.onerror = () => {
      setLoadError('UEditor加载失败');
    };

    document.head.appendChild(script);
  }, []);

  // 初始化UEditor
  const initUEditor = useCallback(() => {
    if (!isUEditorLoaded || !window.UE || !containerRef.current) return;

    const editorId = `ueditor_${Date.now()}`;
    containerRef.current.id = editorId;

    const defaultConfig = {
      initialFrameHeight: height,
      initialFrameWidth: width,
      autoHeightEnabled: false,
      focus: false,
      readonly: false,
      enableAutoSave: false,
      toolbars: [[
        'fullscreen', 'source', '|', 'undo', 'redo', '|',
        'bold', 'italic', 'underline', 'strikethrough', '|',
        'forecolor', 'backcolor', '|',
        'insertorderedlist', 'insertunorderedlist', '|',
        'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',
        'link', 'unlink', '|',
        'simpleupload', 'insertimage', '|',
        'inserttable', 'deletetable', '|',
        'searchreplace', 'help'
      ]],
      ...config
    };

    try {
      const editor = window.UE.getEditor(editorId, defaultConfig);

      if (editor && typeof editor.ready === 'function') {
        editorRef.current = editor;

        editorRef.current.ready(() => {
          try {
            setIsUEditorReady(true);
            console.log('UEditor ready回调执行');

            // 设置初始内容
            if (content && editorRef.current && typeof editorRef.current.setContent === 'function') {
              editorRef.current.setContent(content);
            }

            // 监听内容变化
            if (editorRef.current && typeof editorRef.current.addListener === 'function') {
              editorRef.current.addListener('contentChange', () => {
                try {
                  if (editorRef.current && typeof editorRef.current.getContent === 'function') {
                    const newContent = editorRef.current.getContent();
                    onChange(newContent);
                  }
                } catch (error) {
                  console.error('UEditor内容变化处理失败:', error);
                }
              });
            }
          } catch (error) {
            console.error('UEditor ready回调执行失败:', error);
            setLoadError('UEditor初始化回调失败');
          }
        });
      } else {
        console.error('UEditor实例创建失败或ready方法不存在');
        setLoadError('UEditor实例创建失败');
      }
    } catch (error) {
      console.error('UEditor初始化失败:', error);
      setLoadError('UEditor初始化失败');
    }
  }, [isUEditorLoaded, content, onChange, config, height, width]);

  // 更新内容
  useEffect(() => {
    if (isUEditorReady &&
        editorRef.current &&
        typeof editorRef.current.getContent === 'function' &&
        typeof editorRef.current.setContent === 'function') {
      try {
        const currentContent = editorRef.current.getContent();
        if (content !== currentContent) {
          editorRef.current.setContent(content);
        }
      } catch (error) {
        console.error('UEditor内容更新失败:', error);
      }
    }
  }, [content, isUEditorReady]);

  // 组件挂载时加载UEditor
  useEffect(() => {
    checkUEditorAvailability();
  }, [checkUEditorAvailability]);

  // UEditor加载完成后初始化
  useEffect(() => {
    if (isUEditorLoaded) {
      initUEditor();
    }
  }, [isUEditorLoaded, initUEditor]);

  // 组件卸载时销毁编辑器
  useEffect(() => {
    return () => {
      if (editorRef.current && typeof editorRef.current.destroy === 'function') {
        try {
          editorRef.current.destroy();
          editorRef.current = null;
        } catch (error) {
          console.error('UEditor销毁失败:', error);
        }
      }
    };
  }, []);

  // 如果UEditor不可用，静默降级到备用编辑器
  if (loadError) {
    return (
      <div className={className}>
        {/* 静默使用备用编辑器，不显示警告 */}
        <AdvancedRichTextEditor
          content={content}
          onChange={onChange}
          className="border border-gray-300 dark:border-gray-600 rounded-md"
        />
      </div>
    );
  }

  // UEditor加载中
  if (!isUEditorLoaded) {
    return (
      <div className={`${className} flex items-center justify-center h-64 border border-gray-300 dark:border-gray-600 rounded-md`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600 dark:text-gray-400">正在加载UEditor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* 模式切换 */}
      <div className="flex items-center gap-2 mb-4 p-2 bg-gray-50 dark:bg-gray-700 rounded-t-md border border-gray-300 dark:border-gray-600">
        <span className="text-sm text-gray-600 dark:text-gray-400">编辑模式:</span>
        <button
          onClick={() => setIsRichMode(false)}
          className={`px-3 py-1 text-xs rounded ${
            !isRichMode 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
          }`}
        >
          Markdown
        </button>
        <button
          onClick={() => setIsRichMode(true)}
          className={`px-3 py-1 text-xs rounded ${
            isRichMode 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
          }`}
        >
          UEditor富文本
        </button>
      </div>

      {/* 编辑器内容 */}
      {isRichMode ? (
        // UEditor模式
        <div 
          ref={containerRef}
          className="border border-gray-300 dark:border-gray-600 rounded-b-md"
          style={{ minHeight: height }}
        />
      ) : (
        // Markdown模式
        <textarea
          value={content}
          onChange={(e) => onChange(e.target.value)}
          className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-b-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          style={{ height: height, lineHeight: '1.6' }}
          placeholder="输入Markdown内容..."
        />
      )}

      {/* 状态栏 */}
      <div className="flex items-center justify-between px-4 py-2 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border border-t-0 border-gray-300 dark:border-gray-600 rounded-b-md">
        <span>
          {isRichMode ? '🎨 UEditor富文本模式' : '📝 Markdown模式'} | 字符数: {content.length}
        </span>
        <span>
          {isRichMode ? 'UEditor提供强大的富文本编辑功能' : '支持标准Markdown语法'}
        </span>
      </div>
    </div>
  );
};
