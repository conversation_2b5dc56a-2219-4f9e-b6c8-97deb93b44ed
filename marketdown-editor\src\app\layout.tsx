import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Martetdown Editor - 强大的Markdown编辑器",
  description: "功能强大的Markdown编辑器，支持实时预览、AI助手、多格式导出和多语言翻译",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <script src="https://res.zvo.cn/translate/translate.js" async></script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}

        {/* Translate.js 初始化脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if (typeof translate !== 'undefined') {
                // 设置忽略翻译的标签和类名
                translate.ignore.tag.push('code', 'pre');
                translate.ignore.class.push('no-translate', 'markdown-editor', 'ai-content');

                // 设置使用v2版本
                translate.setUseVersion2();

                // 执行翻译初始化
                translate.execute();
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
