"""
Word文档格式转换器
"""

import os
import subprocess
from typing import Optional, List
from docx import Document
import docx2txt

from .base_converter import BaseConverter
from ..exceptions import ConversionError


class DocxConverter(BaseConverter):
    """Word文档转换器"""
    
    @property
    def supported_input_formats(self) -> list:
        return ['docx']
    
    @property
    def supported_output_formats(self) -> list:
        return ['pdf', 'xlsx', 'txt', 'md']
    
    def convert_to_pdf(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Word转PDF
        优先使用pandoc，备用LibreOffice
        """
        try:
            # 尝试使用pandoc
            cmd = [
                'pandoc',
                input_path,
                '-o', output_path,
                '--pdf-engine=xelatex'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return os.path.exists(output_path)
            else:
                self.logger.warning(f"Pandoc转换失败: {result.stderr}")
                
        except FileNotFoundError:
            self.logger.info("Pandoc不可用，尝试LibreOffice")
        
        # 尝试使用LibreOffice
        try:
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', os.path.dirname(output_path),
                input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # LibreOffice可能生成的文件名与期望不同
                expected_pdf = os.path.join(
                    os.path.dirname(output_path),
                    os.path.splitext(os.path.basename(input_path))[0] + '.pdf'
                )
                
                if os.path.exists(expected_pdf) and expected_pdf != output_path:
                    import shutil
                    shutil.move(expected_pdf, output_path)
                
                return os.path.exists(output_path)
                
        except FileNotFoundError:
            self.logger.warning("LibreOffice不可用")
        
        # 备用方法：使用reportlab
        return self._convert_docx_to_pdf_alternative(input_path, output_path, **kwargs)
    
    def convert_to_docx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Word转Word（复制文件）
        """
        try:
            import shutil
            shutil.copy2(input_path, output_path)
            return os.path.exists(output_path)
        except Exception as e:
            self.logger.error(f"Word复制失败: {str(e)}")
            return False
    
    def convert_to_xlsx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Word转Excel
        提取表格和文本内容
        """
        try:
            import pandas as pd
            
            # 读取Word文档
            doc = Document(input_path)
            
            # 提取表格
            tables_data = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                if table_data:
                    tables_data.append(table_data)
            
            # 如果有表格，创建Excel
            if tables_data:
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    for i, table_data in enumerate(tables_data):
                        if table_data:
                            df = pd.DataFrame(table_data[1:], columns=table_data[0] if len(table_data) > 1 else None)
                            sheet_name = f'Table_{i+1}' if len(tables_data) > 1 else 'Sheet1'
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
            else:
                # 如果没有表格，将文本内容放入Excel
                text_content = self._extract_text_from_docx(input_path)
                df = pd.DataFrame({'Content': [text_content]})
                df.to_excel(output_path, index=False)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Word转Excel失败: {str(e)}")
            return False
    
    def convert_to_txt(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Word转纯文本
        """
        try:
            # 使用docx2txt提取文本
            text_content = docx2txt.process(input_path)
            
            if not text_content:
                # 备用方法
                text_content = self._extract_text_from_docx(input_path)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Word转文本失败: {str(e)}")
            return False
    
    def convert_to_md(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Word转Markdown
        """
        try:
            # 尝试使用pandoc
            cmd = [
                'pandoc',
                input_path,
                '-o', output_path,
                '--wrap=none'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return os.path.exists(output_path)
            else:
                self.logger.warning(f"Pandoc转换失败: {result.stderr}")
                
        except FileNotFoundError:
            self.logger.info("Pandoc不可用，使用备用方法")
        
        # 备用方法：手动转换
        return self._convert_docx_to_md_alternative(input_path, output_path, **kwargs)
    
    def _extract_text_from_docx(self, docx_path: str) -> str:
        """
        从Word文档提取文本
        """
        try:
            doc = Document(docx_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
            
        except Exception as e:
            self.logger.error(f"文本提取失败: {str(e)}")
            return ""
    
    def _convert_docx_to_pdf_alternative(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        备用的Word转PDF方法
        """
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib.units import inch
            
            # 提取文本
            text_content = self._extract_text_from_docx(input_path)
            
            if not text_content:
                return False
            
            # 创建PDF
            doc_pdf = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # 按段落分割
            paragraphs = text_content.split('\n')
            
            for para_text in paragraphs:
                para_text = para_text.strip()
                if para_text:
                    para = Paragraph(para_text, styles['Normal'])
                    story.append(para)
                    story.append(Spacer(1, 6))
            
            doc_pdf.build(story)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"备用Word转PDF失败: {str(e)}")
            return False
    
    def _convert_docx_to_md_alternative(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        备用的Word转Markdown方法
        """
        try:
            doc = Document(input_path)
            markdown_content = []
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if not text:
                    markdown_content.append('')
                    continue
                
                # 检测标题样式
                if paragraph.style.name.startswith('Heading'):
                    level = 1
                    if 'Heading' in paragraph.style.name:
                        try:
                            level = int(paragraph.style.name.split()[-1])
                        except:
                            level = 1
                    
                    markdown_content.append('#' * level + ' ' + text)
                else:
                    markdown_content.append(text)
            
            # 处理表格
            for table in doc.tables:
                markdown_content.append('')  # 空行
                
                # 表格标题行
                header_row = [cell.text.strip() for cell in table.rows[0].cells]
                markdown_content.append('| ' + ' | '.join(header_row) + ' |')
                markdown_content.append('| ' + ' | '.join(['---'] * len(header_row)) + ' |')
                
                # 表格数据行
                for row in table.rows[1:]:
                    row_data = [cell.text.strip() for cell in row.cells]
                    markdown_content.append('| ' + ' | '.join(row_data) + ' |')
                
                markdown_content.append('')  # 空行
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"备用Word转Markdown失败: {str(e)}")
            return False
