{"name": "marte<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "docx": "^9.5.1", "file-saver": "^2.0.5", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "mysql2": "^3.11.5", "next": "15.4.4", "openai": "^5.10.2", "prismjs": "^1.30.0", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}