"""
文件处理工具函数
"""

import os
import mimetypes
from pathlib import Path
from typing import Dict, Optional, Tuple
import chardet

from ..exceptions import FileNotFoundError, InvalidFileError


def detect_file_format(file_path: str) -> str:
    """
    检测文件格式
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件格式 (pdf, docx, xlsx, txt, md)
        
    Raises:
        FileNotFoundError: 文件不存在
        InvalidFileError: 无法识别文件格式
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    # 获取文件扩展名
    _, ext = os.path.splitext(file_path.lower())
    
    # 扩展名映射
    ext_mapping = {
        '.pdf': 'pdf',
        '.docx': 'docx',
        '.doc': 'doc',  # 暂不支持，但可以识别
        '.xlsx': 'xlsx',
        '.xls': 'xls',  # 暂不支持，但可以识别
        '.txt': 'txt',
        '.md': 'md',
        '.markdown': 'md'
    }
    
    if ext in ext_mapping:
        return ext_mapping[ext]
    
    # 如果扩展名无法识别，尝试通过MIME类型检测
    mime_type, _ = mimetypes.guess_type(file_path)
    if mime_type:
        mime_mapping = {
            'application/pdf': 'pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
            'text/plain': 'txt',
            'text/markdown': 'md'
        }
        if mime_type in mime_mapping:
            return mime_mapping[mime_type]
    
    raise InvalidFileError(f"无法识别文件格式: {file_path}")


def validate_file(file_path: str) -> bool:
    """
    验证文件是否有效
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否有效
    """
    try:
        if not os.path.exists(file_path):
            return False
        
        if not os.path.isfile(file_path):
            return False
            
        if os.path.getsize(file_path) == 0:
            return False
            
        # 尝试检测文件格式
        detect_file_format(file_path)
        return True
        
    except Exception:
        return False


def get_file_info(file_path: str) -> Dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件信息字典
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    stat = os.stat(file_path)
    file_format = detect_file_format(file_path)
    
    info = {
        'path': file_path,
        'name': os.path.basename(file_path),
        'format': file_format,
        'size': stat.st_size,
        'modified': stat.st_mtime,
        'created': stat.st_ctime
    }
    
    # 对于文本文件，检测编码
    if file_format in ['txt', 'md']:
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB检测编码
                encoding_info = chardet.detect(raw_data)
                info['encoding'] = encoding_info.get('encoding', 'utf-8')
                info['confidence'] = encoding_info.get('confidence', 0.0)
        except Exception:
            info['encoding'] = 'utf-8'
            info['confidence'] = 0.0
    
    return info


def ensure_output_dir(output_path: str) -> None:
    """
    确保输出目录存在
    
    Args:
        output_path: 输出文件路径
    """
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)


def get_safe_filename(filename: str) -> str:
    """
    获取安全的文件名（移除特殊字符）
    
    Args:
        filename: 原始文件名
        
    Returns:
        安全的文件名
    """
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    safe_filename = filename
    for char in unsafe_chars:
        safe_filename = safe_filename.replace(char, '_')
    
    return safe_filename
