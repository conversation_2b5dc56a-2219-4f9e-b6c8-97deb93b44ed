#!/usr/bin/env python3
"""
文档转换器演示脚本
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from document_converter import DocumentConverter


def main():
    """主演示函数"""
    print("=== 文档转换器演示 ===\n")
    
    # 创建转换器实例
    converter = DocumentConverter(log_level='INFO')
    
    # 显示支持的格式
    print("支持的格式:")
    formats = converter.get_supported_formats()
    for fmt in formats:
        print(f"  - {fmt}")
    print()
    
    # 获取示例文件路径
    examples_dir = Path(__file__).parent
    sample_md = examples_dir / 'sample.md'
    sample_txt = examples_dir / 'sample.txt'
    
    # 创建输出目录
    output_dir = examples_dir / 'output'
    output_dir.mkdir(exist_ok=True)
    
    print("开始转换演示...\n")
    
    # 演示1: Markdown转文本
    if sample_md.exists():
        print("1. Markdown -> TXT")
        output_txt = output_dir / 'sample_from_md.txt'
        try:
            success = converter.convert(str(sample_md), str(output_txt))
            if success:
                print(f"   ✓ 转换成功: {output_txt}")
            else:
                print("   ✗ 转换失败")
        except Exception as e:
            print(f"   ✗ 转换出错: {e}")
        print()
    
    # 演示2: 文本转Markdown
    if sample_txt.exists():
        print("2. TXT -> Markdown")
        output_md = output_dir / 'sample_from_txt.md'
        try:
            success = converter.convert(str(sample_txt), str(output_md))
            if success:
                print(f"   ✓ 转换成功: {output_md}")
            else:
                print("   ✗ 转换失败")
        except Exception as e:
            print(f"   ✗ 转换出错: {e}")
        print()
    
    # 演示3: 创建临时Excel文件并转换
    print("3. 创建Excel并转换为其他格式")
    try:
        import pandas as pd
        
        # 创建示例数据
        data = {
            '姓名': ['张三', '李四', '王五', '赵六'],
            '年龄': [25, 30, 28, 35],
            '城市': ['北京', '上海', '广州', '深圳'],
            '职业': ['工程师', '设计师', '产品经理', '数据分析师']
        }
        
        df = pd.DataFrame(data)
        excel_file = output_dir / 'sample_data.xlsx'
        df.to_excel(excel_file, index=False)
        print(f"   创建Excel文件: {excel_file}")
        
        # 转换为Markdown
        excel_to_md = output_dir / 'sample_data.md'
        success = converter.convert(str(excel_file), str(excel_to_md))
        if success:
            print(f"   ✓ Excel -> Markdown: {excel_to_md}")
        else:
            print("   ✗ Excel -> Markdown 转换失败")
        
    except ImportError:
        print("   跳过Excel演示（需要pandas库）")
    except Exception as e:
        print(f"   ✗ Excel演示出错: {e}")
    print()
    
    # 演示4: 批量转换
    print("4. 批量转换演示")
    try:
        # 收集所有Markdown和文本文件
        input_files = []
        for pattern in ['*.md', '*.txt']:
            input_files.extend(examples_dir.glob(pattern))
        
        if input_files:
            print(f"   找到 {len(input_files)} 个文件进行批量转换")
            
            # 批量转换为不同格式
            batch_output_dir = output_dir / 'batch'
            batch_output_dir.mkdir(exist_ok=True)
            
            results = converter.batch_convert(
                [str(f) for f in input_files],
                str(batch_output_dir),
                'txt'  # 统一转换为文本格式
            )
            
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            print(f"   批量转换完成: {successful}/{total} 成功")
        else:
            print("   未找到可转换的文件")
            
    except Exception as e:
        print(f"   ✗ 批量转换出错: {e}")
    print()
    
    # 演示5: 文件信息查看
    print("5. 文件信息查看")
    if sample_md.exists():
        try:
            info = converter.get_file_info(str(sample_md))
            print(f"   文件: {info['name']}")
            print(f"   格式: {info['format']}")
            print(f"   大小: {info['size']} 字节")
            if 'encoding' in info:
                print(f"   编码: {info['encoding']}")
        except Exception as e:
            print(f"   ✗ 获取文件信息出错: {e}")
    print()
    
    print("=== 演示完成 ===")
    print(f"输出文件保存在: {output_dir}")
    print("\n提示:")
    print("- 某些转换功能需要安装额外工具（如pandoc、LibreOffice）")
    print("- 如果转换失败，请检查依赖是否正确安装")
    print("- 可以使用命令行工具进行更多操作：docconvert --help")


if __name__ == '__main__':
    main()
