"""
Markdown格式转换器
"""

import os
import subprocess
import tempfile
from typing import Optional
import markdown
from bs4 import BeautifulSoup

from .base_converter import BaseConverter
from ..exceptions import ConversionError


class MarkdownConverter(BaseConverter):
    """Markdown转换器"""
    
    @property
    def supported_input_formats(self) -> list:
        return ['md', 'markdown']
    
    @property
    def supported_output_formats(self) -> list:
        return ['pdf', 'docx', 'xlsx', 'txt']
    
    def convert_to_pdf(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Markdown转PDF
        使用pandoc进行转换
        """
        try:
            # 使用pandoc转换
            cmd = [
                'pandoc',
                input_path,
                '-o', output_path,
                '--pdf-engine=xelatex',  # 支持中文
                '-V', 'CJKmainfont=SimSun'  # 设置中文字体
            ]
            
            # 添加额外参数
            if kwargs.get('template'):
                cmd.extend(['--template', kwargs['template']])
            
            if kwargs.get('toc', False):
                cmd.append('--toc')
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return os.path.exists(output_path)
            else:
                self.logger.error(f"Pandoc错误: {result.stderr}")
                return False
                
        except FileNotFoundError:
            # 如果pandoc不可用，尝试其他方法
            return self._convert_md_to_pdf_alternative(input_path, output_path, **kwargs)
        except Exception as e:
            self.logger.error(f"Markdown转PDF失败: {str(e)}")
            return False
    
    def convert_to_docx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Markdown转Word
        使用pandoc进行转换
        """
        try:
            cmd = [
                'pandoc',
                input_path,
                '-o', output_path,
                '--reference-doc=' + kwargs.get('reference_doc', '')
            ]
            
            if kwargs.get('toc', False):
                cmd.append('--toc')
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return os.path.exists(output_path)
            else:
                self.logger.error(f"Pandoc错误: {result.stderr}")
                return False
                
        except FileNotFoundError:
            # 如果pandoc不可用，尝试其他方法
            return self._convert_md_to_docx_alternative(input_path, output_path, **kwargs)
        except Exception as e:
            self.logger.error(f"Markdown转Word失败: {str(e)}")
            return False
    
    def convert_to_xlsx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Markdown转Excel
        将Markdown表格转换为Excel
        """
        try:
            import pandas as pd
            
            # 读取Markdown文件
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 转换为HTML
            html = markdown.markdown(content, extensions=['tables'])
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找表格
            tables = soup.find_all('table')
            
            if not tables:
                # 如果没有表格，创建一个包含文本内容的Excel
                df = pd.DataFrame({'Content': [content]})
                df.to_excel(output_path, index=False)
                return True
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for i, table in enumerate(tables):
                    # 解析表格
                    rows = []
                    for tr in table.find_all('tr'):
                        row = [td.get_text(strip=True) for td in tr.find_all(['td', 'th'])]
                        if row:
                            rows.append(row)
                    
                    if rows:
                        df = pd.DataFrame(rows[1:], columns=rows[0] if len(rows) > 1 else None)
                        sheet_name = f'Table_{i+1}' if len(tables) > 1 else 'Sheet1'
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Markdown转Excel失败: {str(e)}")
            return False
    
    def convert_to_txt(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Markdown转纯文本
        移除Markdown标记
        """
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 转换为HTML然后提取纯文本
            html = markdown.markdown(content)
            soup = BeautifulSoup(html, 'html.parser')
            text = soup.get_text()
            
            # 清理多余的空行
            lines = text.split('\n')
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                line = line.strip()
                if line or not prev_empty:
                    cleaned_lines.append(line)
                prev_empty = not line
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(cleaned_lines))
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Markdown转文本失败: {str(e)}")
            return False
    
    def convert_to_md(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Markdown转Markdown（复制文件）
        """
        try:
            import shutil
            shutil.copy2(input_path, output_path)
            return os.path.exists(output_path)
        except Exception as e:
            self.logger.error(f"Markdown复制失败: {str(e)}")
            return False
    
    def _convert_md_to_pdf_alternative(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        备用的Markdown转PDF方法（不使用pandoc）
        """
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # 读取Markdown内容
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 转换为HTML
            html = markdown.markdown(content)
            soup = BeautifulSoup(html, 'html.parser')
            
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # 处理HTML元素
            for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol']):
                text = element.get_text()
                if element.name.startswith('h'):
                    # 标题
                    level = int(element.name[1])
                    style = styles['Heading1'] if level <= 3 else styles['Normal']
                    para = Paragraph(text, style)
                    story.append(para)
                    story.append(Spacer(1, 12))
                elif element.name == 'p':
                    # 段落
                    para = Paragraph(text, styles['Normal'])
                    story.append(para)
                    story.append(Spacer(1, 6))
            
            # 构建PDF
            doc.build(story)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"备用Markdown转PDF失败: {str(e)}")
            return False
    
    def _convert_md_to_docx_alternative(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        备用的Markdown转Word方法（不使用pandoc）
        """
        try:
            from docx import Document
            from docx.shared import Inches
            
            # 读取Markdown内容
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 转换为HTML
            html = markdown.markdown(content)
            soup = BeautifulSoup(html, 'html.parser')
            
            # 创建Word文档
            doc = Document()
            
            # 处理HTML元素
            for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol']):
                text = element.get_text()
                if element.name.startswith('h'):
                    # 标题
                    level = int(element.name[1])
                    heading = doc.add_heading(text, level=level)
                elif element.name == 'p':
                    # 段落
                    doc.add_paragraph(text)
            
            # 保存文档
            doc.save(output_path)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"备用Markdown转Word失败: {str(e)}")
            return False
