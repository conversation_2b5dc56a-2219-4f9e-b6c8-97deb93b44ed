'use client';

// IndexedDB 数据库配置
const DB_NAME = 'MartetdownDB';
const DB_VERSION = 2; // 增加版本号以支持新字段

// 数据存储对象
const STORES = {
  AI_MODELS: 'aiModels',
  DOCUMENTS: 'documents',
  SETTINGS: 'settings',
  TEMPLATES: 'templates',
} as const;

// AI模型接口
export interface AIModel {
  id?: number;
  name: string;
  api_key: string;
  base_url?: string;
  model_name: string;
  model_type: 'text' | 'image' | 'multimodal'; // 新增模型类型
  temperature: number;
  max_tokens: number;
  description?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// 数据库初始化
class IndexedDBManager {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const oldVersion = event.oldVersion;

        // 创建AI模型存储
        if (!db.objectStoreNames.contains(STORES.AI_MODELS)) {
          const aiModelsStore = db.createObjectStore(STORES.AI_MODELS, {
            keyPath: 'id',
            autoIncrement: true,
          });
          // 移除名称的唯一约束，允许重复名称
          aiModelsStore.createIndex('name', 'name', { unique: false });
          aiModelsStore.createIndex('is_default', 'is_default');
          aiModelsStore.createIndex('model_type', 'model_type'); // 新增模型类型索引
        } else if (oldVersion < 2) {
          // 版本2升级：添加模型类型索引
          const transaction = (event.target as IDBOpenDBRequest).transaction!;
          const aiModelsStore = transaction.objectStore(STORES.AI_MODELS);
          if (!aiModelsStore.indexNames.contains('model_type')) {
            aiModelsStore.createIndex('model_type', 'model_type');
          }
        }

        // 创建文档存储
        if (!db.objectStoreNames.contains(STORES.DOCUMENTS)) {
          const documentsStore = db.createObjectStore(STORES.DOCUMENTS, {
            keyPath: 'id',
          });
          documentsStore.createIndex('title', 'title');
          documentsStore.createIndex('updated_at', 'updated_at');
        }

        // 创建设置存储
        if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
          db.createObjectStore(STORES.SETTINGS, {
            keyPath: 'key',
          });
        }

        // 创建模板存储
        if (!db.objectStoreNames.contains(STORES.TEMPLATES)) {
          const templatesStore = db.createObjectStore(STORES.TEMPLATES, {
            keyPath: 'id',
          });
          templatesStore.createIndex('category', 'category');
        }
      };
    });
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db;
  }

  // AI模型操作
  async getAllAIModels(): Promise<AIModel[]> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.AI_MODELS], 'readonly');
      const store = transaction.objectStore(STORES.AI_MODELS);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('Failed to get AI models'));
      };
    });
  }

  async getAIModelById(id: number): Promise<AIModel | null> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.AI_MODELS], 'readonly');
      const store = transaction.objectStore(STORES.AI_MODELS);
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get AI model'));
      };
    });
  }

  async createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const db = await this.ensureDB();
    
    // 如果设置为默认，先取消其他默认设置
    if (model.is_default) {
      await this.clearDefaultAIModel();
    }

    const now = new Date().toISOString();
    const modelWithTimestamps: Omit<AIModel, 'id'> = {
      ...model,
      created_at: now,
      updated_at: now,
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.AI_MODELS], 'readwrite');
      const store = transaction.objectStore(STORES.AI_MODELS);
      const request = store.add(modelWithTimestamps);

      request.onsuccess = () => {
        resolve(request.result as number);
      };

      request.onerror = () => {
        reject(new Error('Failed to create AI model'));
      };
    });
  }

  async updateAIModel(id: number, updates: Partial<Omit<AIModel, 'id' | 'created_at'>>): Promise<void> {
    const db = await this.ensureDB();
    
    // 如果设置为默认，先取消其他默认设置
    if (updates.is_default) {
      await this.clearDefaultAIModel();
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.AI_MODELS], 'readwrite');
      const store = transaction.objectStore(STORES.AI_MODELS);
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const existingModel = getRequest.result;
        if (!existingModel) {
          reject(new Error('AI model not found'));
          return;
        }

        const updatedModel = {
          ...existingModel,
          ...updates,
          updated_at: new Date().toISOString(),
        };

        const putRequest = store.put(updatedModel);
        putRequest.onsuccess = () => resolve();
        putRequest.onerror = () => reject(new Error('Failed to update AI model'));
      };

      getRequest.onerror = () => {
        reject(new Error('Failed to get AI model for update'));
      };
    });
  }

  async deleteAIModel(id: number): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.AI_MODELS], 'readwrite');
      const store = transaction.objectStore(STORES.AI_MODELS);
      const request = store.delete(id);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to delete AI model'));
      };
    });
  }

  async getDefaultAIModel(): Promise<AIModel | null> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.AI_MODELS], 'readonly');
      const store = transaction.objectStore(STORES.AI_MODELS);
      const index = store.index('is_default');
      const request = index.get(true);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get default AI model'));
      };
    });
  }

  async setDefaultAIModel(id: number): Promise<void> {
    await this.clearDefaultAIModel();
    await this.updateAIModel(id, { is_default: true });
  }

  private async clearDefaultAIModel(): Promise<void> {
    const models = await this.getAllAIModels();
    const defaultModel = models.find(m => m.is_default);
    if (defaultModel && defaultModel.id) {
      await this.updateAIModel(defaultModel.id, { is_default: false });
    }
  }

  // 设置操作
  async getSetting(key: string): Promise<any> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.SETTINGS], 'readonly');
      const store = transaction.objectStore(STORES.SETTINGS);
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.value : null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get setting'));
      };
    });
  }

  async setSetting(key: string, value: any): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORES.SETTINGS], 'readwrite');
      const store = transaction.objectStore(STORES.SETTINGS);
      const request = store.put({
        key,
        value,
        updated_at: new Date().toISOString(),
      });

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to set setting'));
      };
    });
  }
}

// 创建单例实例
export const dbManager = new IndexedDBManager();

// 初始化默认AI模型
export const initializeDefaultModels = async () => {
  try {
    await dbManager.init();
    
    const existingModels = await dbManager.getAllAIModels();
    if (existingModels.length === 0) {
      // 添加默认模型
      const defaultModels = [
        {
          name: 'OpenAI GPT-3.5 Turbo',
          api_key: '',
          base_url: 'https://api.openai.com/v1',
          model_name: 'gpt-3.5-turbo',
          model_type: 'text' as const,
          temperature: 0.7,
          max_tokens: 2000,
          description: 'OpenAI官方GPT-3.5 Turbo模型，性价比高',
          is_default: true,
        },
        {
          name: 'OpenAI GPT-4',
          api_key: '',
          base_url: 'https://api.openai.com/v1',
          model_name: 'gpt-4',
          model_type: 'text' as const,
          temperature: 0.7,
          max_tokens: 2000,
          description: 'OpenAI最新GPT-4模型，能力更强',
          is_default: false,
        },
        {
          name: 'OpenAI GPT-4 Vision',
          api_key: '',
          base_url: 'https://api.openai.com/v1',
          model_name: 'gpt-4-vision-preview',
          model_type: 'multimodal' as const,
          temperature: 0.7,
          max_tokens: 2000,
          description: 'OpenAI GPT-4 Vision模型，支持图像理解',
          is_default: false,
        },
        {
          name: 'DALL-E 3',
          api_key: '',
          base_url: 'https://api.openai.com/v1',
          model_name: 'dall-e-3',
          model_type: 'image' as const,
          temperature: 0.7,
          max_tokens: 1000,
          description: 'OpenAI DALL-E 3图像生成模型',
          is_default: false,
        },
        {
          name: 'Azure OpenAI',
          api_key: '',
          base_url: '',
          model_name: 'gpt-35-turbo',
          model_type: 'text' as const,
          temperature: 0.7,
          max_tokens: 2000,
          description: 'Microsoft Azure OpenAI服务',
          is_default: false,
        },
      ];

      for (const model of defaultModels) {
        await dbManager.createAIModel(model);
      }
    }
  } catch (error) {
    console.error('Failed to initialize default models:', error);
  }
};
