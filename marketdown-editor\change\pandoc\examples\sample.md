# 文档转换器示例

这是一个示例Markdown文档，用于演示文档转换功能。

## 功能特性

文档转换器支持以下格式之间的转换：

- **PDF** - 便携式文档格式
- **Word (.docx)** - Microsoft Word文档
- **Excel (.xlsx)** - Microsoft Excel电子表格
- **TXT** - 纯文本文件
- **Markdown (.md)** - 标记语言文档

## 使用示例

### 命令行转换

```bash
# 单文件转换
docconvert convert input.pdf output.docx

# 批量转换
docconvert batch "*.md" -o ./converted -f pdf

# 查看文件信息
docconvert info document.pdf
```

### Python API

```python
from document_converter import DocumentConverter

converter = DocumentConverter()

# 单文件转换
converter.convert('input.md', 'output.pdf')

# 批量转换
converter.batch_convert(['file1.txt', 'file2.md'], 
                       output_format='docx', 
                       output_dir='./converted')
```

## 数据表格示例

| 格式 | 扩展名 | 支持读取 | 支持写入 |
|------|--------|----------|----------|
| PDF | .pdf | ✅ | ✅ |
| Word | .docx | ✅ | ✅ |
| Excel | .xlsx | ✅ | ✅ |
| 文本 | .txt | ✅ | ✅ |
| Markdown | .md | ✅ | ✅ |

## 注意事项

1. **依赖要求**: 某些转换功能需要安装额外的工具（如pandoc、LibreOffice）
2. **格式保持**: 转换过程中会尽可能保持原文档的格式和结构
3. **编码支持**: 自动检测文本文件编码，支持UTF-8、GBK等常见编码
4. **批量处理**: 支持并行处理多个文件，提高转换效率

## 技术实现

转换器使用以下核心库：

- `pandoc` - 通用文档转换
- `python-docx` - Word文档处理
- `openpyxl` - Excel文档处理
- `PyPDF2` / `pdfplumber` - PDF文档处理
- `reportlab` - PDF生成
- `markdown` - Markdown解析

---

*这个示例文档展示了Markdown的各种元素，包括标题、列表、表格、代码块等，可以用来测试转换到其他格式的效果。*
