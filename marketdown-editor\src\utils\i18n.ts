export type Language = 'zh-CN' | 'en-US';

export interface Translations {
  // 通用
  common: {
    save: string;
    cancel: string;
    confirm: string;
    delete: string;
    edit: string;
    close: string;
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
  };
  
  // 应用标题和描述
  app: {
    title: string;
    subtitle: string;
    description: string;
  };
  
  // 编辑器
  editor: {
    placeholder: string;
    newDocument: string;
    openFile: string;
    saveFile: string;
    export: string;
    find: string;
    replace: string;
    shortcuts: string;
    lineNumbers: string;
    fontSize: string;
    theme: string;
    fullscreen: string;
    focus: string;
    preview: string;
    split: string;
    editOnly: string;
  };
  
  // 文件管理
  file: {
    newDocument: string;
    openFile: string;
    saveDocument: string;
    deleteDocument: string;
    renameDocument: string;
    documents: string;
    noDocuments: string;
    createToStart: string;
    confirmDelete: string;
    enterTitle: string;
    enterNewTitle: string;
    untitled: string;
  };
  
  // 导出
  export: {
    title: string;
    format: string;
    documentTitle: string;
    author: string;
    pageSize: string;
    fontSize: string;
    font: string;
    includeStyles: string;
    exporting: string;
    exportFailed: string;
    characters: string;
    words: string;
    lines: string;
  };
  
  // AI助手
  ai: {
    title: string;
    configure: string;
    apiKey: string;
    baseUrl: string;
    model: string;
    saveConfig: string;
    configFailed: string;
    processing: string;
    failed: string;
    noContent: string;
    reconfigure: string;
    
    // AI功能
    functions: {
      grammar: string;
      grammarDesc: string;
      format: string;
      formatDesc: string;
      styleFormal: string;
      styleFormalDesc: string;
      styleCasual: string;
      styleCasualDesc: string;
      expand: string;
      expandDesc: string;
      summarize: string;
      summarizeDesc: string;
      translateEn: string;
      translateZh: string;
    };
  };
  
  // 查找替换
  findReplace: {
    title: string;
    find: string;
    replace: string;
    replaceWith: string;
    caseSensitive: string;
    regex: string;
    next: string;
    previous: string;
    replaceOne: string;
    replaceAll: string;
    matches: string;
    noMatches: string;
    findPlaceholder: string;
    replacePlaceholder: string;
  };
  
  // 状态栏
  status: {
    characters: string;
    words: string;
    lines: string;
    saving: string;
    unsavedChanges: string;
    saved: string;
    saveNow: string;
    version: string;
  };
  
  // 主题
  theme: {
    light: string;
    dark: string;
    system: string;
  };
  
  // 快捷键
  shortcuts: {
    title: string;
    bold: string;
    italic: string;
    save: string;
    open: string;
    new: string;
    find: string;
    replace: string;
    undo: string;
    redo: string;
    selectAll: string;
    copy: string;
    paste: string;
    cut: string;
  };
}

export const translations: Record<Language, Translations> = {
  'zh-CN': {
    common: {
      save: '保存',
      cancel: '取消',
      confirm: '确认',
      delete: '删除',
      edit: '编辑',
      close: '关闭',
      loading: '加载中...',
      error: '错误',
      success: '成功',
      warning: '警告',
      info: '信息',
    },
    
    app: {
      title: 'Martetdown Editor',
      subtitle: 'Markdown编辑器',
      description: '功能强大的Markdown编辑器，支持实时预览、AI助手、多格式导出',
    },
    
    editor: {
      placeholder: '开始输入您的Markdown内容...',
      newDocument: '新建',
      openFile: '打开',
      saveFile: '保存',
      export: '导出',
      find: '查找',
      replace: '替换',
      shortcuts: '快捷键',
      lineNumbers: '行号',
      fontSize: '字体大小',
      theme: '主题',
      fullscreen: '全屏',
      focus: '专注',
      preview: '预览',
      split: '分屏',
      editOnly: '编辑',
    },
    
    file: {
      newDocument: '新建文档',
      openFile: '打开文件',
      saveDocument: '保存文档',
      deleteDocument: '删除文档',
      renameDocument: '重命名文档',
      documents: '文档',
      noDocuments: '暂无文档',
      createToStart: '创建新文档开始编辑',
      confirmDelete: '确定要删除吗？',
      enterTitle: '请输入文档标题',
      enterNewTitle: '请输入新标题',
      untitled: '无标题文档',
    },
    
    export: {
      title: '导出文档',
      format: '导出格式',
      documentTitle: '文档标题',
      author: '作者',
      pageSize: '页面大小',
      fontSize: '字体大小',
      font: '字体',
      includeStyles: '包含样式',
      exporting: '导出中...',
      exportFailed: '导出失败',
      characters: '字符数',
      words: '单词数',
      lines: '行数',
    },
    
    ai: {
      title: 'AI助手',
      configure: 'AI配置',
      apiKey: 'OpenAI API密钥',
      baseUrl: 'API基础URL（可选）',
      model: '模型',
      saveConfig: '保存配置',
      configFailed: '配置失败，请检查API密钥',
      processing: 'AI正在处理中...',
      failed: 'AI处理失败',
      noContent: '请先输入一些内容',
      reconfigure: '重新配置AI',
      
      functions: {
        grammar: '语法检查',
        grammarDesc: '修正语法错误',
        format: '格式优化',
        formatDesc: '改善文档结构',
        styleFormal: '正式风格',
        styleFormalDesc: '商务正式',
        styleCasual: '轻松风格',
        styleCasualDesc: '友好亲切',
        expand: '内容扩展',
        expandDesc: '添加更多细节',
        summarize: '内容总结',
        summarizeDesc: '提取关键要点',
        translateEn: '翻译为英文',
        translateZh: '翻译为中文',
      },
    },
    
    findReplace: {
      title: '查找和替换',
      find: '查找',
      replace: '替换',
      replaceWith: '替换为',
      caseSensitive: '区分大小写',
      regex: '正则表达式',
      next: '下一个',
      previous: '上一个',
      replaceOne: '替换',
      replaceAll: '全部',
      matches: '个匹配项',
      noMatches: '无匹配项',
      findPlaceholder: '输入要查找的文本...',
      replacePlaceholder: '输入替换文本...',
    },
    
    status: {
      characters: '字符',
      words: '单词',
      lines: '行数',
      saving: '保存中...',
      unsavedChanges: '有未保存更改',
      saved: '已保存',
      saveNow: '立即保存',
      version: '版本',
    },
    
    theme: {
      light: '亮色主题',
      dark: '暗色主题',
      system: '跟随系统',
    },
    
    shortcuts: {
      title: '快捷键',
      bold: '粗体',
      italic: '斜体',
      save: '保存',
      open: '打开',
      new: '新建',
      find: '查找',
      replace: '替换',
      undo: '撤销',
      redo: '重做',
      selectAll: '全选',
      copy: '复制',
      paste: '粘贴',
      cut: '剪切',
    },
  },
  
  'en-US': {
    common: {
      save: 'Save',
      cancel: 'Cancel',
      confirm: 'Confirm',
      delete: 'Delete',
      edit: 'Edit',
      close: 'Close',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      warning: 'Warning',
      info: 'Info',
    },
    
    app: {
      title: 'Martetdown Editor',
      subtitle: 'Markdown Editor',
      description: 'Powerful Markdown editor with real-time preview, AI assistant, and multi-format export',
    },
    
    editor: {
      placeholder: 'Start typing your markdown content...',
      newDocument: 'New',
      openFile: 'Open',
      saveFile: 'Save',
      export: 'Export',
      find: 'Find',
      replace: 'Replace',
      shortcuts: 'Shortcuts',
      lineNumbers: 'Line Numbers',
      fontSize: 'Font Size',
      theme: 'Theme',
      fullscreen: 'Fullscreen',
      focus: 'Focus',
      preview: 'Preview',
      split: 'Split',
      editOnly: 'Edit',
    },
    
    file: {
      newDocument: 'New Document',
      openFile: 'Open File',
      saveDocument: 'Save Document',
      deleteDocument: 'Delete Document',
      renameDocument: 'Rename Document',
      documents: 'Documents',
      noDocuments: 'No documents yet',
      createToStart: 'Create a new document to get started',
      confirmDelete: 'Are you sure you want to delete?',
      enterTitle: 'Enter document title',
      enterNewTitle: 'Enter new title',
      untitled: 'Untitled Document',
    },
    
    export: {
      title: 'Export Document',
      format: 'Export Format',
      documentTitle: 'Document Title',
      author: 'Author',
      pageSize: 'Page Size',
      fontSize: 'Font Size',
      font: 'Font',
      includeStyles: 'Include Styles',
      exporting: 'Exporting...',
      exportFailed: 'Export failed',
      characters: 'Characters',
      words: 'Words',
      lines: 'Lines',
    },
    
    ai: {
      title: 'AI Assistant',
      configure: 'AI Configuration',
      apiKey: 'OpenAI API Key',
      baseUrl: 'API Base URL (Optional)',
      model: 'Model',
      saveConfig: 'Save Configuration',
      configFailed: 'Configuration failed, please check API key',
      processing: 'AI is processing...',
      failed: 'AI processing failed',
      noContent: 'Please enter some content first',
      reconfigure: 'Reconfigure AI',
      
      functions: {
        grammar: 'Grammar Check',
        grammarDesc: 'Fix grammar errors',
        format: 'Format Optimization',
        formatDesc: 'Improve document structure',
        styleFormal: 'Formal Style',
        styleFormalDesc: 'Business formal',
        styleCasual: 'Casual Style',
        styleCasualDesc: 'Friendly and approachable',
        expand: 'Content Expansion',
        expandDesc: 'Add more details',
        summarize: 'Content Summary',
        summarizeDesc: 'Extract key points',
        translateEn: 'Translate to English',
        translateZh: 'Translate to Chinese',
      },
    },
    
    findReplace: {
      title: 'Find and Replace',
      find: 'Find',
      replace: 'Replace',
      replaceWith: 'Replace with',
      caseSensitive: 'Case sensitive',
      regex: 'Regular expression',
      next: 'Next',
      previous: 'Previous',
      replaceOne: 'Replace',
      replaceAll: 'Replace All',
      matches: 'matches',
      noMatches: 'No matches',
      findPlaceholder: 'Enter text to find...',
      replacePlaceholder: 'Enter replacement text...',
    },
    
    status: {
      characters: 'Characters',
      words: 'Words',
      lines: 'Lines',
      saving: 'Saving...',
      unsavedChanges: 'Unsaved changes',
      saved: 'Saved',
      saveNow: 'Save now',
      version: 'Version',
    },
    
    theme: {
      light: 'Light Theme',
      dark: 'Dark Theme',
      system: 'Follow System',
    },
    
    shortcuts: {
      title: 'Shortcuts',
      bold: 'Bold',
      italic: 'Italic',
      save: 'Save',
      open: 'Open',
      new: 'New',
      find: 'Find',
      replace: 'Replace',
      undo: 'Undo',
      redo: 'Redo',
      selectAll: 'Select All',
      copy: 'Copy',
      paste: 'Paste',
      cut: 'Cut',
    },
  },
};

import { useState } from 'react';

// 国际化Hook
export const useI18n = () => {
  const getStoredLanguage = (): Language => {
    if (typeof window === 'undefined') return 'zh-CN';

    try {
      const stored = localStorage.getItem('martetdown-language');
      if (stored && (stored === 'zh-CN' || stored === 'en-US')) {
        return stored as Language;
      }
    } catch (error) {
      console.error('Failed to load language preference:', error);
    }

    // 默认中文
    return 'zh-CN';
  };

  const [currentLanguage, setCurrentLanguage] = useState<Language>(getStoredLanguage());

  const changeLanguage = (language: Language) => {
    setCurrentLanguage(language);
    try {
      localStorage.setItem('martetdown-language', language);
    } catch (error) {
      console.error('Failed to save language preference:', error);
    }
  };

  const t = translations[currentLanguage];

  return {
    language: currentLanguage,
    changeLanguage,
    t,
    availableLanguages: Object.keys(translations) as Language[],
  };
};
