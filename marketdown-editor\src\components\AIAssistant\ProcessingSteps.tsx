'use client';

import React from 'react';

interface ProcessingStepsProps {
  currentStep: string;
  progress: number;
  isVisible: boolean;
}

interface Step {
  id: string;
  title: string;
  description: string;
  icon: string;
  progressRange: [number, number];
}

const steps: Step[] = [
  {
    id: 'init',
    title: '初始化',
    description: '正在准备AI请求和分析内容',
    icon: '🔄',
    progressRange: [0, 25]
  },
  {
    id: 'analyze',
    title: '内容分析',
    description: '正在理解文档结构和语义',
    icon: '📝',
    progressRange: [25, 50]
  },
  {
    id: 'process',
    title: 'AI处理',
    description: '正在生成优化建议和内容',
    icon: '🤖',
    progressRange: [50, 80]
  },
  {
    id: 'apply',
    title: '应用结果',
    description: '正在整理和应用处理结果',
    icon: '✨',
    progressRange: [80, 95]
  },
  {
    id: 'complete',
    title: '完成',
    description: '处理完成，内容已优化',
    icon: '✅',
    progressRange: [95, 100]
  }
];

export const ProcessingSteps: React.FC<ProcessingStepsProps> = ({
  currentStep,
  progress,
  isVisible
}) => {
  if (!isVisible) return null;

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => 
      progress >= step.progressRange[0] && progress < step.progressRange[1]
    );
  };

  const currentStepIndex = getCurrentStepIndex();

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
      {/* 标题和总进度 */}
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <div className="flex-1">
          <h3 className="text-blue-700 dark:text-blue-300 font-medium">AI正在处理中</h3>
          <p className="text-sm text-blue-600 dark:text-blue-400">{currentStep}</p>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-blue-700 dark:text-blue-300">{progress}%</div>
          <div className="text-xs text-blue-600 dark:text-blue-400">完成度</div>
        </div>
      </div>

      {/* 总进度条 */}
      <div className="mb-4">
        <div className="w-full bg-blue-100 dark:bg-blue-800 rounded-full h-3">
          <div 
            className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-500 ease-out relative overflow-hidden"
            style={{ width: `${progress}%` }}
          >
            {/* 进度条动画效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* 步骤列表 */}
      <div className="space-y-2">
        {steps.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = progress > step.progressRange[1];
          const isUpcoming = progress < step.progressRange[0];

          return (
            <div
              key={step.id}
              className={`flex items-center gap-3 p-2 rounded-md transition-all duration-300 ${
                isActive 
                  ? 'bg-blue-100 dark:bg-blue-800/50 scale-105' 
                  : isCompleted
                  ? 'bg-green-50 dark:bg-green-900/20'
                  : 'bg-gray-50 dark:bg-gray-800/50'
              }`}
            >
              {/* 步骤图标 */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm transition-all duration-300 ${
                isActive
                  ? 'bg-blue-500 text-white animate-pulse'
                  : isCompleted
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
              }`}>
                {isCompleted ? '✓' : step.icon}
              </div>

              {/* 步骤信息 */}
              <div className="flex-1">
                <div className={`font-medium transition-colors duration-300 ${
                  isActive
                    ? 'text-blue-700 dark:text-blue-300'
                    : isCompleted
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {step.title}
                </div>
                <div className={`text-xs transition-colors duration-300 ${
                  isActive
                    ? 'text-blue-600 dark:text-blue-400'
                    : isCompleted
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 dark:text-gray-500'
                }`}>
                  {step.description}
                </div>
              </div>

              {/* 步骤状态 */}
              <div className="text-right">
                {isActive && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-ping"></div>
                    <span className="text-xs text-blue-600 dark:text-blue-400">进行中</span>
                  </div>
                )}
                {isCompleted && (
                  <span className="text-xs text-green-600 dark:text-green-400">已完成</span>
                )}
                {isUpcoming && (
                  <span className="text-xs text-gray-500 dark:text-gray-500">等待中</span>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 底部提示 */}
      <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-md">
        <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
          <span>💡</span>
          <span>
            {progress < 50 
              ? "AI正在分析您的文档内容，请稍候..."
              : progress < 80
              ? "AI正在生成优化建议，马上就好..."
              : "即将完成，正在整理结果..."
            }
          </span>
        </div>
      </div>
    </div>
  );
};
