'use client';

import React, { useRef } from 'react';
import { markdownActions } from '@/utils/markdown';

interface MarkdownToolbarProps {
  onAction?: (actionName: string) => void;
  className?: string;
}

export const MarkdownToolbar: React.FC<MarkdownToolbarProps> = ({ 
  onAction,
  className = '' 
}) => {
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);

  // 获取当前活动的textarea元素
  const getActiveTextarea = (): HTMLTextAreaElement | null => {
    const activeElement = document.activeElement;
    if (activeElement && activeElement.tagName === 'TEXTAREA') {
      return activeElement as HTMLTextAreaElement;
    }
    
    // 如果没有活动的textarea，尝试找到页面上的markdown编辑器
    const textarea = document.querySelector('textarea[placeholder*="markdown"]') as HTMLTextAreaElement;
    return textarea || null;
  };

  const handleAction = (action: typeof markdownActions[0]) => {
    const textarea = getActiveTextarea();
    if (textarea) {
      action.action(textarea);
      onAction?.(action.name);
    }
  };

  return (
    <div className={`flex flex-wrap gap-1 p-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 ${className}`}>
      {markdownActions.map((action) => (
        <button
          key={action.name}
          onClick={() => handleAction(action)}
          title={`${action.name}${action.shortcut ? ` (${action.shortcut})` : ''}`}
          className="
            px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300
            bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600
            border border-gray-300 dark:border-gray-600 rounded
            transition-colors duration-200
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
            disabled:opacity-50 disabled:cursor-not-allowed
          "
        >
          {action.icon}
        </button>
      ))}
      
      {/* 分隔符 */}
      <div className="w-px bg-gray-300 dark:bg-gray-600 mx-1" />
      
      {/* 额外的工具按钮 */}
      <button
        onClick={() => {
          const textarea = getActiveTextarea();
          if (textarea) {
            // 清空内容
            textarea.value = '';
            textarea.dispatchEvent(new Event('input', { bubbles: true }));
            textarea.focus();
          }
        }}
        title="Clear content"
        className="
          px-3 py-1.5 text-sm font-medium text-red-600 dark:text-red-400
          bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30
          border border-red-300 dark:border-red-600 rounded
          transition-colors duration-200
          focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1
        "
      >
        Clear
      </button>
    </div>
  );
};
