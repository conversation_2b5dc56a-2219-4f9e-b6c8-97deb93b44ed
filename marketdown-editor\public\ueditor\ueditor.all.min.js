/**
 * UEditor 简化加载器
 * 直接创建UE对象和基础功能
 */

// 设置UEditor路径
window.UEDITOR_HOME_URL = window.UEDITOR_HOME_URL || '/ueditor/';

// 创建基础的UE对象
window.UE = window.UE || {};

// 基础工具函数
window.UE.utils = {
  isIE: function() {
    return navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident') !== -1;
  },
  
  extend: function(target, source) {
    for (var key in source) {
      if (source.hasOwnProperty(key)) {
        target[key] = source[key];
      }
    }
    return target;
  },
  
  each: function(obj, fn) {
    if (obj.length) {
      for (var i = 0; i < obj.length; i++) {
        if (fn.call(obj[i], i, obj[i]) === false) break;
      }
    } else {
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (fn.call(obj[key], key, obj[key]) === false) break;
        }
      }
    }
  }
};

// 基础编辑器类
window.UE.Editor = function(options, container) {
  this.options = window.UE.utils.extend({
    initialFrameHeight: 320,
    initialFrameWidth: '100%',
    autoHeightEnabled: false,
    focus: false,
    readonly: false,
    elementPathEnabled: false,
    wordCount: false,
    maximumWords: 10000,
    toolbars: [[
      'fullscreen', 'source', '|', 'undo', 'redo', '|',
      'bold', 'italic', 'underline', 'strikethrough', '|',
      'forecolor', 'backcolor', '|',
      'insertorderedlist', 'insertunorderedlist', '|',
      'justifyleft', 'justifycenter', 'justifyright', '|',
      'link', 'unlink', '|',
      'simpleupload', 'insertimage', '|',
      'inserttable', 'searchreplace'
    ]]
  }, options || {});

  this.container = container;
  this.ready = false;
  this.listeners = {};
  this.content = '';
  this._isReady = false;

  this.init();
};

window.UE.Editor.prototype = {
  init: function() {
    var me = this;

    // 创建编辑区域
    this.createEditor();

    // 模拟ready状态
    setTimeout(function() {
      me.ready = true;
      me._isReady = true;
      me.fireEvent('ready');
      console.log('UEditor实例初始化完成');
    }, 200);
  },
  
  createEditor: function() {
    var container = typeof this.container === 'string'
      ? document.getElementById(this.container)
      : this.container;

    if (!container) {
      console.error('UEditor容器未找到:', this.container);
      return;
    }

    // 清空容器
    container.innerHTML = '';

    // 创建工具栏
    this.createToolbar(container);

    // 创建编辑区域
    var editorContainer = document.createElement('div');
    editorContainer.style.position = 'relative';
    editorContainer.style.border = '1px solid #ccc';
    editorContainer.style.borderTop = 'none';

    var iframe = document.createElement('iframe');
    iframe.style.width = '100%';
    iframe.style.height = this.options.initialFrameHeight + 'px';
    iframe.style.border = 'none';
    iframe.style.display = 'block';
    iframe.frameBorder = '0';

    editorContainer.appendChild(iframe);
    container.appendChild(editorContainer);

    var doc = iframe.contentDocument || iframe.contentWindow.document;
    doc.open();
    doc.write('<!DOCTYPE html><html><head><meta charset="utf-8"><style>body{margin:8px;font-family:"Microsoft YaHei",Arial,sans-serif;font-size:14px;line-height:1.6;color:#333;background:#fff;}p{margin:0 0 10px 0;}</style></head><body contenteditable="true"><p><br></p></body></html>');
    doc.close();

    this.document = doc;
    this.body = doc.body;
    this.iframe = iframe;
    this.container = container;

    // 绑定事件
    var me = this;
    this.body.addEventListener('input', function() {
      me.content = me.body.innerHTML;
      me.fireEvent('contentChange');
    });

    this.body.addEventListener('keydown', function(e) {
      me.fireEvent('keydown', e);
    });

    this.body.addEventListener('focus', function() {
      me.fireEvent('focus');
    });

    this.body.addEventListener('blur', function() {
      me.fireEvent('blur');
    });
  },

  createToolbar: function(container) {
    var toolbar = document.createElement('div');
    toolbar.style.background = '#f1f1f1';
    toolbar.style.border = '1px solid #ccc';
    toolbar.style.padding = '5px';
    toolbar.style.fontSize = '0';

    var buttons = [
      {name: 'bold', title: '粗体', cmd: 'bold'},
      {name: 'italic', title: '斜体', cmd: 'italic'},
      {name: 'underline', title: '下划线', cmd: 'underline'},
      {name: 'strikethrough', title: '删除线', cmd: 'strikeThrough'},
      {name: '|', title: '分隔符'},
      {name: 'justifyleft', title: '左对齐', cmd: 'justifyLeft'},
      {name: 'justifycenter', title: '居中', cmd: 'justifyCenter'},
      {name: 'justifyright', title: '右对齐', cmd: 'justifyRight'},
      {name: '|', title: '分隔符'},
      {name: 'insertorderedlist', title: '有序列表', cmd: 'insertOrderedList'},
      {name: 'insertunorderedlist', title: '无序列表', cmd: 'insertUnorderedList'},
      {name: '|', title: '分隔符'},
      {name: 'undo', title: '撤销', cmd: 'undo'},
      {name: 'redo', title: '重做', cmd: 'redo'}
    ];

    var me = this;
    buttons.forEach(function(btn) {
      if (btn.name === '|') {
        var sep = document.createElement('span');
        sep.style.display = 'inline-block';
        sep.style.width = '1px';
        sep.style.height = '20px';
        sep.style.background = '#ccc';
        sep.style.margin = '0 5px';
        sep.style.verticalAlign = 'middle';
        toolbar.appendChild(sep);
      } else {
        var button = document.createElement('button');
        button.innerHTML = btn.title;
        button.title = btn.title;
        button.style.margin = '0 2px';
        button.style.padding = '4px 8px';
        button.style.border = '1px solid #ccc';
        button.style.background = '#fff';
        button.style.cursor = 'pointer';
        button.style.fontSize = '12px';

        if (btn.cmd) {
          button.onclick = function() {
            me.execCommand(btn.cmd);
            me.focus();
          };
        }

        toolbar.appendChild(button);
      }
    });

    container.appendChild(toolbar);
    this.toolbar = toolbar;
  },
  
  ready: function(fn) {
    if (this.ready) {
      fn.call(this);
    } else {
      this.addListener('ready', fn);
    }
  },
  
  addListener: function(type, fn) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type].push(fn);
  },
  
  fireEvent: function(type) {
    var listeners = this.listeners[type];
    if (listeners) {
      for (var i = 0; i < listeners.length; i++) {
        listeners[i].call(this);
      }
    }
  },
  
  setContent: function(content) {
    if (this.body) {
      this.body.innerHTML = content || '';
      this.content = content || '';
    }
  },
  
  getContent: function() {
    if (this.body) {
      this.content = this.body.innerHTML;
    }
    return this.content || '';
  },
  
  focus: function() {
    if (this.body) {
      this.body.focus();
    }
  },
  
  destroy: function() {
    if (this.iframe && this.iframe.parentNode) {
      this.iframe.parentNode.removeChild(this.iframe);
    }
    this.listeners = {};
  },
  
  execCommand: function(cmd, value) {
    if (this.document) {
      try {
        this.document.execCommand(cmd, false, value);
        this.fireEvent('contentChange');
      } catch (e) {
        console.warn('Command not supported:', cmd);
      }
    }
  }
};

// getEditor 工厂函数
window.UE.getEditor = function(container, options) {
  return new window.UE.Editor(options, container);
};

// 版本信息
window.UE.version = '1.5.0-simple';

// 标记为已加载
window.UE._isReady = true;

console.log('UEditor Simple Version Loaded');
