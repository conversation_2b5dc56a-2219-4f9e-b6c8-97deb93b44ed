import { dbManager } from './indexedDB';
import { mysqlManager } from './mysql';

// 数据库迁移工具
export class DatabaseMigration {
  
  // 检查MySQL连接状态
  async checkMySQLConnection(): Promise<boolean> {
    try {
      await mysqlManager.init();
      return true;
    } catch (error) {
      console.error('MySQL连接检查失败:', error);
      return false;
    }
  }

  // 从IndexedDB迁移AI模型到MySQL
  async migrateAIModels(): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    try {
      // 获取IndexedDB中的AI模型
      const indexedModels = await dbManager.getAllAIModels();
      console.log(`找到 ${indexedModels.length} 个AI模型需要迁移`);

      for (const model of indexedModels) {
        try {
          // 转换数据格式
          const mysqlModel = {
            name: model.name,
            api_key: model.api_key,
            base_url: model.base_url,
            model_name: model.model_name,
            model_type: model.model_type,
            temperature: model.temperature,
            max_tokens: model.max_tokens,
            description: model.description,
            is_default: model.is_default,
          };

          await mysqlManager.createAIModel(mysqlModel);
          success++;
          console.log(`AI模型 "${model.name}" 迁移成功`);
        } catch (error) {
          failed++;
          console.error(`AI模型 "${model.name}" 迁移失败:`, error);
        }
      }
    } catch (error) {
      console.error('AI模型迁移过程出错:', error);
    }

    return { success, failed };
  }

  // 从IndexedDB迁移设置到MySQL
  async migrateSettings(): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    try {
      // 常见的设置键
      const settingKeys = [
        'theme',
        'language',
        'editor-config',
        'auto-save',
        'font-size',
        'line-numbers',
        'word-wrap'
      ];

      for (const key of settingKeys) {
        try {
          const value = await dbManager.getSetting(key);
          if (value !== null) {
            await mysqlManager.setSetting(key, value);
            success++;
            console.log(`设置 "${key}" 迁移成功`);
          }
        } catch (error) {
          failed++;
          console.error(`设置 "${key}" 迁移失败:`, error);
        }
      }
    } catch (error) {
      console.error('设置迁移过程出错:', error);
    }

    return { success, failed };
  }

  // 完整迁移流程
  async migrateAll(): Promise<{
    aiModels: { success: number; failed: number };
    settings: { success: number; failed: number };
    totalTime: number;
  }> {
    const startTime = Date.now();
    
    console.log('开始数据库迁移...');
    
    // 初始化MySQL
    await mysqlManager.init();
    
    // 迁移AI模型
    console.log('迁移AI模型...');
    const aiModelsResult = await this.migrateAIModels();
    
    // 迁移设置
    console.log('迁移设置...');
    const settingsResult = await this.migrateSettings();
    
    const totalTime = Date.now() - startTime;
    
    console.log('数据库迁移完成!');
    console.log(`总耗时: ${totalTime}ms`);
    console.log(`AI模型: 成功 ${aiModelsResult.success}, 失败 ${aiModelsResult.failed}`);
    console.log(`设置: 成功 ${settingsResult.success}, 失败 ${settingsResult.failed}`);
    
    return {
      aiModels: aiModelsResult,
      settings: settingsResult,
      totalTime
    };
  }

  // 清空MySQL数据（谨慎使用）
  async clearMySQLData(): Promise<void> {
    try {
      await mysqlManager.init();
      
      // 清空所有表
      const connection = await (mysqlManager as any).getConnection();
      await connection.execute('DELETE FROM ai_models');
      await connection.execute('DELETE FROM documents');
      await connection.execute('DELETE FROM settings');
      await connection.execute('DELETE FROM templates');
      
      console.log('MySQL数据已清空');
    } catch (error) {
      console.error('清空MySQL数据失败:', error);
      throw error;
    }
  }

  // 验证迁移结果
  async validateMigration(): Promise<{
    aiModelsMatch: boolean;
    settingsMatch: boolean;
    details: any;
  }> {
    try {
      // 比较AI模型数量
      const indexedModels = await dbManager.getAllAIModels();
      const mysqlModels = await mysqlManager.getAllAIModels();
      const aiModelsMatch = indexedModels.length === mysqlModels.length;

      // 检查设置
      const settingKeys = ['theme', 'language', 'editor-config'];
      let settingsMatch = true;
      const settingsDetails: any = {};

      for (const key of settingKeys) {
        try {
          const indexedValue = await dbManager.getSetting(key);
          const mysqlValue = await mysqlManager.getSetting(key);
          const match = JSON.stringify(indexedValue) === JSON.stringify(mysqlValue);
          settingsDetails[key] = { indexedValue, mysqlValue, match };
          if (!match) settingsMatch = false;
        } catch (error) {
          settingsDetails[key] = { error: error.message };
          settingsMatch = false;
        }
      }

      return {
        aiModelsMatch,
        settingsMatch,
        details: {
          aiModels: {
            indexed: indexedModels.length,
            mysql: mysqlModels.length
          },
          settings: settingsDetails
        }
      };
    } catch (error) {
      console.error('验证迁移结果失败:', error);
      throw error;
    }
  }
}

// 创建迁移工具实例
export const dbMigration = new DatabaseMigration();

// 便捷函数
export const migrateToMySQL = async () => {
  return await dbMigration.migrateAll();
};

export const checkMySQLStatus = async () => {
  return await dbMigration.checkMySQLConnection();
};
