// 文档格式标准配置

export interface FormatStandard {
  id: string;
  name: string;
  description: string;
  icon: string;
  rules: {
    title: TitleRules;
    content: ContentRules;
    special: SpecialElementRules;
    layout: LayoutRules;
  };
  prompt: string;
}

export interface TitleRules {
  h1: string;
  h2: string;
  h3: string;
  h4: string;
  numbering: string;
  spacing: string;
}

export interface ContentRules {
  font: string;
  spacing: string;
  paragraph: string;
  emphasis: string;
  mixedText: string;
}

export interface SpecialElementRules {
  table: string;
  list: string;
  quote: string;
  code: string;
  image: string;
}

export interface LayoutRules {
  margins: string;
  alignment: string;
  pageSetup: string;
  headerFooter: string;
}

export const formatStandards: FormatStandard[] = [
  {
    id: 'academic',
    name: '学术论文',
    description: '符合学术规范的严谨格式',
    icon: '🎓',
    rules: {
      title: {
        h1: '一级标题：三号宋体加粗，居中，段前距1.5行，段后距1行',
        h2: '二级标题：四号黑体，左对齐，段前距1行，段后距0.5行',
        h3: '三级标题：小四号楷体加粗，左对齐，段前距0.5行',
        h4: '四级标题：五号宋体加粗，首行缩进2字符',
        numbering: '使用阿拉伯数字编号：1. 1.1 1.1.1',
        spacing: '标题后不加标点符号，与正文间距0.5行'
      },
      content: {
        font: '正文：五号宋体，行间距1.5倍，段间距0.5行',
        spacing: '首行缩进2字符，避免单字成行',
        paragraph: '段落长度控制在3-8行，超过8行建议拆分',
        emphasis: '关键词用【加粗】，重要数据用【加粗+红色】',
        mixedText: '中英文混排：英文用Times New Roman，中英文间空1格'
      },
      special: {
        table: '表格居中，1.5磅实线边框，表头小五号黑体加粗居中，表内小五号宋体',
        list: '有序列表用"1. 2. 3."，无序列表用"•"，列表项间距0.5行',
        quote: '引用用【斜体+双引号】，句尾标注来源',
        code: '代码用等宽字体，浅灰背景，单独成段',
        image: '图片居中，宽度不超过页面80%，下方标注图号和标题'
      },
      layout: {
        margins: '页边距：上下2.5cm，左右2cm',
        alignment: '正文两端对齐，标题居中或左对齐',
        pageSetup: 'A4纸张，纵向排版',
        headerFooter: '页眉：文档标题（左）+页码（右），页脚：部门+日期'
      }
    },
    prompt: `请按照学术论文格式标准优化这份文档，具体要求：

**标题格式：**
- 一级标题：# 使用三号宋体加粗，居中，段前距1.5行
- 二级标题：## 使用四号黑体，左对齐，段前距1行  
- 三级标题：### 使用小四号楷体加粗，左对齐，段前距0.5行
- 标题编号使用阿拉伯数字：1. 1.1 1.1.1
- 标题后不加标点符号

**正文格式：**
- 使用五号宋体，行间距1.5倍，段间距0.5行
- 首行缩进2字符，段落长度控制在3-8行
- 中英文混排时英文用Times New Roman，中英文间空1格
- 关键词用**加粗**，重要数据用**加粗**标注

**特殊元素：**
- 表格居中，使用1.5磅实线边框，表头加粗居中
- 有序列表用"1. 2. 3."，无序列表用"•"
- 引用用*斜体*加双引号，句尾标注来源
- 图片居中，宽度不超过页面80%，下方标注图号

**检查要点：**
- 确保格式前后一致，无多余空行空格
- 重点内容突出清晰，层级分明
- 符合学术规范，专业严谨

请优化后说明主要修改内容。`
  },
  
  {
    id: 'business',
    name: '商务报告',
    description: '专业商务文档格式',
    icon: '💼',
    rules: {
      title: {
        h1: '一级标题：二号微软雅黑加粗，居中，蓝色',
        h2: '二级标题：三号微软雅黑加粗，左对齐，深蓝色',
        h3: '三级标题：四号微软雅黑，左对齐，段前距0.5行',
        h4: '四级标题：小四号微软雅黑，首行缩进2字符',
        numbering: '使用数字编号：1. 1.1 1.1.1',
        spacing: '标题与正文间距1行，突出层级'
      },
      content: {
        font: '正文：小四号微软雅黑，行间距1.25倍',
        spacing: '段间距0.5行，重要段落前后空1行',
        paragraph: '段落简洁明了，每段3-5行',
        emphasis: '关键数据用【加粗+蓝色】，百分比和金额突出显示',
        mixedText: '数字与中文间空1格，如"增长 25%"'
      },
      special: {
        table: '表格现代简洁风格，表头深蓝背景白字，数据右对齐',
        list: '使用项目符号"▪"或编号，重点用"★"标记',
        quote: '客户反馈或数据来源用浅蓝背景框突出',
        code: '技术内容用等宽字体，浅灰背景',
        image: '图表居中，配色统一，下方标注数据来源'
      },
      layout: {
        margins: '页边距：上下2cm，左右2.5cm',
        alignment: '正文左对齐，数据表格居中',
        pageSetup: 'A4纸张，可考虑横向排版（图表较多时）',
        headerFooter: '页眉：公司Logo+报告标题，页脚：页码+机密标识'
      }
    },
    prompt: `请按照商务报告格式标准优化这份文档，具体要求：

**标题格式：**
- 一级标题：# 使用二号微软雅黑加粗，居中，蓝色
- 二级标题：## 使用三号微软雅黑加粗，左对齐，深蓝色
- 三级标题：### 使用四号微软雅黑，左对齐
- 使用数字编号体系：1. 1.1 1.1.1

**正文格式：**
- 使用小四号微软雅黑，行间距1.25倍
- 段落简洁明了，每段3-5行
- 关键数据用**加粗**并突出显示
- 数字与中文间空1格，如"增长 25%"

**商务特色：**
- 重要数据用**加粗+突出色**标注
- 表格使用现代简洁风格，表头深色背景
- 使用项目符号"▪"，重点用"★"标记
- 客户反馈或重要信息用背景框突出

**专业要求：**
- 格式统一专业，体现商务正式感
- 数据图表清晰易读，配色协调
- 重点信息突出，便于快速浏览
- 符合企业文档规范

请优化后说明主要修改内容和商务化改进。`
  },

  {
    id: 'resume',
    name: '简历文档',
    description: '简洁专业的简历格式',
    icon: '📄',
    rules: {
      title: {
        h1: '姓名：一号微软雅黑加粗，居中',
        h2: '模块标题：四号微软雅黑加粗，左对齐，下划线',
        h3: '子项目：小四号微软雅黑加粗',
        h4: '详细信息：五号微软雅黑',
        numbering: '避免使用编号，用分隔线区分模块',
        spacing: '模块间空1.5行，内容紧凑'
      },
      content: {
        font: '正文：小四号微软雅黑，行间距1.15倍',
        spacing: '信息密度适中，避免过于拥挤',
        paragraph: '每项经历2-3行，突出关键成果',
        emphasis: '成果数据用【加粗】，技能用【标签样式】',
        mixedText: '时间格式统一：2020.01-2023.12'
      },
      special: {
        table: '技能表格简洁，无边框，左对齐',
        list: '使用"•"符号，成果用"✓"标记',
        quote: '自我评价用斜体，简洁有力',
        code: '技术技能用标签形式展示',
        image: '头像圆形，右上角位置，适当大小'
      },
      layout: {
        margins: '页边距：上下1.5cm，左右2cm',
        alignment: '左对齐为主，姓名居中',
        pageSetup: 'A4纸张，尽量控制在1-2页',
        headerFooter: '页脚：联系方式，页码（多页时）'
      }
    },
    prompt: `请按照简历文档格式标准优化这份文档，具体要求：

**个人信息：**
- 姓名使用一号微软雅黑加粗，居中显示
- 联系方式紧凑排列，使用统一格式
- 避免过多装饰，保持专业简洁

**模块结构：**
- 模块标题用## 四号微软雅黑加粗，可加下划线
- 模块顺序：个人信息→教育背景→工作经历→项目经验→技能特长
- 模块间空1.5行，内容紧凑有序

**内容格式：**
- 时间格式统一：2020.01-2023.12
- 每项经历2-3行，突出关键成果和数据
- 成果数据用**加粗**突出
- 使用"•"列举要点，"✓"标记成就

**专业要求：**
- 信息密度适中，重点突出
- 技能用标签形式展示
- 避免冗余信息，突出核心竞争力
- 整体布局美观，易于阅读

**检查要点：**
- 确保信息准确完整
- 格式统一专业
- 重点信息突出
- 控制在1-2页内

请优化后说明主要调整内容。`
  }
];

// 获取格式标准
export const getFormatStandard = (id: string): FormatStandard | undefined => {
  return formatStandards.find(standard => standard.id === id);
};

// 获取所有格式标准
export const getAllFormatStandards = (): FormatStandard[] => {
  return formatStandards;
};

// 格式检查规则
export const formatCheckRules = {
  title: {
    consistency: '检查标题层级是否一致',
    numbering: '检查编号格式是否统一',
    spacing: '检查标题间距是否合适'
  },
  content: {
    font: '检查字体字号是否统一',
    spacing: '检查行间距段间距是否合适',
    alignment: '检查对齐方式是否正确'
  },
  special: {
    table: '检查表格格式是否规范',
    list: '检查列表格式是否一致',
    emphasis: '检查重点标记是否突出'
  }
};
