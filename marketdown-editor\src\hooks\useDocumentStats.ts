'use client';

import { useMemo } from 'react';

export interface DocumentStats {
  // 基础统计
  characters: number;
  charactersNoSpaces: number;
  words: number;
  sentences: number;
  paragraphs: number;
  lines: number;
  
  // 阅读统计
  readingTime: number; // 分钟
  speakingTime: number; // 分钟
  
  // Markdown特定统计
  headings: {
    h1: number;
    h2: number;
    h3: number;
    h4: number;
    h5: number;
    h6: number;
    total: number;
  };
  
  lists: {
    ordered: number;
    unordered: number;
    total: number;
  };
  
  links: number;
  images: number;
  codeBlocks: number;
  inlineCode: number;
  tables: number;
  blockquotes: number;
  
  // 复杂度分析
  complexity: {
    score: number; // 0-100
    level: 'simple' | 'moderate' | 'complex';
    factors: string[];
  };
  
  // 语言分析
  language: {
    detected: string;
    confidence: number;
  };
  
  // 结构分析
  structure: {
    hasTableOfContents: boolean;
    maxHeadingDepth: number;
    averageParagraphLength: number;
    averageSentenceLength: number;
  };
}

export const useDocumentStats = (content: string): DocumentStats => {
  return useMemo(() => {
    if (!content.trim()) {
      return getEmptyStats();
    }

    // 基础统计
    const characters = content.length;
    const charactersNoSpaces = content.replace(/\s/g, '').length;
    const words = content.trim() ? content.trim().split(/\s+/).length : 0;
    const lines = content.split('\n').length;
    
    // 句子统计（简单实现）
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    
    // 段落统计
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
    
    // 阅读时间（假设每分钟200词）
    const readingTime = Math.ceil(words / 200);
    
    // 演讲时间（假设每分钟150词）
    const speakingTime = Math.ceil(words / 150);
    
    // Markdown元素统计
    const headings = countHeadings(content);
    const lists = countLists(content);
    const links = countLinks(content);
    const images = countImages(content);
    const codeBlocks = countCodeBlocks(content);
    const inlineCode = countInlineCode(content);
    const tables = countTables(content);
    const blockquotes = countBlockquotes(content);
    
    // 复杂度分析
    const complexity = analyzeComplexity(content, {
      words,
      sentences,
      headings: headings.total,
      lists: lists.total,
      codeBlocks,
      tables,
    });
    
    // 语言检测（简单实现）
    const language = detectLanguage(content);
    
    // 结构分析
    const structure = analyzeStructure(content, headings, paragraphs, sentences, words);

    return {
      characters,
      charactersNoSpaces,
      words,
      sentences,
      paragraphs,
      lines,
      readingTime,
      speakingTime,
      headings,
      lists,
      links,
      images,
      codeBlocks,
      inlineCode,
      tables,
      blockquotes,
      complexity,
      language,
      structure,
    };
  }, [content]);
};

function getEmptyStats(): DocumentStats {
  return {
    characters: 0,
    charactersNoSpaces: 0,
    words: 0,
    sentences: 0,
    paragraphs: 0,
    lines: 0,
    readingTime: 0,
    speakingTime: 0,
    headings: { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0, total: 0 },
    lists: { ordered: 0, unordered: 0, total: 0 },
    links: 0,
    images: 0,
    codeBlocks: 0,
    inlineCode: 0,
    tables: 0,
    blockquotes: 0,
    complexity: { score: 0, level: 'simple', factors: [] },
    language: { detected: 'unknown', confidence: 0 },
    structure: {
      hasTableOfContents: false,
      maxHeadingDepth: 0,
      averageParagraphLength: 0,
      averageSentenceLength: 0,
    },
  };
}

function countHeadings(content: string) {
  const h1 = (content.match(/^# /gm) || []).length;
  const h2 = (content.match(/^## /gm) || []).length;
  const h3 = (content.match(/^### /gm) || []).length;
  const h4 = (content.match(/^#### /gm) || []).length;
  const h5 = (content.match(/^##### /gm) || []).length;
  const h6 = (content.match(/^###### /gm) || []).length;
  
  return {
    h1, h2, h3, h4, h5, h6,
    total: h1 + h2 + h3 + h4 + h5 + h6,
  };
}

function countLists(content: string) {
  const unordered = (content.match(/^[\s]*[-*+] /gm) || []).length;
  const ordered = (content.match(/^[\s]*\d+\. /gm) || []).length;
  
  return {
    ordered,
    unordered,
    total: ordered + unordered,
  };
}

function countLinks(content: string) {
  return (content.match(/\[.*?\]\(.*?\)/g) || []).length;
}

function countImages(content: string) {
  return (content.match(/!\[.*?\]\(.*?\)/g) || []).length;
}

function countCodeBlocks(content: string) {
  return (content.match(/```[\s\S]*?```/g) || []).length;
}

function countInlineCode(content: string) {
  // 排除代码块中的内联代码
  const withoutCodeBlocks = content.replace(/```[\s\S]*?```/g, '');
  return (withoutCodeBlocks.match(/`[^`]+`/g) || []).length;
}

function countTables(content: string) {
  return (content.match(/\|.*\|/g) || []).length > 0 ? 
    (content.match(/\n\|.*\|\n/g) || []).length : 0;
}

function countBlockquotes(content: string) {
  return (content.match(/^> /gm) || []).length;
}

function analyzeComplexity(content: string, stats: any) {
  const factors: string[] = [];
  let score = 0;
  
  // 基于各种因素计算复杂度
  if (stats.words > 1000) {
    factors.push('长文档');
    score += 20;
  }
  
  if (stats.sentences / stats.words > 0.1) {
    factors.push('句子较短');
    score += 10;
  } else if (stats.sentences / stats.words < 0.05) {
    factors.push('句子较长');
    score += 15;
  }
  
  if (stats.headings > 10) {
    factors.push('结构复杂');
    score += 15;
  }
  
  if (stats.codeBlocks > 5) {
    factors.push('包含大量代码');
    score += 20;
  }
  
  if (stats.tables > 3) {
    factors.push('包含多个表格');
    score += 10;
  }
  
  if (stats.lists > 10) {
    factors.push('包含大量列表');
    score += 10;
  }
  
  // 技术术语检测（简单实现）
  const technicalTerms = content.match(/\b(API|HTTP|JSON|XML|SQL|CSS|HTML|JavaScript|Python|React|Vue|Angular)\b/gi);
  if (technicalTerms && technicalTerms.length > 10) {
    factors.push('技术文档');
    score += 15;
  }
  
  let level: 'simple' | 'moderate' | 'complex';
  if (score < 30) {
    level = 'simple';
  } else if (score < 60) {
    level = 'moderate';
  } else {
    level = 'complex';
  }
  
  return {
    score: Math.min(100, score),
    level,
    factors,
  };
}

function detectLanguage(content: string) {
  // 简单的语言检测
  const chineseChars = content.match(/[\u4e00-\u9fff]/g);
  const englishWords = content.match(/\b[a-zA-Z]+\b/g);
  
  const chineseRatio = chineseChars ? chineseChars.length / content.length : 0;
  const englishRatio = englishWords ? englishWords.join('').length / content.length : 0;
  
  if (chineseRatio > 0.3) {
    return { detected: 'zh-CN', confidence: chineseRatio };
  } else if (englishRatio > 0.5) {
    return { detected: 'en', confidence: englishRatio };
  } else {
    return { detected: 'mixed', confidence: Math.max(chineseRatio, englishRatio) };
  }
}

function analyzeStructure(content: string, headings: any, paragraphs: number, sentences: number, words: number) {
  // 检查是否有目录结构
  const hasTableOfContents = /目录|Table of Contents|TOC/i.test(content) || 
                            (headings.h1 > 0 && headings.h2 > 2);
  
  // 最大标题深度
  let maxHeadingDepth = 0;
  if (headings.h6 > 0) maxHeadingDepth = 6;
  else if (headings.h5 > 0) maxHeadingDepth = 5;
  else if (headings.h4 > 0) maxHeadingDepth = 4;
  else if (headings.h3 > 0) maxHeadingDepth = 3;
  else if (headings.h2 > 0) maxHeadingDepth = 2;
  else if (headings.h1 > 0) maxHeadingDepth = 1;
  
  // 平均段落长度
  const averageParagraphLength = paragraphs > 0 ? Math.round(words / paragraphs) : 0;
  
  // 平均句子长度
  const averageSentenceLength = sentences > 0 ? Math.round(words / sentences) : 0;
  
  return {
    hasTableOfContents,
    maxHeadingDepth,
    averageParagraphLength,
    averageSentenceLength,
  };
}
