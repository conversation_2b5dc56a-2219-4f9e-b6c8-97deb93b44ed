'use client';

import React, { useState, useEffect } from 'react';
import { ClipboardUtils } from '@/utils/clipboard';

interface CopyButtonProps {
  content: string;
  theme?: 'professional' | 'modern' | 'minimal' | 'colorful';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
}

export const CopyButton: React.FC<CopyButtonProps> = ({
  content,
  theme = 'modern',
  className = '',
  size = 'md',
  variant = 'primary'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [copyStatus, setCopyStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isSupported, setIsSupported] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 只在客户端检查剪贴板支持
  useEffect(() => {
    setIsMounted(true);
    setIsSupported(ClipboardUtils.isClipboardSupported());
  }, []);

  const handleCopy = async () => {
    if (!content.trim()) {
      setCopyStatus('error');
      setTimeout(() => setCopyStatus('idle'), 2000);
      return;
    }

    setIsLoading(true);
    setCopyStatus('idle');

    try {
      const success = await ClipboardUtils.copyToClipboard(content, theme);
      
      if (success) {
        setCopyStatus('success');
        setTimeout(() => setCopyStatus('idle'), 3000);
      } else {
        setCopyStatus('error');
        setTimeout(() => setCopyStatus('idle'), 2000);
      }
    } catch (error) {
      console.error('复制失败:', error);
      setCopyStatus('error');
      setTimeout(() => setCopyStatus('idle'), 2000);
    } finally {
      setIsLoading(false);
    }
  };

  // 尺寸样式
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  // 变体样式
  const variantClasses = {
    primary: 'bg-blue-500 hover:bg-blue-600 text-white',
    secondary: 'bg-gray-500 hover:bg-gray-600 text-white',
    outline: 'border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white bg-transparent'
  };

  // 状态样式
  const getStatusClasses = () => {
    switch (copyStatus) {
      case 'success':
        return 'bg-green-500 hover:bg-green-600 text-white';
      case 'error':
        return 'bg-red-500 hover:bg-red-600 text-white';
      default:
        return variantClasses[variant];
    }
  };

  // 图标
  const getIcon = () => {
    if (isLoading) {
      return (
        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }

    switch (copyStatus) {
      case 'success':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        );
    }
  };

  // 按钮文字
  const getButtonText = () => {
    if (isLoading) return '复制中...';
    
    switch (copyStatus) {
      case 'success':
        return '复制成功！';
      case 'error':
        return '复制失败';
      default:
        return '复制到Word';
    }
  };

  // 在服务器端渲染时显示加载状态，避免hydration不匹配
  if (!isMounted) {
    return (
      <button
        disabled
        className={`
          inline-flex items-center gap-2 rounded-md font-medium transition-all duration-200
          ${sizeClasses[size]}
          ${variantClasses[variant]}
          cursor-not-allowed opacity-70
          ${className}
        `}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        <span>复制到Word</span>
      </button>
    );
  }

  if (!isSupported) {
    return (
      <div className={`inline-flex items-center gap-2 px-4 py-2 text-sm text-gray-500 bg-gray-100 rounded-md cursor-not-allowed ${className}`}>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
        </svg>
        浏览器不支持
      </div>
    );
  }

  return (
    <button
      onClick={handleCopy}
      disabled={isLoading}
      className={`
        inline-flex items-center gap-2 rounded-md font-medium transition-all duration-200
        ${sizeClasses[size]}
        ${getStatusClasses()}
        ${isLoading ? 'cursor-not-allowed opacity-70' : 'hover:shadow-md active:scale-95'}
        ${className}
      `}
      title="复制带样式的内容到Word"
    >
      {getIcon()}
      <span>{getButtonText()}</span>
    </button>
  );
};

// 快速复制组件 - 用于工具栏
export const QuickCopyButton: React.FC<{
  content: string;
  theme?: string;
  className?: string;
}> = ({ content, theme = 'modern', className = '' }) => {
  const [copied, setCopied] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleQuickCopy = async () => {
    if (!isMounted) return;

    try {
      const success = await ClipboardUtils.copyToClipboard(content, theme);
      if (success) {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    } catch (error) {
      console.error('快速复制失败:', error);
    }
  };

  // 在服务器端渲染时显示默认状态
  if (!isMounted) {
    return (
      <button
        disabled
        className={`
          p-2 rounded-md transition-colors
          bg-gray-100 text-gray-600 cursor-not-allowed opacity-70
          ${className}
        `}
        title="复制到Word"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      </button>
    );
  }

  return (
    <button
      onClick={handleQuickCopy}
      className={`
        p-2 rounded-md transition-colors
        ${copied
          ? 'bg-green-100 text-green-600 hover:bg-green-200'
          : 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800'
        }
        ${className}
      `}
      title={copied ? '已复制！' : '复制到Word'}
    >
      {copied ? (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      ) : (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      )}
    </button>
  );
};
