'use client';

import React, { useCallback, useEffect, useRef } from 'react';
import { useEditorStore } from '@/stores/useEditorStore';
import { debounce } from '@/utils';

interface MarkdownEditorProps {
  className?: string;
}

export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({ className = '' }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const {
    currentDocument,
    editorConfig,
    updateCurrentDocument,
    setError,
  } = useEditorStore();

  // 防抖的内容更新函数
  const debouncedUpdate = useCallback(
    debounce((content: string) => {
      updateCurrentDocument({ content });
    }, 300),
    [updateCurrentDocument]
  );

  const handleContentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    debouncedUpdate(content);
  }, [debouncedUpdate]);

  // 处理键盘快捷键
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const { ctrlKey, metaKey, key } = e;
    const isCmd = ctrlKey || metaKey;

    if (isCmd) {
      switch (key) {
        case 'b':
          e.preventDefault();
          insertMarkdown('**', '**');
          break;
        case 'i':
          e.preventDefault();
          insertMarkdown('*', '*');
          break;
        case 'k':
          e.preventDefault();
          insertMarkdown('[', '](url)');
          break;
        default:
          break;
      }
    }

    // Tab键处理
    if (key === 'Tab') {
      e.preventDefault();
      insertTab();
    }
  }, []);

  const insertMarkdown = useCallback((before: string, after: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const beforeText = textarea.value.substring(0, start);
    const afterText = textarea.value.substring(end);

    const newText = beforeText + before + selectedText + after + afterText;
    textarea.value = newText;

    // 设置新的光标位置
    const newCursorPos = start + before.length + selectedText.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    
    // 触发change事件
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.focus();
  }, []);

  const insertTab = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const beforeText = textarea.value.substring(0, start);
    const afterText = textarea.value.substring(end);

    textarea.value = beforeText + '  ' + afterText;
    textarea.setSelectionRange(start + 2, start + 2);
    
    // 触发change事件
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
  }, []);

  // 自动调整高度
  const adjustHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, []);

  useEffect(() => {
    adjustHeight();
  }, [currentDocument?.content, adjustHeight]);

  if (!currentDocument) {
    return (
      <div className={`flex items-center justify-center h-full text-gray-500 ${className}`}>
        <div className="text-center">
          <p className="text-lg mb-2">No document selected</p>
          <p className="text-sm">Create a new document to start editing</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <div className="flex-1 p-4">
        <textarea
          ref={textareaRef}
          defaultValue={currentDocument.content}
          onChange={handleContentChange}
          onKeyDown={handleKeyDown}
          onInput={adjustHeight}
          placeholder="Start writing your markdown..."
          className={`
            w-full h-full min-h-[500px] resize-none border-none outline-none
            bg-transparent text-gray-900 dark:text-gray-100
            placeholder-gray-400 dark:placeholder-gray-500
            font-mono leading-relaxed
          `}
          style={{
            fontSize: `${editorConfig.fontSize}px`,
            fontFamily: editorConfig.fontFamily,
            lineHeight: editorConfig.lineHeight,
            whiteSpace: editorConfig.wordWrap ? 'pre-wrap' : 'pre',
          }}
          spellCheck={false}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
        />
      </div>
    </div>
  );
};
