"""
核心功能测试
"""

import os
import tempfile
import pytest
from pathlib import Path

from document_converter.core import DocumentConverter
from document_converter.exceptions import ConversionError, UnsupportedFormatError, FileNotFoundError


class TestDocumentConverter:
    """文档转换器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.converter = DocumentConverter()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_file(self, filename: str, content: str) -> str:
        """创建测试文件"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_get_supported_formats(self):
        """测试获取支持的格式"""
        formats = self.converter.get_supported_formats()
        expected_formats = ['pdf', 'docx', 'xlsx', 'txt', 'md']
        
        assert isinstance(formats, list)
        assert all(fmt in formats for fmt in expected_formats)
    
    def test_validate_conversion(self):
        """测试转换验证"""
        # 支持的转换
        assert self.converter.validate_conversion('md', 'pdf') == True
        assert self.converter.validate_conversion('txt', 'docx') == True
        
        # 不支持的转换
        assert self.converter.validate_conversion('unknown', 'pdf') == False
        assert self.converter.validate_conversion('md', 'unknown') == False
    
    def test_convert_txt_to_md(self):
        """测试文本转Markdown"""
        # 创建测试文本文件
        txt_content = """第一章 介绍
        
这是一个测试文档。

第二章 内容

这里是具体内容。
        """
        
        txt_file = self.create_test_file('test.txt', txt_content)
        md_file = os.path.join(self.temp_dir, 'test.md')
        
        # 执行转换
        success = self.converter.convert(txt_file, md_file)
        
        assert success == True
        assert os.path.exists(md_file)
        
        # 检查转换结果
        with open(md_file, 'r', encoding='utf-8') as f:
            result = f.read()
        
        assert '# 第一章 介绍' in result
        assert '# 第二章 内容' in result
    
    def test_convert_md_to_txt(self):
        """测试Markdown转文本"""
        # 创建测试Markdown文件
        md_content = """# 标题一

这是段落内容。

## 标题二

- 列表项1
- 列表项2

**粗体文本**
        """
        
        md_file = self.create_test_file('test.md', md_content)
        txt_file = os.path.join(self.temp_dir, 'test.txt')
        
        # 执行转换
        success = self.converter.convert(md_file, txt_file)
        
        assert success == True
        assert os.path.exists(txt_file)
        
        # 检查转换结果
        with open(txt_file, 'r', encoding='utf-8') as f:
            result = f.read()
        
        assert '标题一' in result
        assert '标题二' in result
        assert '列表项1' in result
        assert '粗体文本' in result
    
    def test_file_not_found_error(self):
        """测试文件不存在错误"""
        with pytest.raises(Exception):
            self.converter.convert('nonexistent.txt', 'output.md')
    
    def test_batch_convert(self):
        """测试批量转换"""
        # 创建多个测试文件
        files = []
        for i in range(3):
            content = f"# 文档 {i+1}\n\n这是第{i+1}个测试文档。"
            file_path = self.create_test_file(f'test_{i+1}.md', content)
            files.append(file_path)
        
        # 批量转换
        results = self.converter.batch_convert(files, self.temp_dir, 'txt')
        
        # 检查结果
        assert len(results) == 3
        assert all(success for success in results.values())
        
        # 检查输出文件
        for i in range(3):
            output_file = os.path.join(self.temp_dir, f'test_{i+1}.txt')
            assert os.path.exists(output_file)
    
    def test_get_file_info(self):
        """测试获取文件信息"""
        # 创建测试文件
        content = "测试内容"
        test_file = self.create_test_file('test.txt', content)
        
        # 获取文件信息
        info = self.converter.get_file_info(test_file)
        
        assert info['format'] == 'txt'
        assert info['name'] == 'test.txt'
        assert info['size'] > 0
        assert 'encoding' in info
