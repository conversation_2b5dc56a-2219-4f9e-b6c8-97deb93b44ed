'use client';

import { useState } from 'react';
import { CopyButton, QuickCopyButton } from '@/components/ui/CopyButton';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const sampleContent = `# 复制功能测试文档

## 基本表格

| 姓名 | 年龄 | 职业 | 城市 |
|------|------|------|------|
| 张三 | 25 | 前端工程师 | 北京 |
| 李四 | 30 | 产品经理 | 上海 |
| 王五 | 28 | UI设计师 | 深圳 |

## 复杂表格

| 项目名称 | 开始时间 | 结束时间 | 负责人 | 状态 | 预算 |
|----------|----------|----------|--------|------|------|
| **网站重构** | 2024-01-01 | 2024-03-31 | 张三 | *进行中* | ¥50,000 |
| **移动应用** | 2024-02-15 | 2024-06-30 | 李四 | *计划中* | ¥80,000 |
| **数据平台** | 2024-01-15 | 2024-05-15 | 王五 | *已完成* | ¥120,000 |

## 其他内容

### 代码示例
\`\`\`javascript
function copyToWord() {
  console.log('复制到Word成功！');
}
\`\`\`

### 列表
1. 第一项内容
2. 第二项内容
3. 第三项内容

- 无序列表项1
- 无序列表项2
- 无序列表项3

### 格式化文本
这是一段包含 **粗体文字**、*斜体文字* 和 \`行内代码\` 的段落。
`;

export default function TestCopy() {
  const [content, setContent] = useState(sampleContent);
  const [selectedTheme, setSelectedTheme] = useState<'professional' | 'modern' | 'minimal' | 'colorful'>('modern');

  const themes = [
    { value: 'professional', label: '专业', color: '#4a5568' },
    { value: 'modern', label: '现代', color: '#667eea' },
    { value: 'minimal', label: '简约', color: '#a0aec0' },
    { value: 'colorful', label: '彩色', color: '#f56565' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            📋 复制到Word功能测试
          </h1>
          
          {/* 功能说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-800 mb-2">🎯 功能特点</h2>
            <ul className="text-blue-700 space-y-1">
              <li>• <strong>保留样式</strong>：复制时保留表格格式、颜色和样式</li>
              <li>• <strong>多种主题</strong>：支持4种不同的表格主题风格</li>
              <li>• <strong>完整内容</strong>：包括标题、段落、表格、列表等所有内容</li>
              <li>• <strong>Word兼容</strong>：直接粘贴到Word中，格式完美保留</li>
            </ul>
          </div>

          {/* 主题选择 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              选择表格主题
            </h3>
            <div className="flex gap-3">
              {themes.map((theme) => (
                <button
                  key={theme.value}
                  onClick={() => setSelectedTheme(theme.value as any)}
                  className={`px-4 py-2 rounded-md border-2 transition-colors ${
                    selectedTheme === theme.value
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: theme.color }}
                    ></div>
                    {theme.label}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 复制按钮区域 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              复制操作
            </h3>
            <div className="flex flex-wrap gap-3">
              <CopyButton 
                content={content}
                theme={selectedTheme}
                size="lg"
                variant="primary"
              />
              
              <CopyButton 
                content={content}
                theme={selectedTheme}
                size="md"
                variant="outline"
                className="border-green-500 text-green-600 hover:bg-green-500 hover:text-white"
              />
              
              <QuickCopyButton 
                content={content}
                theme={selectedTheme}
                className="bg-purple-100 hover:bg-purple-200 text-purple-600"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 编辑器 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Markdown 编辑器
            </h2>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 font-mono text-sm resize-none"
              placeholder="输入Markdown内容..."
            />
          </div>

          {/* 预览 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              预览效果
            </h2>
            <div className="h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-700">
              <div className="prose dark:prose-invert max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            📖 使用说明
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-white">1️⃣ 复制步骤</h3>
              <ol className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>1. 选择喜欢的表格主题</li>
                <li>2. 点击"复制到Word"按钮</li>
                <li>3. 等待复制成功提示</li>
                <li>4. 打开Word文档</li>
                <li>5. 按Ctrl+V粘贴</li>
              </ol>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-white">2️⃣ 支持内容</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• 表格（带样式）</li>
                <li>• 标题（H1-H6）</li>
                <li>• 段落文本</li>
                <li>• 粗体、斜体</li>
                <li>• 行内代码</li>
                <li>• 列表项目</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-white">3️⃣ 注意事项</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• 需要现代浏览器支持</li>
                <li>• 确保允许剪贴板访问</li>
                <li>• Word 2016+版本最佳</li>
                <li>• 复杂表格可能需要微调</li>
                <li>• 支持Office 365在线版</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 技术说明 */}
        <div className="mt-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            🔧 技术实现
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">核心技术</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• <strong>Clipboard API</strong>：现代剪贴板接口</li>
                <li>• <strong>HTML格式</strong>：保留样式的富文本</li>
                <li>• <strong>CSS内联样式</strong>：确保兼容性</li>
                <li>• <strong>主题系统</strong>：动态样式生成</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">兼容性</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• <strong>Chrome 76+</strong>：完全支持</li>
                <li>• <strong>Firefox 127+</strong>：完全支持</li>
                <li>• <strong>Safari 13.1+</strong>：完全支持</li>
                <li>• <strong>Edge 79+</strong>：完全支持</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
