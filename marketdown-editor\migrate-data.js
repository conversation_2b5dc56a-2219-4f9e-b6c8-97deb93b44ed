// 数据迁移脚本
// 这个脚本会从IndexedDB获取数据并通过API迁移到MySQL

const API_BASE = 'http://localhost:3000/api/database';

// 模拟IndexedDB数据（你可以从浏览器控制台获取真实数据）
const mockData = {
  aiModels: [
    {
      name: 'OpenAI GPT-3.5 Turbo',
      api_key: 'your-api-key-here',
      base_url: 'https://api.openai.com/v1',
      model_name: 'gpt-3.5-turbo',
      model_type: 'text',
      temperature: 0.7,
      max_tokens: 2000,
      description: 'OpenAI官方GPT-3.5 Turbo模型，性价比高',
      is_default: true,
    },
    {
      name: 'OpenAI GPT-4',
      api_key: 'your-api-key-here',
      base_url: 'https://api.openai.com/v1',
      model_name: 'gpt-4',
      model_type: 'text',
      temperature: 0.7,
      max_tokens: 2000,
      description: 'OpenAI最新GPT-4模型，能力更强',
      is_default: false,
    }
  ],
  settings: {
    theme: 'light',
    language: 'zh-CN',
    'auto-save': true,
    'font-size': 14
  }
};

// 检查MySQL连接
async function checkConnection() {
  try {
    console.log('🔍 检查MySQL连接...');
    const response = await fetch(`${API_BASE}?action=check`);
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ MySQL连接成功');
      console.log('📊 连接信息:', result.config);
      return true;
    } else {
      console.log('❌ MySQL连接失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('❌ 连接检查失败:', error.message);
    return false;
  }
}

// 初始化数据库
async function initDatabase() {
  try {
    console.log('🔧 初始化MySQL数据库...');
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'init' })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 数据库初始化完成');
      if (result.defaultModelsAdded) {
        console.log('📝 已添加默认AI模型');
      }
      return true;
    } else {
      console.log('❌ 数据库初始化失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('❌ 初始化失败:', error.message);
    return false;
  }
}

// 执行数据迁移
async function migrateData() {
  try {
    console.log('📦 开始数据迁移...');
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        action: 'migrate',
        data: mockData
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 数据迁移完成!');
      console.log('📊 迁移结果:');
      console.log(`   AI模型: 成功 ${result.result.aiModels.success}, 失败 ${result.result.aiModels.failed}`);
      console.log(`   设置: 成功 ${result.result.settings.success}, 失败 ${result.result.settings.failed}`);
      return true;
    } else {
      console.log('❌ 数据迁移失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('❌ 迁移失败:', error.message);
    return false;
  }
}

// 获取迁移后的数据
async function getModels() {
  try {
    console.log('📋 获取MySQL中的AI模型...');
    const response = await fetch(`${API_BASE}?action=models`);
    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ 找到 ${result.data.length} 个AI模型:`);
      result.data.forEach((model, index) => {
        console.log(`   ${index + 1}. ${model.name} (${model.model_name}) ${model.is_default ? '[默认]' : ''}`);
      });
      return result.data;
    } else {
      console.log('❌ 获取模型失败:', result.message);
      return [];
    }
  } catch (error) {
    console.log('❌ 获取失败:', error.message);
    return [];
  }
}

// 主函数
async function main() {
  console.log('🚀 开始MySQL数据迁移流程...\n');
  
  // 1. 检查连接
  const connected = await checkConnection();
  if (!connected) {
    console.log('\n❌ MySQL连接失败，请检查配置后重试');
    return;
  }
  
  console.log('');
  
  // 2. 初始化数据库
  const initialized = await initDatabase();
  if (!initialized) {
    console.log('\n❌ 数据库初始化失败');
    return;
  }
  
  console.log('');
  
  // 3. 执行迁移
  const migrated = await migrateData();
  if (!migrated) {
    console.log('\n❌ 数据迁移失败');
    return;
  }
  
  console.log('');
  
  // 4. 验证结果
  await getModels();
  
  console.log('\n🎉 数据迁移流程完成!');
  console.log('💡 提示: 你现在可以在应用中切换到MySQL数据库了');
}

// 运行脚本
if (typeof window === 'undefined') {
  // Node.js环境
  const fetch = require('node-fetch');
  main().catch(console.error);
} else {
  // 浏览器环境
  main().catch(console.error);
}

// 导出函数供浏览器使用
if (typeof window !== 'undefined') {
  window.migrateToMySQL = main;
  window.checkMySQLConnection = checkConnection;
  window.initMySQLDatabase = initDatabase;
  window.getMySQLModels = getModels;
}
