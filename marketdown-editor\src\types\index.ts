// 文档相关类型
export interface Document {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
}

// 编辑器配置类型
export interface EditorConfig {
  theme: 'light' | 'dark';
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  wordWrap: boolean;
  showLineNumbers: boolean;
  autoSave: boolean;
  autoSaveInterval: number; // 秒
}

// 导出配置类型
export interface ExportConfig {
  format: 'pdf' | 'docx' | 'html' | 'txt';
  pageSize?: 'A4' | 'Letter' | 'A3';
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  includeTableOfContents?: boolean;
  customStyles?: string;
}

// AI功能类型
export interface AIConfig {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

export interface AIRequest {
  type: 'grammar' | 'style' | 'expand' | 'summarize' | 'translate';
  content: string;
  options?: Record<string, any>;
}

export interface AIResponse {
  success: boolean;
  result?: string;
  error?: string;
  suggestions?: string[];
}

// 文件管理类型
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  lastModified: Date;
  type: 'markdown' | 'text';
}

// 应用状态类型
export interface AppState {
  currentDocument: Document | null;
  documents: Document[];
  editorConfig: EditorConfig;
  isLoading: boolean;
  error: string | null;
}
