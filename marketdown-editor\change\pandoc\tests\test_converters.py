"""
转换器测试
"""

import os
import tempfile
import pytest

from document_converter.converters.md_converter import MarkdownConverter
from document_converter.converters.txt_converter import TxtConverter


class TestMarkdownConverter:
    """Markdown转换器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.converter = MarkdownConverter()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_file(self, filename: str, content: str) -> str:
        """创建测试文件"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_supported_formats(self):
        """测试支持的格式"""
        assert 'md' in self.converter.supported_input_formats
        assert 'markdown' in self.converter.supported_input_formats
        assert 'pdf' in self.converter.supported_output_formats
        assert 'docx' in self.converter.supported_output_formats
        assert 'txt' in self.converter.supported_output_formats
    
    def test_convert_to_txt(self):
        """测试Markdown转文本"""
        md_content = """# 标题

这是一个段落。

## 子标题

- 列表项1
- 列表项2

**粗体** 和 *斜体* 文本。
"""
        
        md_file = self.create_test_file('test.md', md_content)
        txt_file = os.path.join(self.temp_dir, 'output.txt')
        
        success = self.converter.convert_to_txt(md_file, txt_file)
        
        assert success == True
        assert os.path.exists(txt_file)
        
        with open(txt_file, 'r', encoding='utf-8') as f:
            result = f.read()
        
        assert '标题' in result
        assert '子标题' in result
        assert '列表项1' in result
        assert '粗体' in result
    
    def test_convert_to_xlsx(self):
        """测试Markdown转Excel（表格）"""
        md_content = """# 数据表

| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25   | 北京 |
| 李四 | 30   | 上海 |
| 王五 | 28   | 广州 |

这是表格下面的文本。
"""
        
        md_file = self.create_test_file('test.md', md_content)
        xlsx_file = os.path.join(self.temp_dir, 'output.xlsx')
        
        success = self.converter.convert_to_xlsx(md_file, xlsx_file)
        
        assert success == True
        assert os.path.exists(xlsx_file)


class TestTxtConverter:
    """文本转换器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.converter = TxtConverter()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_file(self, filename: str, content: str) -> str:
        """创建测试文件"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_supported_formats(self):
        """测试支持的格式"""
        assert 'txt' in self.converter.supported_input_formats
        assert 'pdf' in self.converter.supported_output_formats
        assert 'docx' in self.converter.supported_output_formats
        assert 'md' in self.converter.supported_output_formats
    
    def test_convert_to_md(self):
        """测试文本转Markdown"""
        txt_content = """第一章 介绍

这是介绍内容。

第二章 详细说明

这里是详细说明。

    代码示例
    print("Hello World")

普通段落继续。
"""
        
        txt_file = self.create_test_file('test.txt', txt_content)
        md_file = os.path.join(self.temp_dir, 'output.md')
        
        success = self.converter.convert_to_md(txt_file, md_file)
        
        assert success == True
        assert os.path.exists(md_file)
        
        with open(md_file, 'r', encoding='utf-8') as f:
            result = f.read()
        
        assert '# 第一章 介绍' in result
        assert '# 第二章 详细说明' in result
        assert '```' in result  # 代码块
    
    def test_detect_tables(self):
        """测试表格检测"""
        txt_content = """数据表格：

姓名	年龄	城市
张三	25	北京
李四	30	上海
王五	28	广州

表格结束。
"""
        
        txt_file = self.create_test_file('test.txt', txt_content)
        xlsx_file = os.path.join(self.temp_dir, 'output.xlsx')
        
        success = self.converter.convert_to_xlsx(txt_file, xlsx_file)
        
        assert success == True
        assert os.path.exists(xlsx_file)
    
    def test_is_likely_heading(self):
        """测试标题检测"""
        assert self.converter._is_likely_heading("第一章 介绍") == True
        assert self.converter._is_likely_heading("1. 概述") == True
        assert self.converter._is_likely_heading("CHAPTER ONE") == True
        assert self.converter._is_likely_heading("这是一个很长的段落，不应该被识别为标题。") == False
