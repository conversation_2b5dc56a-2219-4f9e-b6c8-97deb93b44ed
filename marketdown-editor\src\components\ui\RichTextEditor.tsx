'use client';

import React, { useState, useRef, useEffect } from 'react';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  className?: string;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content,
  onChange,
  className = ''
}) => {
  const [isRichMode, setIsRichMode] = useState(false);
  const [selection, setSelection] = useState<{ start: number; end: number } | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const richEditorRef = useRef<HTMLDivElement>(null);

  // 将Markdown转换为HTML用于富文本显示
  const markdownToHtml = (markdown: string): string => {
    return markdown
      // 标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 粗体和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 行内代码
      .replace(/`(.*?)`/g, '<code>$1</code>')
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
      // 列表
      .replace(/^\* (.*$)/gim, '<li>$1</li>')
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
      // 换行
      .replace(/\n/g, '<br>');
  };

  // 将HTML转换回Markdown
  const htmlToMarkdown = (html: string): string => {
    return html
      // 标题
      .replace(/<h1>(.*?)<\/h1>/g, '# $1\n')
      .replace(/<h2>(.*?)<\/h2>/g, '## $1\n')
      .replace(/<h3>(.*?)<\/h3>/g, '### $1\n')
      // 粗体和斜体
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      // 行内代码
      .replace(/<code>(.*?)<\/code>/g, '`$1`')
      // 链接
      .replace(/<a href="([^"]+)">(.*?)<\/a>/g, '[$2]($1)')
      // 列表
      .replace(/<li>(.*?)<\/li>/g, '- $1\n')
      // 换行
      .replace(/<br>/g, '\n')
      // 清理HTML标签
      .replace(/<[^>]*>/g, '');
  };

  // 格式化工具函数
  const formatText = (format: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);

    let formattedText = '';
    let newContent = '';

    switch (format) {
      case 'bold':
        formattedText = selectedText ? `**${selectedText}**` : '**粗体文字**';
        break;
      case 'italic':
        formattedText = selectedText ? `*${selectedText}*` : '*斜体文字*';
        break;
      case 'code':
        formattedText = selectedText ? `\`${selectedText}\`` : '`代码`';
        break;
      case 'codeblock':
        formattedText = selectedText ? `\`\`\`\n${selectedText}\n\`\`\`` : '```\n代码块\n```';
        break;
      case 'h1':
        formattedText = `# ${selectedText || '一级标题'}`;
        break;
      case 'h2':
        formattedText = `## ${selectedText || '二级标题'}`;
        break;
      case 'h3':
        formattedText = `### ${selectedText || '三级标题'}`;
        break;
      case 'link':
        const url = prompt('请输入链接地址:');
        if (url) {
          formattedText = `[${selectedText || '链接文字'}](${url})`;
        } else {
          return;
        }
        break;
      case 'image':
        const imgUrl = prompt('请输入图片地址:');
        const imgAlt = prompt('请输入图片描述:') || '图片';
        if (imgUrl) {
          formattedText = `![${imgAlt}](${imgUrl})`;
        } else {
          return;
        }
        break;
      case 'list':
        if (selectedText) {
          formattedText = selectedText.split('\n').map(line => line.trim() ? `- ${line.trim()}` : '').join('\n');
        } else {
          formattedText = '- 列表项1\n- 列表项2\n- 列表项3';
        }
        break;
      case 'orderedlist':
        if (selectedText) {
          formattedText = selectedText.split('\n').map((line, index) =>
            line.trim() ? `${index + 1}. ${line.trim()}` : ''
          ).join('\n');
        } else {
          formattedText = '1. 列表项1\n2. 列表项2\n3. 列表项3';
        }
        break;
      case 'quote':
        if (selectedText) {
          formattedText = selectedText.split('\n').map(line => `> ${line}`).join('\n');
        } else {
          formattedText = '> 引用内容';
        }
        break;
      case 'hr':
        formattedText = '\n---\n';
        break;
      case 'strikethrough':
        formattedText = selectedText ? `~~${selectedText}~~` : '~~删除线文字~~';
        break;
      default:
        return;
    }

    newContent = content.substring(0, start) + formattedText + content.substring(end);
    onChange(newContent);

    // 恢复光标位置
    setTimeout(() => {
      if (textarea) {
        textarea.focus();
        const newPosition = start + formattedText.length;
        textarea.setSelectionRange(newPosition, newPosition);
      }
    }, 0);
  };

  // 处理键盘快捷键
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          formatText('bold');
          break;
        case 'i':
          e.preventDefault();
          formatText('italic');
          break;
        case 'k':
          e.preventDefault();
          formatText('link');
          break;
        case '`':
          e.preventDefault();
          formatText('code');
          break;
      }
    }
  };

  // 插入表格
  const insertTable = () => {
    const tableMarkdown = `
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |
`;
    
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const newContent = content.substring(0, start) + tableMarkdown + content.substring(start);
      onChange(newContent);
    }
  };

  // 工具栏按钮组件
  const ToolbarButton: React.FC<{
    onClick: () => void;
    title: string;
    icon: string;
    active?: boolean;
  }> = ({ onClick, title, icon, active = false }) => (
    <button
      onClick={onClick}
      title={title}
      className={`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${
        active ? 'bg-blue-100 dark:bg-blue-900' : ''
      }`}
    >
      <span className="text-sm">{icon}</span>
    </button>
  );

  return (
    <div className={`border border-gray-300 dark:border-gray-600 rounded-md ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        {/* 模式切换 */}
        <div className="flex items-center gap-1 mr-4">
          <button
            onClick={() => setIsRichMode(false)}
            className={`px-3 py-1 text-xs rounded ${
              !isRichMode 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
          >
            Markdown
          </button>
          <button
            onClick={() => setIsRichMode(true)}
            className={`px-3 py-1 text-xs rounded ${
              isRichMode 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
          >
            富文本
          </button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mr-2"></div>

        {/* 文本格式 */}
        <ToolbarButton onClick={() => formatText('bold')} title="粗体 (Ctrl+B)" icon="𝐁" />
        <ToolbarButton onClick={() => formatText('italic')} title="斜体 (Ctrl+I)" icon="𝐼" />
        <ToolbarButton onClick={() => formatText('strikethrough')} title="删除线" icon="S̶" />
        <ToolbarButton onClick={() => formatText('code')} title="行内代码 (Ctrl+`)" icon="</>" />

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 标题 */}
        <ToolbarButton onClick={() => formatText('h1')} title="一级标题" icon="H1" />
        <ToolbarButton onClick={() => formatText('h2')} title="二级标题" icon="H2" />
        <ToolbarButton onClick={() => formatText('h3')} title="三级标题" icon="H3" />

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 列表和引用 */}
        <ToolbarButton onClick={() => formatText('list')} title="无序列表" icon="•" />
        <ToolbarButton onClick={() => formatText('orderedlist')} title="有序列表" icon="1." />
        <ToolbarButton onClick={() => formatText('quote')} title="引用" icon="❝" />

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 插入元素 */}
        <ToolbarButton onClick={() => formatText('link')} title="链接 (Ctrl+K)" icon="🔗" />
        <ToolbarButton onClick={() => formatText('image')} title="图片" icon="🖼️" />
        <ToolbarButton onClick={() => formatText('codeblock')} title="代码块" icon="{ }" />
        <ToolbarButton onClick={insertTable} title="表格" icon="⊞" />
        <ToolbarButton onClick={() => formatText('hr')} title="分隔线" icon="—" />
      </div>

      {/* 编辑器内容 */}
      {isRichMode ? (
        // 富文本模式
        <div
          ref={richEditorRef}
          contentEditable
          className="p-4 min-h-[300px] focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          dangerouslySetInnerHTML={{ __html: markdownToHtml(content) }}
          onInput={(e) => {
            const html = e.currentTarget.innerHTML;
            const markdown = htmlToMarkdown(html);
            onChange(markdown);
          }}
          style={{
            lineHeight: '1.6',
            fontFamily: 'system-ui, -apple-system, sans-serif'
          }}
        />
      ) : (
        // Markdown模式
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-full p-4 min-h-[300px] resize-none focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono text-sm"
          placeholder="输入Markdown内容... 支持快捷键: Ctrl+B(粗体), Ctrl+I(斜体), Ctrl+K(链接)"
          style={{ lineHeight: '1.6' }}
        />
      )}

      {/* 状态栏 */}
      <div className="flex items-center justify-between px-4 py-2 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-300 dark:border-gray-600">
        <span>
          {isRichMode ? '🎨 富文本模式' : '📝 Markdown模式'} | 字符数: {content.length} | 行数: {content.split('\n').length}
        </span>
        <span>
          快捷键: Ctrl+B(粗体) Ctrl+I(斜体) Ctrl+K(链接) Ctrl+`(代码)
        </span>
      </div>
    </div>
  );
};
