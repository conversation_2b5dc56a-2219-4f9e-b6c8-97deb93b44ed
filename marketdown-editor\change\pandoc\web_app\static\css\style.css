/* 文档转换器样式 */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    background-color: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f1ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-content h6 {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 文件信息样式 */
.file-info {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 4px solid #0d6efd;
}

.format-badge {
    background-color: #0d6efd;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    display: inline-block;
}

/* 格式特定颜色 */
.format-badge.pdf { background-color: #dc3545; }
.format-badge.docx { background-color: #0d6efd; }
.format-badge.xlsx { background-color: #198754; }
.format-badge.txt { background-color: #6c757d; }
.format-badge.md { background-color: #17a2b8; }

/* 进度条样式 */
.progress {
    height: 20px;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* 结果图标动画 */
.result-icon i {
    animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 格式项目样式 */
.format-item {
    padding: 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.format-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

/* 卡片样式增强 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

/* 按钮样式增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* 错误提示样式 */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(220, 53, 69, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-area {
        padding: 30px 15px;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .format-item {
        padding: 15px;
    }
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 文件大小显示 */
.file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 转换选项样式 */
.form-check {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-top: 5px;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

/* 拖拽状态样式 */
.drag-active {
    border-color: #198754 !important;
    background-color: #d1e7dd !important;
}

/* 成功状态样式 */
.upload-success {
    border-color: #198754;
    background-color: #d1e7dd;
}

/* 错误状态样式 */
.upload-error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

/* 禁用状态样式 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
