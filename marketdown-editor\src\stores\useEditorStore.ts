import { create } from 'zustand';
import { Document, EditorConfig } from '@/types';
import { generateId } from '@/utils';

interface EditorState {
  // 文档状态
  currentDocument: Document | null;
  documents: Document[];
  
  // 编辑器配置
  editorConfig: EditorConfig;
  
  // UI状态
  isPreviewMode: boolean;
  isSplitView: boolean;
  isFullscreen: boolean;
  
  // 加载状态
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentDocument: (document: Document | null) => void;
  updateCurrentDocument: (updates: Partial<Document>) => void;
  createNewDocument: (title?: string) => void;
  saveDocument: (document: Document) => void;
  deleteDocument: (id: string) => void;
  
  setEditorConfig: (config: Partial<EditorConfig>) => void;
  
  setPreviewMode: (mode: boolean) => void;
  setSplitView: (split: boolean) => void;
  setFullscreen: (fullscreen: boolean) => void;
  
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const defaultEditorConfig: EditorConfig = {
  theme: 'light',
  fontSize: 14,
  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
  lineHeight: 1.5,
  wordWrap: true,
  showLineNumbers: false,
  autoSave: true,
  autoSaveInterval: 30,
};

export const useEditorStore = create<EditorState>((set, get) => ({
      // 初始状态
      currentDocument: null,
      documents: [],
      editorConfig: defaultEditorConfig,
      isPreviewMode: false,
      isSplitView: true,
      isFullscreen: false,
      isLoading: false,
      error: null,

      // Actions
      setCurrentDocument: (document) => set({ currentDocument: document }),

      updateCurrentDocument: (updates) => {
        const { currentDocument } = get();
        if (currentDocument) {
          const updatedDocument = {
            ...currentDocument,
            ...updates,
            updatedAt: new Date(),
          };
          set({ currentDocument: updatedDocument });
          
          // 同时更新documents数组中的文档
          const { documents } = get();
          const updatedDocuments = documents.map(doc =>
            doc.id === updatedDocument.id ? updatedDocument : doc
          );
          set({ documents: updatedDocuments });
        }
      },

      createNewDocument: (title = 'Untitled') => {
        const newDocument: Document = {
          id: generateId(),
          title,
          content: '',
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        const { documents } = get();
        set({
          currentDocument: newDocument,
          documents: [newDocument, ...documents],
        });
      },

      saveDocument: (document) => {
        const { documents } = get();
        const existingIndex = documents.findIndex(doc => doc.id === document.id);
        
        if (existingIndex >= 0) {
          const updatedDocuments = [...documents];
          updatedDocuments[existingIndex] = document;
          set({ documents: updatedDocuments });
        } else {
          set({ documents: [document, ...documents] });
        }
      },

      deleteDocument: (id) => {
        const { documents, currentDocument } = get();
        const updatedDocuments = documents.filter(doc => doc.id !== id);
        
        set({
          documents: updatedDocuments,
          currentDocument: currentDocument?.id === id ? null : currentDocument,
        });
      },

      setEditorConfig: (config) => {
        const { editorConfig } = get();
        set({ editorConfig: { ...editorConfig, ...config } });
      },

      setPreviewMode: (mode) => set({ isPreviewMode: mode }),
      setSplitView: (split) => set({ isSplitView: split }),
      setFullscreen: (fullscreen) => set({ isFullscreen: fullscreen }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
    }));
