'use client';

import React, { useState, useEffect } from 'react';
import { intelligentFormatting } from '@/utils/intelligentFormatting';

interface TOCItem {
  level: number;
  title: string;
  anchor: string;
  line: number;
}

interface TableOfContentsPageProps {
  content: string;
  onNavigate?: (line: number, anchor: string) => void;
  onClose?: () => void;
  className?: string;
}

export const TableOfContentsPage: React.FC<TableOfContentsPageProps> = ({
  content,
  onNavigate,
  onClose,
  className = ''
}) => {
  const [toc, setToc] = useState<TOCItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredToc, setFilteredToc] = useState<TOCItem[]>([]);

  // 生成目录
  useEffect(() => {
    const generatedToc = intelligentFormatting.generateTOC(content);
    setToc(generatedToc);
    setFilteredToc(generatedToc);
  }, [content]);

  // 搜索过滤
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredToc(toc);
    } else {
      const filtered = toc.filter(item =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredToc(filtered);
    }
  }, [searchTerm, toc]);

  // 处理目录项点击
  const handleTocItemClick = (item: TOCItem) => {
    if (onNavigate) {
      onNavigate(item.line, item.anchor);
    }
  };

  // 生成目录层级样式
  const getLevelStyle = (level: number) => {
    const baseIndent = (level - 1) * 20;
    const colors = {
      1: 'text-blue-700 dark:text-blue-300 font-bold text-lg',
      2: 'text-green-700 dark:text-green-300 font-semibold text-base',
      3: 'text-purple-700 dark:text-purple-300 font-medium text-sm',
      4: 'text-orange-700 dark:text-orange-300 text-sm',
      5: 'text-red-700 dark:text-red-300 text-sm',
      6: 'text-gray-700 dark:text-gray-300 text-xs'
    };
    
    return {
      paddingLeft: `${baseIndent}px`,
      className: colors[level as keyof typeof colors] || colors[6]
    };
  };

  // 获取层级图标
  const getLevelIcon = (level: number) => {
    const icons = {
      1: '📖',
      2: '📝',
      3: '📄',
      4: '📋',
      5: '📌',
      6: '🔸'
    };
    return icons[level as keyof typeof icons] || '•';
  };

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-900 ${className}`}>
      {/* 页面头部 */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            📚 文档目录
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            共 {toc.length} 个章节 • 点击跳转到对应位置
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            title="关闭目录"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* 搜索框 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索目录项..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
        {searchTerm && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            找到 {filteredToc.length} 个匹配项
          </p>
        )}
      </div>

      {/* 目录内容 */}
      <div className="flex-1 overflow-auto p-4">
        {filteredToc.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
            <div className="text-6xl mb-4">📄</div>
            <h3 className="text-lg font-medium mb-2">
              {toc.length === 0 ? '文档中没有标题' : '没有找到匹配的标题'}
            </h3>
            <p className="text-sm text-center">
              {toc.length === 0 
                ? '在文档中添加标题（# ## ### 等）来生成目录'
                : '尝试使用不同的搜索关键词'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {filteredToc.map((item, index) => {
              const levelStyle = getLevelStyle(item.level);
              const icon = getLevelIcon(item.level);
              
              return (
                <div
                  key={index}
                  onClick={() => handleTocItemClick(item)}
                  className="group cursor-pointer p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 border border-transparent hover:border-gray-200 dark:hover:border-gray-700"
                  style={{ paddingLeft: `${16 + (item.level - 1) * 20}px` }}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{icon}</span>
                    <div className="flex-1 min-w-0">
                      <div className={`${levelStyle.className} group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors`}>
                        {searchTerm ? (
                          <span dangerouslySetInnerHTML={{
                            __html: item.title.replace(
                              new RegExp(`(${searchTerm})`, 'gi'),
                              '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>'
                            )
                          }} />
                        ) : (
                          item.title
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-2">
                        <span>第 {item.line} 行</span>
                        <span>•</span>
                        <span>H{item.level} 级标题</span>
                      </div>
                    </div>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 页面底部统计 */}
      {toc.length > 0 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span>📊 目录统计</span>
              {[1, 2, 3, 4, 5, 6].map(level => {
                const count = toc.filter(item => item.level === level).length;
                return count > 0 ? (
                  <span key={level} className="flex items-center gap-1">
                    <span className="text-xs">H{level}:</span>
                    <span className="font-medium">{count}</span>
                  </span>
                ) : null;
              })}
            </div>
            <div className="text-xs">
              💡 提示：点击任意标题可跳转到对应位置
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
