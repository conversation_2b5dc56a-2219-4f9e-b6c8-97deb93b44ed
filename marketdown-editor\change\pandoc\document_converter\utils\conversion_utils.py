"""
转换相关工具函数
"""

from typing import Dict, List, Type, Optional
from ..exceptions import UnsupportedFormatError


# 支持的格式列表
SUPPORTED_FORMATS = ['pdf', 'docx', 'xlsx', 'txt', 'md']

# 转换器映射（延迟导入避免循环依赖）
_CONVERTER_MAPPING = None


def get_supported_formats() -> List[str]:
    """
    获取支持的文件格式列表
    
    Returns:
        支持的格式列表
    """
    return SUPPORTED_FORMATS.copy()


def is_format_supported(file_format: str) -> bool:
    """
    检查格式是否支持
    
    Args:
        file_format: 文件格式
        
    Returns:
        是否支持
    """
    return file_format.lower() in SUPPORTED_FORMATS


def get_converter_class(source_format: str, target_format: str):
    """
    获取对应的转换器类
    
    Args:
        source_format: 源格式
        target_format: 目标格式
        
    Returns:
        转换器类
        
    Raises:
        UnsupportedFormatError: 不支持的格式转换
    """
    global _CONVERTER_MAPPING
    
    # 延迟导入避免循环依赖
    if _CONVERTER_MAPPING is None:
        from ..converters.pdf_converter import PDFConverter
        from ..converters.docx_converter import DocxConverter
        from ..converters.xlsx_converter import XlsxConverter
        from ..converters.txt_converter import TxtConverter
        from ..converters.md_converter import MarkdownConverter
        
        _CONVERTER_MAPPING = {
            'pdf': PDFConverter,
            'docx': DocxConverter,
            'xlsx': XlsxConverter,
            'txt': TxtConverter,
            'md': MarkdownConverter
        }
    
    source_format = source_format.lower()
    target_format = target_format.lower()
    
    if not is_format_supported(source_format):
        raise UnsupportedFormatError(f"不支持的源格式: {source_format}")
    
    if not is_format_supported(target_format):
        raise UnsupportedFormatError(f"不支持的目标格式: {target_format}")
    
    # 返回源格式对应的转换器
    return _CONVERTER_MAPPING[source_format]


def get_conversion_matrix() -> Dict[str, List[str]]:
    """
    获取转换矩阵（每种格式可以转换到哪些格式）
    
    Returns:
        转换矩阵字典
    """
    matrix = {}
    for source_format in SUPPORTED_FORMATS:
        matrix[source_format] = [fmt for fmt in SUPPORTED_FORMATS if fmt != source_format]
    return matrix


def validate_conversion(source_format: str, target_format: str) -> bool:
    """
    验证转换是否可行
    
    Args:
        source_format: 源格式
        target_format: 目标格式
        
    Returns:
        是否可以转换
    """
    try:
        get_converter_class(source_format, target_format)
        return True
    except UnsupportedFormatError:
        return False


def get_output_filename(input_filename: str, target_format: str) -> str:
    """
    根据输入文件名和目标格式生成输出文件名
    
    Args:
        input_filename: 输入文件名
        target_format: 目标格式
        
    Returns:
        输出文件名
    """
    import os
    
    # 获取不带扩展名的文件名
    name_without_ext = os.path.splitext(input_filename)[0]
    
    # 添加新的扩展名
    return f"{name_without_ext}.{target_format}"
