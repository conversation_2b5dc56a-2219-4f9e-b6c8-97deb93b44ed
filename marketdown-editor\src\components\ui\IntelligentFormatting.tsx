'use client';

import React, { useState, useEffect } from 'react';
import { intelligentFormatting, DocumentStructure, FormatSuggestion } from '@/utils/intelligentFormatting';
import { aiService } from '@/utils/ai';
import { TableOfContentsPage } from './TableOfContentsPage';

interface IntelligentFormattingProps {
  content: string;
  onContentChange: (content: string) => void;
  className?: string;
}

export const IntelligentFormatting: React.FC<IntelligentFormattingProps> = ({
  content,
  onContentChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'toc' | 'suggestions' | 'analysis' | 'optimize'>('toc');
  const [documentStructure, setDocumentStructure] = useState<DocumentStructure | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isAIConfigured, setIsAIConfigured] = useState(false);
  const [fixingProgress, setFixingProgress] = useState('');
  const [fixingResults, setFixingResults] = useState<string[]>([]);
  const [showFixingProcess, setShowFixingProcess] = useState(false);
  const [detailedAnalysis, setDetailedAnalysis] = useState<any>(null);
  const [showDetailedReport, setShowDetailedReport] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [showTocPage, setShowTocPage] = useState(false);

  // 分析文档结构
  const analyzeDocument = () => {
    setIsAnalyzing(true);
    try {
      const structure = intelligentFormatting.analyzeDocumentStructure(content);
      setDocumentStructure(structure);
    } catch (error) {
      console.error('文档分析失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 插入目录
  const insertTOC = () => {
    const newContent = intelligentFormatting.insertTOCToDocument(content);
    onContentChange(newContent);
  };

  // 处理目录页面导航
  const handleTocNavigation = (line: number, anchor: string) => {
    // 关闭目录页面
    setShowTocPage(false);

    // 触发导航事件（可以通过props传递给父组件）
    if (onContentChange) {
      // 这里可以添加滚动到指定行的逻辑
      console.log(`导航到第 ${line} 行，锚点: ${anchor}`);

      // 可以通过自定义事件通知父组件
      const event = new CustomEvent('tocNavigate', {
        detail: { line, anchor }
      });
      window.dispatchEvent(event);
    }
  };

  // 本地自动修复格式
  const autoFixFormatting = () => {
    const originalContent = content;
    const fixedContent = intelligentFormatting.autoFixFormatting(content);

    if (fixedContent !== originalContent) {
      onContentChange(fixedContent);
      analyzeDocument(); // 重新分析

      // 显示修复结果
      setTimeout(() => {
        alert('本地自动修复完成！已修复格式问题，请查看控制台了解详细修复信息。');
      }, 100);
    } else {
      alert('未发现需要修复的格式问题。');
    }
  };

  // AI智能自动修复
  const aiAutoFixFormatting = async (useCustomPrompt = false) => {
    setIsAnalyzing(true);
    setShowFixingProcess(true);
    setFixingResults([]);

    try {
      const originalContent = content;

      // 步骤1：分析文档
      setFixingProgress('🔍 正在分析文档结构和格式问题...');
      await new Promise(resolve => setTimeout(resolve, 800));

      // 生成本地建议用于对比
      const localSuggestions = intelligentFormatting.generateFormatSuggestions(content);
      setFixingResults(prev => [...prev, `📊 检测到 ${localSuggestions.length} 个潜在格式问题`]);

      if (localSuggestions.length > 0) {
        const problemTypes = [...new Set(localSuggestions.map(s => s.type))];
        setFixingResults(prev => [...prev, `🎯 问题类型：${problemTypes.join('、')}`]);
      }

      // 显示自定义提示信息
      if (useCustomPrompt && customPrompt.trim()) {
        setFixingResults(prev => [...prev, `💬 用户指导：${customPrompt.trim()}`]);
      }

      // 步骤2：AI分析
      setFixingProgress('🤖 AI正在深度分析文档格式...');
      await new Promise(resolve => setTimeout(resolve, 500));

      setFixingResults(prev => [...prev, '🧠 AI正在理解文档上下文和语义结构...']);

      // 步骤3：执行修复
      setFixingProgress('⚡ AI正在执行智能修复...');
      const customPromptText = useCustomPrompt && customPrompt.trim() ? customPrompt.trim() : undefined;
      const result = await intelligentFormatting.autoFixFormattingWithAI(content, customPromptText);

      // 步骤4：分析修复结果
      setFixingProgress('📝 正在分析修复结果...');
      await new Promise(resolve => setTimeout(resolve, 300));

      if (result.success && result.result !== originalContent) {
        // 使用详细的分析数据
        const analysis = result.analysis;

        setFixingResults(prev => [...prev,
          result.isAIFixed
            ? '✅ AI智能修复成功完成！'
            : '⚠️ AI修复失败，已使用本地修复',
        ]);

        if (analysis) {
          const { changedLines, addedLines, removedLines, changes } = analysis;
          const totalChanges = changedLines + addedLines + removedLines;

          setFixingResults(prev => [...prev,
            `📊 修复统计：共处理 ${totalChanges} 处变更`,
            ...(changedLines > 0 ? [`  • 修改了 ${changedLines} 行`] : []),
            ...(addedLines > 0 ? [`  • 新增了 ${addedLines} 行`] : []),
            ...(removedLines > 0 ? [`  • 删除了 ${removedLines} 行`] : [])
          ]);

          // 显示具体的修复类型
          const changeTypes = new Set();
          changes.forEach(change => {
            if (change.type === 'modified' && change.original && change.fixed) {
              // 分析修改类型
              if (change.original.match(/^#{1,6}[^\s]/) && change.fixed.match(/^#{1,6}\s/)) {
                changeTypes.add('标题格式');
              }
              if (change.original.match(/[\u4e00-\u9fff][a-zA-Z]|[a-zA-Z][\u4e00-\u9fff]/) &&
                  change.fixed.includes(' ')) {
                changeTypes.add('中英文排版');
              }
              if (change.original.match(/\s{2,}/) && !change.fixed.match(/\s{2,}/)) {
                changeTypes.add('空格规范');
              }
              if (change.original.match(/\s+$/) && !change.fixed.match(/\s+$/)) {
                changeTypes.add('行尾清理');
              }
            }
          });

          if (changeTypes.size > 0) {
            setFixingResults(prev => [...prev, `🎯 修复类型：${Array.from(changeTypes).join('、')}`]);
          }
        }

        setFixingResults(prev => [...prev, '🎉 文档格式已优化完成']);

        // 保存详细分析数据
        setDetailedAnalysis(analysis);

        onContentChange(result.result);

        // 延迟重新分析，让用户看到过程
        setTimeout(() => {
          analyzeDocument();
        }, 1000);

        // 显示最终结果
        setTimeout(() => {
          if (result.isAIFixed) {
            alert('🤖 AI智能修复完成！\n\n✨ 修复内容包括：\n• 标题格式规范化\n• 中英文排版优化\n• 空格和标点规范\n• 列表和代码块格式\n• 表格对齐优化\n\n请查看修复过程了解详细信息。');
          } else {
            const message = result.error
              ? `AI修复遇到问题：${result.error}\n\n已使用本地修复作为备选方案，基础格式问题已修复。`
              : 'AI服务未配置，已使用本地修复完成基础格式优化。';
            alert(message);
          }
        }, 1500);

      } else if (result.result === originalContent) {
        setFixingResults(prev => [...prev, '✅ 文档格式已经很规范，无需修复']);
        setTimeout(() => {
          alert('✅ 恭喜！您的文档格式已经很规范，未发现需要修复的问题。');
        }, 1000);
      } else {
        setFixingResults(prev => [...prev, '❌ 修复过程中出现错误']);
        alert('修复失败，请重试。');
      }
    } catch (error) {
      console.error('AI自动修复失败:', error);
      setFixingResults(prev => [...prev, `❌ 修复失败：${error instanceof Error ? error.message : '未知错误'}`]);
      alert('AI自动修复失败，请重试。');
    } finally {
      setFixingProgress('');
      setTimeout(() => {
        setIsAnalyzing(false);
      }, 2000); // 让用户有时间看到完整的过程
    }
  };



  // 应用建议
  const applySuggestion = (suggestion: FormatSuggestion) => {
    if (!suggestion.suggestion || suggestion.suggestion.includes('删除') || suggestion.suggestion.includes('考虑')) {
      return;
    }

    const lines = content.split('\n');
    lines[suggestion.line - 1] = suggestion.suggestion;
    onContentChange(lines.join('\n'));
    analyzeDocument(); // 重新分析
  };

  // 检查AI配置状态
  const checkAIConfiguration = () => {
    setIsAIConfigured(aiService.isInitialized());
  };

  // 组件打开时自动分析
  useEffect(() => {
    if (isOpen && content) {
      analyzeDocument();
      checkAIConfiguration();
    }
  }, [isOpen, content]);

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={`px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium ${className}`}
        title="智能排版"
      >
        智能排版
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">智能排版助手</h2>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 标签页 */}
        <div className="flex border-b border-gray-200 dark:border-gray-600">
          {[
            { key: 'toc', label: '目录生成', icon: '📋' },
            { key: 'suggestions', label: '格式建议', icon: '💡' },
            { key: 'analysis', label: '文档分析', icon: '📊' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex items-center gap-2 px-4 py-2 text-sm font-medium ${
                activeTab === tab.key
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-400'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-4 overflow-auto">
          {isAnalyzing && (
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                <p className="text-gray-600 dark:text-gray-400">正在分析文档...</p>
              </div>
            </div>
          )}

          {!isAnalyzing && documentStructure && (
            <>
              {/* 目录生成 */}
              {activeTab === 'toc' && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">文档目录</h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setShowTocPage(true)}
                        className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium"
                        disabled={documentStructure.toc.length === 0}
                        title="打开独立目录页面"
                      >
                        📚 目录页
                      </button>
                      <button
                        onClick={insertTOC}
                        className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                        disabled={documentStructure.toc.length === 0}
                        title="在文档中插入目录"
                      >
                        插入目录
                      </button>
                    </div>
                  </div>

                  {documentStructure.toc.length === 0 ? (
                    <p className="text-gray-500 dark:text-gray-400">文档中没有找到标题</p>
                  ) : (
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <h4 className="font-medium mb-2 text-gray-900 dark:text-white">目录预览：</h4>
                      <div className="space-y-1">
                        {documentStructure.toc.map((item, index) => (
                          <div
                            key={index}
                            className="flex items-center text-sm"
                            style={{ paddingLeft: `${(item.level - 1) * 16}px` }}
                          >
                            <span className="text-gray-600 dark:text-gray-400 mr-2">
                              H{item.level}
                            </span>
                            <span className="text-gray-900 dark:text-white">{item.title}</span>
                            <span className="text-gray-400 ml-auto">第{item.line}行</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 格式建议 */}
              {activeTab === 'suggestions' && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">格式建议</h3>
                    <div className="flex gap-2">
                      <div className="relative">
                        <button
                          onClick={() => aiAutoFixFormatting(false)}
                          disabled={isAnalyzing}
                          className={`px-3 py-1 text-white rounded text-sm font-medium ${
                            isAIConfigured
                              ? 'bg-purple-500 hover:bg-purple-600 disabled:opacity-50'
                              : 'bg-gray-400 cursor-not-allowed'
                          }`}
                          title={isAIConfigured ? 'AI智能修复' : '请先配置AI服务'}
                        >
                          {isAnalyzing ? '🤖 修复中...' : '🤖 AI修复'}
                        </button>

                        {/* 自定义提示按钮 */}
                        <button
                          onClick={() => setShowPromptInput(!showPromptInput)}
                          disabled={isAnalyzing}
                          className={`ml-1 px-2 py-1 text-white rounded text-sm ${
                            isAIConfigured
                              ? 'bg-purple-400 hover:bg-purple-500 disabled:opacity-50'
                              : 'bg-gray-400 cursor-not-allowed'
                          }`}
                          title="自定义AI修复指导"
                        >
                          💬
                        </button>
                      </div>

                      <button
                        onClick={autoFixFormatting}
                        disabled={isAnalyzing}
                        className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm disabled:opacity-50"
                      >
                        本地修复
                      </button>
                    </div>
                  </div>

                  {/* 自定义提示输入框 */}
                  {showPromptInput && (
                    <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="text-purple-500 text-lg mt-1">💬</div>
                        <div className="flex-1">
                          <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">自定义AI修复指导</h4>

                          {/* 预设提示词选择 */}
                          <div className="mb-3">
                            <label className="text-xs text-purple-700 dark:text-purple-300 mb-2 block">
                              📋 选择预设提示词（可选）：
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                {
                                  type: 'general',
                                  label: '通用文档',
                                  prompt: '按照通用文档标准进行格式优化，确保标题层级清晰、段落结构合理、标点符号规范。'
                                },
                                {
                                  type: 'academic',
                                  label: '学术论文',
                                  prompt: '按照学术写作规范进行格式优化，统一引用格式、规范图表标题、确保专业术语一致性、优化章节结构。'
                                },
                                {
                                  type: 'business',
                                  label: '商务报告',
                                  prompt: '按照商务文档标准进行格式优化，突出重点信息、规范数据表格、统一专业术语、优化可读性。'
                                },
                                {
                                  type: 'blog',
                                  label: '博客文章',
                                  prompt: '按照网络阅读习惯进行格式优化，增强可读性、优化段落长度、规范链接格式、突出关键信息。'
                                }
                              ].map(option => (
                                <button
                                  key={option.type}
                                  onClick={() => setCustomPrompt(option.prompt)}
                                  className="p-2 text-left border border-purple-200 dark:border-purple-600 rounded text-xs hover:bg-purple-100 dark:hover:bg-purple-800/30 transition-colors"
                                  disabled={isAnalyzing}
                                >
                                  <div className="font-medium text-purple-800 dark:text-purple-200">{option.label}</div>
                                  <div className="text-purple-600 dark:text-purple-400 mt-1 line-clamp-2">
                                    {option.prompt.substring(0, 50)}...
                                  </div>
                                </button>
                              ))}
                            </div>
                          </div>

                          <textarea
                            value={customPrompt}
                            onChange={(e) => setCustomPrompt(e.target.value)}
                            placeholder="请输入您希望AI特别关注的修复要求，例如：&#10;• 保持技术术语的英文原文&#10;• 统一使用中文标点符号&#10;• 调整标题层级结构&#10;• 优化代码块的语言标识&#10;&#10;或点击上方预设提示词快速填入"
                            className="w-full h-24 px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-md text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            disabled={isAnalyzing}
                          />
                          <div className="flex items-center justify-between mt-3">
                            <div className="text-xs text-purple-700 dark:text-purple-300">
                              💡 提示：AI会根据您的指导进行更精准的格式修复
                            </div>
                            <div className="flex gap-2">
                              <button
                                onClick={() => {
                                  setCustomPrompt('');
                                  setShowPromptInput(false);
                                }}
                                disabled={isAnalyzing}
                                className="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded disabled:opacity-50"
                              >
                                取消
                              </button>
                              <button
                                onClick={() => {
                                  aiAutoFixFormatting(true);
                                  setShowPromptInput(false);
                                }}
                                disabled={isAnalyzing || !isAIConfigured}
                                className={`px-3 py-1 text-xs text-white rounded disabled:opacity-50 ${
                                  isAIConfigured
                                    ? 'bg-purple-500 hover:bg-purple-600'
                                    : 'bg-gray-400 cursor-not-allowed'
                                }`}
                              >
                                🚀 开始AI修复
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 主内容区域 - 显示修复过程或格式建议 */}
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg min-h-[300px]">
                    {/* 修复过程显示 */}
                    {isAnalyzing || (showFixingProcess && fixingResults.length > 0) ? (
                      <div className="p-4 h-full">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="text-blue-500">🔧</div>
                          <h4 className="font-medium text-gray-900 dark:text-white">AI智能修复过程</h4>
                        </div>

                        {/* 当前进度 */}
                        {fixingProgress && (
                          <div className="flex items-center gap-3 mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div className="animate-spin text-lg">⚙️</div>
                            <span className="text-blue-700 dark:text-blue-300 font-medium">{fixingProgress}</span>
                          </div>
                        )}

                        {/* 修复日志 */}
                        <div className="space-y-2 max-h-64 overflow-y-auto">
                          {fixingResults.map((result, index) => (
                            <div key={index} className="flex items-start gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded">
                              <span className="text-xs text-gray-400 mt-1 font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {String(index + 1).padStart(2, '0')}
                              </span>
                              <span className="text-gray-700 dark:text-gray-300 flex-1">{result}</span>
                            </div>
                          ))}
                        </div>

                        {/* 操作按钮 */}
                        {!isAnalyzing && fixingResults.length > 0 && (
                          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600 flex gap-2">
                            {detailedAnalysis && (
                              <button
                                onClick={() => setShowDetailedReport(true)}
                                className="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium"
                              >
                                📊 查看详细报告
                              </button>
                            )}
                            <button
                              onClick={() => {
                                setShowFixingProcess(false);
                                setFixingResults([]);
                                setFixingProgress('');
                              }}
                              className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm"
                            >
                              关闭日志
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      /* 格式建议列表 */
                      <div className="p-4">
                        {!documentStructure ? (
                          <div className="flex items-center justify-center h-48 text-gray-500 dark:text-gray-400">
                            <div className="text-center">
                              <div className="text-4xl mb-2">📝</div>
                              <p>点击上方按钮开始分析文档格式</p>
                            </div>
                          </div>
                        ) : documentStructure.suggestions.length === 0 ? (
                          <div className="flex items-center justify-center h-48 text-green-600 dark:text-green-400">
                            <div className="text-center">
                              <div className="text-4xl mb-2">✅</div>
                              <p className="font-medium">文档格式良好，没有发现问题</p>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                              发现 {documentStructure.suggestions.length} 个格式问题
                            </h4>
                            {documentStructure.suggestions.map((suggestion, index) => (
                              <div
                                key={index}
                                className={`p-3 rounded-lg border-l-4 ${
                                  suggestion.severity === 'error'
                                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                                    : suggestion.severity === 'warning'
                                    ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                                    : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                }`}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                                        suggestion.severity === 'error'
                                          ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                                          : suggestion.severity === 'warning'
                                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                                          : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                                      }`}>
                                        第{suggestion.line}行
                                      </span>
                                      <span className="text-sm text-gray-600 dark:text-gray-400">
                                        {suggestion.type}
                                      </span>
                                    </div>
                                    <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                                      {suggestion.message}
                                    </p>
                                    {suggestion.suggestion &&
                                     !suggestion.suggestion.includes('删除') &&
                                     !suggestion.suggestion.includes('考虑') && (
                                      <div className="bg-white dark:bg-gray-800 p-2 rounded border text-sm font-mono">
                                        {suggestion.suggestion}
                                      </div>
                                    )}
                                  </div>
                                  {suggestion.suggestion &&
                                   !suggestion.suggestion.includes('删除') &&
                                   !suggestion.suggestion.includes('考虑') && (
                                    <button
                                      onClick={() => applySuggestion(suggestion)}
                                      className="ml-3 px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs"
                                    >
                                      应用
                                    </button>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 修复方式说明 - 只在不显示修复过程时显示 */}
                  {!isAnalyzing && !showFixingProcess && (
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
                      <div className="flex items-start gap-2">
                        <div className="text-blue-500 mt-0.5">💡</div>
                        <div className="text-sm text-blue-700 dark:text-blue-300">
                          <div className="font-medium mb-1">修复方式对比：</div>
                          <div className="space-y-1">
                            <div><strong>🤖 AI修复</strong>：使用人工智能进行全面的格式优化，包括复杂的排版规则和上下文理解</div>
                            <div><strong>🔧 本地修复</strong>：使用预定义规则进行基础格式修复，速度快但功能有限</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {documentStructure.suggestions.length === 0 ? (
                    <p className="text-green-600 dark:text-green-400">✅ 文档格式良好，没有发现问题</p>
                  ) : (
                    <div className="space-y-2">
                      {documentStructure.suggestions.map((suggestion, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg border-l-4 ${
                            suggestion.severity === 'error'
                              ? 'bg-red-50 border-red-400 dark:bg-red-900/20'
                              : suggestion.severity === 'warning'
                              ? 'bg-yellow-50 border-yellow-400 dark:bg-yellow-900/20'
                              : 'bg-blue-50 border-blue-400 dark:bg-blue-900/20'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                第{suggestion.line}行：{suggestion.message}
                              </p>
                              {suggestion.suggestion && !suggestion.suggestion.includes('删除') && !suggestion.suggestion.includes('考虑') && (
                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                  建议：{suggestion.suggestion}
                                </p>
                              )}
                            </div>
                            {suggestion.suggestion && !suggestion.suggestion.includes('删除') && !suggestion.suggestion.includes('考虑') && (
                              <button
                                onClick={() => applySuggestion(suggestion)}
                                className="ml-2 px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs"
                              >
                                应用
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* 文档分析 */}
              {activeTab === 'analysis' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">文档分析</h3>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">总字数</p>
                      <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                        {documentStructure.wordCount}
                      </p>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">阅读时间</p>
                      <p className="text-xl font-bold text-green-600 dark:text-green-400">
                        {documentStructure.readingTime}分钟
                      </p>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">标题数量</p>
                      <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                        {Object.values(documentStructure.headingCount).reduce((a, b) => a + b, 0)}
                      </p>
                    </div>
                    <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">格式问题</p>
                      <p className="text-xl font-bold text-orange-600 dark:text-orange-400">
                        {documentStructure.suggestions.length}
                      </p>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium mb-2 text-gray-900 dark:text-white">标题分布：</h4>
                    <div className="space-y-1">
                      {Object.entries(documentStructure.headingCount).map(([level, count]) => (
                        <div key={level} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">H{level} 标题</span>
                          <span className="text-gray-900 dark:text-white">{count} 个</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}


            </>
          )}
        </div>

        {/* 底部操作 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={analyzeDocument}
            disabled={isAnalyzing}
            className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm disabled:opacity-50"
          >
            {isAnalyzing ? '分析中...' : '重新分析'}
          </button>
          
          <button
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
          >
            完成
          </button>
        </div>
      </div>

      {/* 详细修复报告模态框 */}
      {showDetailedReport && detailedAnalysis && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl max-h-5/6 flex flex-col">
            {/* 报告头部 */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">📊 详细修复报告</h3>
              <button
                onClick={() => setShowDetailedReport(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 报告内容 */}
            <div className="p-4 overflow-y-auto flex-1">
              {/* 统计概览 */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 mb-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">📈 修复统计</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {detailedAnalysis.changedLines}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">修改行数</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {detailedAnalysis.addedLines}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">新增行数</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                      {detailedAnalysis.removedLines}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">删除行数</div>
                  </div>
                </div>
              </div>

              {/* 详细变更列表 */}
              {detailedAnalysis.changes && detailedAnalysis.changes.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">🔍 详细变更</h4>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {detailedAnalysis.changes.slice(0, 20).map((change: any, index: number) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-600 rounded p-3">
                        <div className="flex items-center gap-2 mb-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            change.type === 'modified'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                              : change.type === 'added'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                          }`}>
                            {change.type === 'modified' ? '修改' : change.type === 'added' ? '新增' : '删除'}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">第 {change.line} 行</span>
                        </div>

                        {change.original && (
                          <div className="mb-1">
                            <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">原文：</div>
                            <div className="bg-red-50 dark:bg-red-900/20 border-l-2 border-red-300 dark:border-red-700 pl-2 py-1 text-sm font-mono">
                              {change.original}
                            </div>
                          </div>
                        )}

                        {change.fixed && (
                          <div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">修复后：</div>
                            <div className="bg-green-50 dark:bg-green-900/20 border-l-2 border-green-300 dark:border-green-700 pl-2 py-1 text-sm font-mono">
                              {change.fixed}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {detailedAnalysis.changes.length > 20 && (
                      <div className="text-center text-sm text-gray-500 dark:text-gray-400 py-2">
                        还有 {detailedAnalysis.changes.length - 20} 个变更未显示...
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* 报告底部 */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  修复完成时间：{new Date().toLocaleString()}
                </div>
                <button
                  onClick={() => setShowDetailedReport(false)}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                >
                  关闭报告
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 独立目录页面模态框 */}
      {showTocPage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-2xl w-full max-w-4xl h-5/6 flex flex-col">
            <TableOfContentsPage
              content={content}
              onNavigate={handleTocNavigation}
              onClose={() => setShowTocPage(false)}
              className="flex-1"
            />
          </div>
        </div>
      )}
    </div>
  );
};
