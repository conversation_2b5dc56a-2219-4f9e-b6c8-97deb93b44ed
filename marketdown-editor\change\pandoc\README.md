# Document Converter

一个强大的文档格式转换工具，支持PDF、Word(.docx)、Excel(.xlsx)、TXT、Markdown(.md)之间的互相转换。

## 功能特性

- 🔄 **多格式支持**: PDF ↔ Word ↔ Excel ↔ TXT ↔ Markdown
- 🚀 **高效转换**: 基于成熟的开源库，转换质量高
- 🎯 **批量处理**: 支持批量文件转换
- 🛠️ **命令行工具**: 简单易用的CLI界面
- 📝 **格式保持**: 尽可能保持原文档的格式和结构

## 支持的转换格式

| 源格式 | 目标格式 | 状态 |
|--------|----------|------|
| PDF | Word, Excel, TXT, Markdown | ✅ |
| Word | PDF, Excel, TXT, Markdown | ✅ |
| Excel | PDF, Word, TXT, Markdown | ✅ |
| TXT | PDF, Word, Excel, Markdown | ✅ |
| Markdown | PDF, Word, Excel, TXT | ✅ |

## 安装

```bash
# 克隆仓库
git clone https://github.com/your-username/document-converter.git
cd document-converter

# 安装依赖
pip install -r requirements.txt

# 或者直接安装
pip install -e .
```

## 快速开始

### 命令行使用

```bash
# 基本转换
docconvert input.pdf output.docx

# 批量转换
docconvert *.pdf --output-format docx --output-dir ./converted/

# 指定转换选项
docconvert input.md output.pdf --template custom --quality high
```

### Python API

```python
from document_converter import DocumentConverter

converter = DocumentConverter()

# 单文件转换
converter.convert('input.pdf', 'output.docx')

# 批量转换
converter.batch_convert(['file1.pdf', 'file2.md'], output_format='docx')
```

## 依赖要求

- Python 3.8+
- Pandoc (用于Markdown转换)
- 其他Python包见 requirements.txt

## 许可证

MIT License
