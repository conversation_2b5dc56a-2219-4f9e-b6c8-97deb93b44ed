"""
文档转换器核心类
"""

import os
import logging
from typing import List, Dict, Optional, Union
from pathlib import Path

from .exceptions import ConversionError, UnsupportedFormatError, FileNotFoundError
from .utils.file_utils import detect_file_format, validate_file, get_file_info
from .utils.conversion_utils import get_converter_class, get_supported_formats, get_output_filename


class DocumentConverter:
    """文档转换器主类"""
    
    def __init__(self, **config):
        """
        初始化文档转换器
        
        Args:
            **config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 设置日志级别
        log_level = config.get('log_level', 'INFO')
        logging.basicConfig(level=getattr(logging, log_level))
    
    def convert(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        转换单个文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            **kwargs: 转换参数
            
        Returns:
            转换是否成功
            
        Raises:
            FileNotFoundError: 输入文件不存在
            UnsupportedFormatError: 不支持的格式
            ConversionError: 转换失败
        """
        try:
            # 检测输入文件格式
            source_format = detect_file_format(input_path)
            
            # 检测目标格式
            target_format = kwargs.get('target_format')
            if target_format is None:
                _, ext = os.path.splitext(output_path.lower())
                target_format = ext.lstrip('.')
            
            self.logger.info(f"转换 {source_format} -> {target_format}: {input_path}")
            
            # 获取转换器
            converter_class = get_converter_class(source_format, target_format)
            converter = converter_class(**self.config)
            
            # 执行转换
            # 移除kwargs中的target_format以避免重复参数
            clean_kwargs = {k: v for k, v in kwargs.items() if k != 'target_format'}
            return converter.convert(input_path, output_path, target_format, **clean_kwargs)
            
        except Exception as e:
            self.logger.error(f"转换失败: {str(e)}")
            raise
    
    def batch_convert(self, input_paths: List[str], output_dir: str = None, 
                     output_format: str = None, **kwargs) -> Dict[str, bool]:
        """
        批量转换文件
        
        Args:
            input_paths: 输入文件路径列表
            output_dir: 输出目录，如果为None则使用输入文件所在目录
            output_format: 输出格式，如果为None则需要在kwargs中指定每个文件的格式
            **kwargs: 转换参数
            
        Returns:
            转换结果字典 {文件路径: 是否成功}
        """
        results = {}
        
        for input_path in input_paths:
            try:
                # 生成输出路径
                if output_dir:
                    filename = os.path.basename(input_path)
                    if output_format:
                        filename = get_output_filename(filename, output_format)
                    output_path = os.path.join(output_dir, filename)
                else:
                    if output_format:
                        output_path = get_output_filename(input_path, output_format)
                    else:
                        raise ConversionError("必须指定output_dir或output_format")
                
                # 转换文件
                success = self.convert(input_path, output_path, **kwargs)
                results[input_path] = success
                
            except Exception as e:
                self.logger.error(f"批量转换失败 {input_path}: {str(e)}")
                results[input_path] = False
        
        return results
    
    def convert_directory(self, input_dir: str, output_dir: str, 
                         output_format: str, recursive: bool = False, 
                         **kwargs) -> Dict[str, bool]:
        """
        转换目录中的所有文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            output_format: 输出格式
            recursive: 是否递归处理子目录
            **kwargs: 转换参数
            
        Returns:
            转换结果字典
        """
        if not os.path.isdir(input_dir):
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")
        
        # 收集所有文件
        input_files = []
        supported_formats = get_supported_formats()
        
        if recursive:
            for root, dirs, files in os.walk(input_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        file_format = detect_file_format(file_path)
                        if file_format in supported_formats:
                            input_files.append(file_path)
                    except Exception:
                        continue
        else:
            for file in os.listdir(input_dir):
                file_path = os.path.join(input_dir, file)
                if os.path.isfile(file_path):
                    try:
                        file_format = detect_file_format(file_path)
                        if file_format in supported_formats:
                            input_files.append(file_path)
                    except Exception:
                        continue
        
        # 批量转换
        return self.batch_convert(input_files, output_dir, output_format, **kwargs)
    
    def get_file_info(self, file_path: str) -> Dict:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        return get_file_info(file_path)
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的格式列表
        
        Returns:
            支持的格式列表
        """
        return get_supported_formats()
    
    def validate_conversion(self, source_format: str, target_format: str) -> bool:
        """
        验证转换是否支持
        
        Args:
            source_format: 源格式
            target_format: 目标格式
            
        Returns:
            是否支持转换
        """
        try:
            get_converter_class(source_format, target_format)
            return True
        except UnsupportedFormatError:
            return False
