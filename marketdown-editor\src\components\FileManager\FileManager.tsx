'use client';

import React, { useCallback, useRef } from 'react';
import { useEditorStore } from '@/stores/useEditorStore';
import { readFileAsText, downloadFile, formatDate } from '@/utils';
import { Document } from '@/types';

interface FileManagerProps {
  className?: string;
}

export const FileManager: React.FC<FileManagerProps> = ({ className = '' }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const {
    documents,
    currentDocument,
    createNewDocument,
    setCurrentDocument,
    deleteDocument,
    updateCurrentDocument,
  } = useEditorStore();

  // 创建新文档
  const handleNewDocument = useCallback(() => {
    const title = prompt('Enter document title:', 'Untitled Document');
    if (title !== null) {
      createNewDocument(title.trim() || 'Untitled Document');
    }
  }, [createNewDocument]);

  // 打开文件选择器
  const handleOpenFile = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // 处理文件选择
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const content = await readFileAsText(file);
      const newDocument: Document = {
        id: Math.random().toString(36).substr(2, 9),
        title: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
        content,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // 添加到文档列表并设为当前文档
      const { documents } = useEditorStore.getState();
      useEditorStore.setState({
        documents: [newDocument, ...documents],
        currentDocument: newDocument,
      });
    } catch (error) {
      alert('Failed to read file. Please try again.');
      console.error('File read error:', error);
    }

    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  }, []);

  // 保存当前文档
  const handleSaveDocument = useCallback(() => {
    if (!currentDocument) {
      alert('No document to save');
      return;
    }

    const filename = `${currentDocument.title}.md`;
    downloadFile(currentDocument.content, filename, 'text/markdown');
  }, [currentDocument]);

  // 删除文档
  const handleDeleteDocument = useCallback((doc: Document, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (confirm(`Are you sure you want to delete "${doc.title}"?`)) {
      deleteDocument(doc.id);
    }
  }, [deleteDocument]);

  // 选择文档
  const handleSelectDocument = useCallback((doc: Document) => {
    setCurrentDocument(doc);
  }, [setCurrentDocument]);

  // 重命名文档
  const handleRenameDocument = useCallback((doc: Document, event: React.MouseEvent) => {
    event.stopPropagation();
    
    const newTitle = prompt('Enter new title:', doc.title);
    if (newTitle !== null && newTitle.trim() !== doc.title) {
      const updatedDoc = { ...doc, title: newTitle.trim() || 'Untitled', updatedAt: new Date() };
      
      // 更新文档列表
      const { documents } = useEditorStore.getState();
      const updatedDocuments = documents.map(d => d.id === doc.id ? updatedDoc : d);
      useEditorStore.setState({ documents: updatedDocuments });
      
      // 如果是当前文档，也更新当前文档
      if (currentDocument?.id === doc.id) {
        setCurrentDocument(updatedDoc);
      }
    }
  }, [currentDocument, setCurrentDocument]);

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 ${className}`}>
      {/* 文件操作按钮 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col gap-2">
          <button
            onClick={handleNewDocument}
            className="w-full px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
          >
            New Document
          </button>
          
          <button
            onClick={handleOpenFile}
            className="w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
          >
            Open File
          </button>
          
          <button
            onClick={handleSaveDocument}
            disabled={!currentDocument}
            className="w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save Current
          </button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept=".md,.txt,.markdown"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* 文档列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 px-2">
            Documents ({documents.length})
          </h3>
          
          {documents.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p className="text-sm">No documents yet</p>
              <p className="text-xs mt-1">Create a new document to get started</p>
            </div>
          ) : (
            <div className="space-y-1">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  onClick={() => handleSelectDocument(doc)}
                  className={`
                    group p-3 rounded-lg cursor-pointer transition-colors
                    ${currentDocument?.id === doc.id
                      ? 'bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }
                  `}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {doc.title}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {formatDate(doc.updatedAt)}
                      </p>
                      {doc.content && (
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1 line-clamp-2">
                          {doc.content.substring(0, 60)}...
                        </p>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={(e) => handleRenameDocument(doc, e)}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Rename"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={(e) => handleDeleteDocument(doc, e)}
                        className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                        title="Delete"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
