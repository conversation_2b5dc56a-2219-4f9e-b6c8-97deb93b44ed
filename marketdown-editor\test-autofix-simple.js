// 简单测试自动修复功能
const testContent = `#测试文档

##标题没有空格

这是一个段落，中文English混合，没有空格。还有数字123和中文混合。

###标题结尾有标点符号。`;

console.log('=== 原始内容 ===');
console.log(testContent);

// 模拟修复逻辑
let lines = testContent.split('\n');
let fixedCount = 0;

lines.forEach((line, index) => {
  const lineNumber = index + 1;
  let newLine = line;
  
  // 修复标题格式
  if (line.match(/^#{1,6}/)) {
    // 添加空格
    if (!line.match(/^#{1,6}\s+/)) {
      newLine = line.replace(/^(#{1,6})/, '$1 ');
      console.log(`修复第${lineNumber}行: 添加标题空格`);
      console.log(`  原文: "${line}"`);
      console.log(`  修复: "${newLine}"`);
      fixedCount++;
    }
    
    // 移除标点符号
    if (newLine.match(/[。！？：；，]$/)) {
      newLine = newLine.replace(/[。！？：；，]+$/, '');
      console.log(`修复第${lineNumber}行: 移除标题标点`);
      console.log(`  修复: "${newLine}"`);
      fixedCount++;
    }
  }
  
  // 修复中英文空格
  if (line.trim() && !line.match(/^[#\s*-+>|`]/)) {
    if (line.match(/[\u4e00-\u9fff][a-zA-Z]|[a-zA-Z][\u4e00-\u9fff]/)) {
      newLine = newLine.replace(/([\u4e00-\u9fff])([a-zA-Z])/g, '$1 $2')
                       .replace(/([a-zA-Z])([\u4e00-\u9fff])/g, '$1 $2');
      if (newLine !== line) {
        console.log(`修复第${lineNumber}行: 中英文空格`);
        console.log(`  原文: "${line}"`);
        console.log(`  修复: "${newLine}"`);
        fixedCount++;
      }
    }
    
    if (line.match(/[\u4e00-\u9fff]\d|\d[\u4e00-\u9fff]/)) {
      newLine = newLine.replace(/([\u4e00-\u9fff])(\d)/g, '$1 $2')
                       .replace(/(\d)([\u4e00-\u9fff])/g, '$1 $2');
      if (newLine !== line) {
        console.log(`修复第${lineNumber}行: 中文数字空格`);
        console.log(`  修复: "${newLine}"`);
        fixedCount++;
      }
    }
  }
  
  lines[index] = newLine;
});

console.log(`\n总共修复了 ${fixedCount} 个问题`);
console.log('\n=== 修复后内容 ===');
console.log(lines.join('\n'));
