'use client';

import { useState } from 'react';
import { ExportDialog } from '@/components/ui/ExportDialog';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const testMarkdown = `# 表格对比度测试

## 测试表格1：基本信息

| 模块 | 训练阶段 | 交付阶段 | 说明 |
|------|----------|----------|------|
| 应用场景 | 开发环境（可联网） | 出入境大厅/自助点/查验车 | 均需满足 GA 458-2021 标准 |
| 时间周期 | 4 周开发（可联网） | 2 周线上测试 | 总周期 6 周，无延期缓冲 |
| 人员 | 2 算法+1 联合式+1 QA（远程协同） | 仅需运维人员（现场部署） | 开发远程，交付后客户技术人员驻场 |

## 测试表格2：技术规格

| 硬件 | 训练端：云服务器（AWS/GCP，可选）开发端：同交付硬件 | 端：RK3588 + 8MP 相机 + 补光灯本地服务器：Win10 + RTX3060 | 交付硬件均为现货，提前预定高端环境 |
| 网络 | 训练期：公网/内网（按需获取数据） | 交付后：物理隔离，内网干扰（无外网） | **数据仅在训练期网联，交付后受限网络理，交付后受限** |

## 测试表格3：复杂内容

| 功能模块 | 详细描述 | 技术实现 | 预期效果 |
|----------|----------|----------|----------|
| 数据采集 | 通过多种传感器采集环境数据，包括温度、湿度、光照强度等关键指标 | 使用IoT设备和边缘计算技术 | 实时数据采集，准确率>95% |
| 数据处理 | 对采集的原始数据进行清洗、过滤、聚合等预处理操作 | Python + Pandas + NumPy | 数据处理延迟<100ms |
| 智能分析 | 基于机器学习算法对数据进行模式识别和异常检测 | TensorFlow + Scikit-learn | 异常检测准确率>90% |
| 可视化展示 | 通过图表、仪表盘等形式直观展示分析结果 | React + D3.js + Chart.js | 响应时间<2s，支持实时更新 |
`;

export default function TestTableFix() {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [content, setContent] = useState(testMarkdown);

  const generateHTMLContent = () => {
    return content
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            🔧 表格黑色背景修复测试
          </h1>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <h2 className="text-lg font-semibold text-green-800 mb-2">✅ 问题已修复</h2>
            <ul className="text-green-700 space-y-1">
              <li>• 移除了所有黑色背景</li>
              <li>• 确保文字颜色对比度充足</li>
              <li>• 优化了所有主题的配色方案</li>
              <li>• 添加了强制文字可见性样式</li>
            </ul>
          </div>
          
          <div className="flex gap-4">
            <button
              onClick={() => setShowExportDialog(true)}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
            >
              测试导出（已修复）
            </button>
            <button
              onClick={() => setContent(testMarkdown)}
              className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors"
            >
              重置内容
            </button>
          </div>
        </div>

        {/* 修复前后对比 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              ❌ 修复前的问题
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <h3 className="font-medium text-red-800">黑色背景问题</h3>
                <p className="text-sm text-red-600">表格背景变成黑色，文字完全看不见</p>
              </div>
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <h3 className="font-medium text-red-800">对比度不足</h3>
                <p className="text-sm text-red-600">文字颜色与背景色对比度太低</p>
              </div>
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <h3 className="font-medium text-red-800">主题配色问题</h3>
                <p className="text-sm text-red-600">某些主题使用了过深的背景色</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              ✅ 修复后的效果
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h3 className="font-medium text-green-800">清晰可见</h3>
                <p className="text-sm text-green-600">所有文字都清晰可见，对比度充足</p>
              </div>
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h3 className="font-medium text-green-800">优化配色</h3>
                <p className="text-sm text-green-600">重新设计了所有主题的配色方案</p>
              </div>
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h3 className="font-medium text-green-800">强制保护</h3>
                <p className="text-sm text-green-600">添加了CSS强制样式，防止黑色背景</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 编辑器 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Markdown 编辑器
            </h2>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 font-mono text-sm resize-none"
              placeholder="输入包含表格的Markdown内容..."
            />
          </div>

          {/* 预览 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              预览效果
            </h2>
            <div className="h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-700">
              <div className="prose dark:prose-invert max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>

        {/* 技术修复说明 */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            🔧 技术修复详情
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">配色方案优化</h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                重新设计了所有4个主题的配色，确保文字与背景有足够对比度
              </p>
            </div>
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">强制样式保护</h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                添加了CSS !important规则，防止任何黑色背景的出现
              </p>
            </div>
            <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-2">对比度检查</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                实现了自动对比度检查函数，确保文字始终可见
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 导出对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        content={content}
        htmlContent={generateHTMLContent()}
      />
    </div>
  );
}
