"""
纯文本格式转换器
"""

import os
import re
from typing import Optional, List
import chardet

from .base_converter import BaseConverter
from ..exceptions import ConversionError


class TxtConverter(BaseConverter):
    """纯文本转换器"""
    
    @property
    def supported_input_formats(self) -> list:
        return ['txt']
    
    @property
    def supported_output_formats(self) -> list:
        return ['pdf', 'docx', 'xlsx', 'md']
    
    def convert_to_pdf(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        文本转PDF
        """
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # 读取文本内容
            text_content = self._read_text_file(input_path)
            
            if not text_content:
                return False
            
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4,
                                  rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            story = []
            styles = getSampleStyleSheet()
            
            # 创建自定义样式
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                leading=14,
                spaceAfter=6
            )
            
            # 按段落分割文本
            paragraphs = text_content.split('\n\n')
            
            for para_text in paragraphs:
                para_text = para_text.strip()
                if para_text:
                    # 检测是否为标题
                    if self._is_likely_heading(para_text):
                        para = Paragraph(para_text, styles['Heading2'])
                    else:
                        # 处理长段落
                        para = Paragraph(para_text.replace('\n', '<br/>'), normal_style)
                    
                    story.append(para)
                    story.append(Spacer(1, 6))
            
            # 构建PDF
            doc.build(story)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"文本转PDF失败: {str(e)}")
            return False
    
    def convert_to_docx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        文本转Word
        """
        try:
            from docx import Document
            from docx.shared import Inches
            
            # 读取文本内容
            text_content = self._read_text_file(input_path)
            
            if not text_content:
                return False
            
            # 创建Word文档
            doc = Document()
            
            # 按段落分割文本
            paragraphs = text_content.split('\n\n')
            
            for para_text in paragraphs:
                para_text = para_text.strip()
                if para_text:
                    # 检测是否为标题
                    if self._is_likely_heading(para_text):
                        doc.add_heading(para_text, level=1)
                    else:
                        # 处理普通段落
                        lines = para_text.split('\n')
                        paragraph = doc.add_paragraph()
                        
                        for i, line in enumerate(lines):
                            if i > 0:
                                paragraph.add_run('\n')
                            paragraph.add_run(line)
            
            # 保存文档
            doc.save(output_path)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"文本转Word失败: {str(e)}")
            return False
    
    def convert_to_xlsx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        文本转Excel
        尝试检测表格结构，否则将内容放入单个单元格
        """
        try:
            import pandas as pd
            
            # 读取文本内容
            text_content = self._read_text_file(input_path)
            
            if not text_content:
                return False
            
            # 尝试检测表格结构
            tables = self._detect_tables_in_text(text_content)
            
            if tables:
                # 如果检测到表格，创建多个工作表
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    for i, table_data in enumerate(tables):
                        df = pd.DataFrame(table_data[1:], columns=table_data[0] if len(table_data) > 1 else None)
                        sheet_name = f'Table_{i+1}' if len(tables) > 1 else 'Sheet1'
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
            else:
                # 如果没有表格，按行分割内容
                lines = text_content.split('\n')
                non_empty_lines = [line for line in lines if line.strip()]
                
                df = pd.DataFrame({'Content': non_empty_lines})
                df.to_excel(output_path, index=False)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"文本转Excel失败: {str(e)}")
            return False
    
    def convert_to_txt(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        文本转文本（复制文件）
        """
        try:
            import shutil
            shutil.copy2(input_path, output_path)
            return os.path.exists(output_path)
        except Exception as e:
            self.logger.error(f"文本复制失败: {str(e)}")
            return False
    
    def convert_to_md(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        文本转Markdown
        智能检测标题和格式
        """
        try:
            # 读取文本内容
            text_content = self._read_text_file(input_path)
            
            if not text_content:
                return False
            
            # 转换为Markdown格式
            markdown_content = self._convert_text_to_markdown(text_content)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"文本转Markdown失败: {str(e)}")
            return False
    
    def _read_text_file(self, file_path: str) -> str:
        """
        读取文本文件，自动检测编码
        """
        try:
            # 首先尝试UTF-8
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                # 检测文件编码
                with open(file_path, 'rb') as f:
                    raw_data = f.read()
                    encoding_info = chardet.detect(raw_data)
                    encoding = encoding_info.get('encoding', 'utf-8')
                
                # 使用检测到的编码读取
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except Exception as e:
                self.logger.error(f"文件编码检测失败: {str(e)}")
                # 最后尝试使用错误忽略模式
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
    
    def _is_likely_heading(self, text: str) -> bool:
        """
        判断文本是否可能是标题
        """
        text = text.strip()
        
        # 短文本且不以句号结尾
        if len(text) < 100 and not text.endswith('.') and not text.endswith('。'):
            return True
        
        # 全大写文本
        if text.isupper() and len(text) < 50:
            return True
        
        # 包含章节编号
        if re.match(r'^(第\s*[一二三四五六七八九十\d]+\s*[章节部分]|Chapter\s+\d+|Section\s+\d+)', text, re.IGNORECASE):
            return True
        
        # 数字编号开头
        if re.match(r'^\d+[\.\)]\s+', text):
            return True
        
        return False
    
    def _detect_tables_in_text(self, text: str) -> List[List[List[str]]]:
        """
        检测文本中的表格结构
        """
        tables = []
        lines = text.split('\n')
        
        current_table = []
        in_table = False
        
        for line in lines:
            line = line.strip()
            
            # 检测表格行（包含多个制表符或多个空格分隔的列）
            if '\t' in line or re.search(r'\s{2,}', line):
                # 分割列
                if '\t' in line:
                    columns = [col.strip() for col in line.split('\t')]
                else:
                    columns = [col.strip() for col in re.split(r'\s{2,}', line)]
                
                # 过滤空列
                columns = [col for col in columns if col]
                
                if len(columns) > 1:
                    current_table.append(columns)
                    in_table = True
                elif in_table:
                    # 表格结束
                    if current_table:
                        tables.append(current_table)
                        current_table = []
                    in_table = False
            elif in_table:
                # 表格结束
                if current_table:
                    tables.append(current_table)
                    current_table = []
                in_table = False
        
        # 处理最后一个表格
        if current_table:
            tables.append(current_table)
        
        return tables
    
    def _convert_text_to_markdown(self, text: str) -> str:
        """
        将纯文本转换为Markdown格式
        """
        lines = text.split('\n')
        markdown_lines = []
        
        in_code_block = False
        
        for line in lines:
            original_line = line
            line = line.strip()
            
            if not line:
                markdown_lines.append('')
                continue
            
            # 检测代码块（缩进的行）
            if original_line.startswith('    ') or original_line.startswith('\t'):
                if not in_code_block:
                    markdown_lines.append('```')
                    in_code_block = True
                markdown_lines.append(original_line)
            else:
                if in_code_block:
                    markdown_lines.append('```')
                    in_code_block = False
                
                # 检测标题
                if self._is_likely_heading(line):
                    # 根据内容判断标题级别
                    if any(keyword in line.lower() for keyword in ['chapter', '章', '第']):
                        markdown_lines.append(f"# {line}")
                    elif re.match(r'^\d+[\.\)]\s+', line):
                        markdown_lines.append(f"## {line}")
                    else:
                        markdown_lines.append(f"### {line}")
                else:
                    markdown_lines.append(line)
        
        # 关闭未关闭的代码块
        if in_code_block:
            markdown_lines.append('```')
        
        return '\n'.join(markdown_lines)
