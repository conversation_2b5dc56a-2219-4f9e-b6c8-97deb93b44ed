'use client';

import { useEffect, useRef } from 'react';

interface NavigationOptions {
  textareaRef: React.RefObject<HTMLTextAreaElement>;
  content: string;
}

export const useEditorNavigation = ({ textareaRef, content }: NavigationOptions) => {
  const lastNavigationRef = useRef<{ line: number; anchor: string } | null>(null);

  // 跳转到指定行
  const navigateToLine = (lineNumber: number) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const lines = content.split('\n');
    
    // 计算目标行的字符位置
    let position = 0;
    for (let i = 0; i < Math.min(lineNumber - 1, lines.length - 1); i++) {
      position += lines[i].length + 1; // +1 for newline character
    }

    // 设置光标位置
    textarea.focus();
    textarea.setSelectionRange(position, position);
    
    // 滚动到可见区域
    scrollToPosition(textarea, position);
  };

  // 跳转到锚点
  const navigateToAnchor = (anchor: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const lines = content.split('\n');
    
    // 查找匹配的标题行
    let targetLine = -1;
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headingMatch) {
        const title = headingMatch[2].trim();
        const lineAnchor = generateAnchor(title);
        if (lineAnchor === anchor) {
          targetLine = i + 1;
          break;
        }
      }
    }

    if (targetLine > 0) {
      navigateToLine(targetLine);
    }
  };

  // 生成锚点（与智能格式化服务保持一致）
  const generateAnchor = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文字符
      .replace(/\s+/g, '-')
      .trim();
  };

  // 滚动到指定位置
  const scrollToPosition = (textarea: HTMLTextAreaElement, position: number) => {
    // 创建一个临时的测量元素
    const measureElement = document.createElement('div');
    measureElement.style.cssText = `
      position: absolute;
      visibility: hidden;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: ${getComputedStyle(textarea).fontFamily};
      font-size: ${getComputedStyle(textarea).fontSize};
      line-height: ${getComputedStyle(textarea).lineHeight};
      padding: ${getComputedStyle(textarea).padding};
      border: ${getComputedStyle(textarea).border};
      width: ${textarea.clientWidth}px;
    `;
    
    document.body.appendChild(measureElement);
    
    // 计算到目标位置的文本高度
    const textBeforePosition = content.substring(0, position);
    measureElement.textContent = textBeforePosition;
    const targetHeight = measureElement.offsetHeight;
    
    document.body.removeChild(measureElement);
    
    // 滚动到目标位置
    const scrollTop = Math.max(0, targetHeight - textarea.clientHeight / 2);
    textarea.scrollTop = scrollTop;
  };

  // 监听目录导航事件
  useEffect(() => {
    const handleTocNavigate = (event: CustomEvent) => {
      const { line, anchor } = event.detail;
      lastNavigationRef.current = { line, anchor };
      
      if (line) {
        navigateToLine(line);
      } else if (anchor) {
        navigateToAnchor(anchor);
      }
    };

    window.addEventListener('tocNavigate', handleTocNavigate as EventListener);
    
    return () => {
      window.removeEventListener('tocNavigate', handleTocNavigate as EventListener);
    };
  }, [content]);

  // 高亮当前行
  const highlightCurrentLine = () => {
    if (!textareaRef.current || !lastNavigationRef.current) return;

    const textarea = textareaRef.current;
    const { line } = lastNavigationRef.current;
    
    if (line) {
      const lines = content.split('\n');
      let position = 0;
      
      // 计算目标行的开始和结束位置
      for (let i = 0; i < Math.min(line - 1, lines.length - 1); i++) {
        position += lines[i].length + 1;
      }
      
      const lineLength = lines[line - 1]?.length || 0;
      const endPosition = position + lineLength;
      
      // 选中整行
      textarea.setSelectionRange(position, endPosition);
      
      // 短暂高亮后清除选择
      setTimeout(() => {
        textarea.setSelectionRange(position, position);
      }, 1500);
    }
  };

  // 获取当前光标所在的标题信息
  const getCurrentHeading = () => {
    if (!textareaRef.current) return null;

    const textarea = textareaRef.current;
    const cursorPosition = textarea.selectionStart;
    const lines = content.split('\n');
    
    let currentPosition = 0;
    let currentLine = 0;
    
    // 找到光标所在行
    for (let i = 0; i < lines.length; i++) {
      const lineLength = lines[i].length + 1; // +1 for newline
      if (currentPosition + lineLength > cursorPosition) {
        currentLine = i;
        break;
      }
      currentPosition += lineLength;
    }
    
    // 向上查找最近的标题
    for (let i = currentLine; i >= 0; i--) {
      const line = lines[i];
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headingMatch) {
        const level = headingMatch[1].length;
        const title = headingMatch[2].trim();
        const anchor = generateAnchor(title);
        return {
          level,
          title,
          anchor,
          line: i + 1
        };
      }
    }
    
    return null;
  };

  return {
    navigateToLine,
    navigateToAnchor,
    highlightCurrentLine,
    getCurrentHeading,
    lastNavigation: lastNavigationRef.current
  };
};
