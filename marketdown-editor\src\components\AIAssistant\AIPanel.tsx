'use client';

import React, { useState, useEffect } from 'react';
import { aiService, AIConfig, defaultAIConfig } from '@/utils/ai';
import { dbManager, AIModel, initializeDefaultModels } from '@/utils/indexedDB';
import { ProcessingSteps } from './ProcessingSteps';
import { PreciseFormatPanel } from './PreciseFormatPanel';

// AIModel接口现在从indexedDB导入

interface AIPanelProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  onContentChange: (newContent: string) => void;
}

export const AIPanel: React.FC<AIPanelProps> = ({
  isOpen,
  onClose,
  content,
  onContentChange,
}) => {
  const [isConfigured, setIsConfigured] = useState(false);
  const [showConfig, setShowConfig] = useState(false);
  const [modelTypeFilter, setModelTypeFilter] = useState<'all' | 'text' | 'image' | 'multimodal'>('all');
  const [showModelManager, setShowModelManager] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState('');
  const [processingProgress, setProcessingProgress] = useState(0);
  const [showPreciseFormat, setShowPreciseFormat] = useState(false);
  const [models, setModels] = useState<AIModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);
  const [newModel, setNewModel] = useState({
    name: '',
    api_key: '',
    base_url: '',
    model_name: '',
    model_type: 'text' as 'text' | 'image' | 'multimodal',
    temperature: 0.7,
    max_tokens: 2000,
    description: '',
  });

  // 加载AI模型列表
  const loadModels = async () => {
    try {
      await initializeDefaultModels(); // 确保默认模型已初始化
      const models = await dbManager.getAllAIModels();
      setModels(models);

      // 找到默认模型并初始化
      const defaultModel = models.find((model: AIModel) => model.is_default);
      if (defaultModel && defaultModel.api_key) {
        setSelectedModel(defaultModel);
        const config: AIConfig = {
          apiKey: defaultModel.api_key,
          baseURL: defaultModel.base_url,
          model: defaultModel.model_name,
          temperature: defaultModel.temperature,
          maxTokens: defaultModel.max_tokens,
        };
        const initialized = aiService.initialize(config);
        setIsConfigured(initialized);
      }
    } catch (error) {
      console.error('Failed to load AI models:', error);
    }
  };

  // 初始化时加载模型
  useEffect(() => {
    if (isOpen) {
      loadModels();
    }
  }, [isOpen]);

  // 选择模型
  const handleSelectModel = async (model: AIModel) => {
    if (!model.api_key) {
      alert('该模型未配置API密钥，请先编辑模型');
      return;
    }

    const config: AIConfig = {
      apiKey: model.api_key,
      baseURL: model.base_url,
      model: model.model_name,
      temperature: model.temperature,
      maxTokens: model.max_tokens,
    };

    const success = aiService.initialize(config);
    if (success) {
      setSelectedModel(model);
      setIsConfigured(true);
      setShowConfig(false);

      // 设置为默认模型
      try {
        if (model.id) {
          await dbManager.setDefaultAIModel(model.id);
          await loadModels(); // 重新加载模型列表
        }
      } catch (error) {
        console.error('Failed to set default model:', error);
      }
    } else {
      alert('模型初始化失败，请检查配置');
    }
  };

  // 保存新模型
  const handleSaveNewModel = async () => {
    if (!newModel.name || !newModel.api_key || !newModel.model_name) {
      alert('请填写必要字段：名称、API密钥、模型名称');
      return;
    }

    try {
      await dbManager.createAIModel(newModel);
      await loadModels();
      setNewModel({
        name: '',
        api_key: '',
        base_url: '',
        model_name: '',
        model_type: 'text',
        temperature: 0.7,
        max_tokens: 2000,
        description: '',
      });
      setShowModelManager(false);
      alert('模型保存成功');
    } catch (error) {
      console.error('Failed to save model:', error);
      alert(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除模型
  const handleDeleteModel = async (modelId: number) => {
    if (!confirm('确定要删除这个模型吗？')) return;

    try {
      await dbManager.deleteAIModel(modelId);
      await loadModels();
      if (selectedModel?.id === modelId) {
        setSelectedModel(null);
        setIsConfigured(false);
      }
      alert('模型删除成功');
    } catch (error) {
      console.error('Failed to delete model:', error);
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // AI处理函数
  const handleAIAction = async (action: string) => {
    if (!isConfigured) {
      setShowConfig(true);
      return;
    }

    if (!content.trim()) {
      alert('请先输入一些内容');
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);
    setProcessingStep('正在初始化AI请求...');
    
    try {
      // 步骤1：准备请求
      setProcessingStep('正在准备AI请求...');
      setProcessingProgress(10);
      await new Promise(resolve => setTimeout(resolve, 300));

      // 步骤2：分析内容
      setProcessingStep('正在分析文档内容...');
      setProcessingProgress(25);
      await new Promise(resolve => setTimeout(resolve, 400));

      let response;

      // 步骤3：执行AI操作
      const actionNames = {
        'grammar': '语法检查',
        'style-formal': '正式风格调整',
        'style-casual': '轻松风格调整',
        'expand': '内容扩展',
        'summarize': '内容总结',
        'translate-en': '翻译为英文',
        'translate-zh': '翻译为中文',
        'format': '格式优化'
      };

      setProcessingStep(`正在执行${actionNames[action as keyof typeof actionNames] || 'AI处理'}...`);
      setProcessingProgress(50);

      switch (action) {
        case 'grammar':
          response = await aiService.checkGrammar(content);
          break;
        case 'style-formal':
          response = await aiService.adjustStyle(content, '正式');
          break;
        case 'style-casual':
          response = await aiService.adjustStyle(content, '轻松');
          break;
        case 'expand':
          response = await aiService.expandContent(content);
          break;
        case 'summarize':
          response = await aiService.summarizeContent(content);
          break;
        case 'translate-en':
          response = await aiService.translateContent(content, '英文');
          break;
        case 'translate-zh':
          response = await aiService.translateContent(content, '中文');
          break;
        case 'format':
          response = await aiService.formatDocument(content);
          break;
        default:
          throw new Error('未知的AI操作');
      }

      // 步骤4：处理AI响应
      setProcessingStep('正在处理AI返回结果...');
      setProcessingProgress(80);
      await new Promise(resolve => setTimeout(resolve, 300));

      if (response.success && response.result) {
        // 步骤5：应用结果
        setProcessingStep('正在应用处理结果...');
        setProcessingProgress(95);
        await new Promise(resolve => setTimeout(resolve, 200));

        onContentChange(response.result);

        // 完成
        setProcessingStep('处理完成！');
        setProcessingProgress(100);
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        setProcessingStep('处理失败');
        alert(`AI处理失败: ${response.error || '未知错误'}`);
      }
    } catch (error) {
      setProcessingStep('处理出错');
      alert(`AI处理失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
      setProcessingProgress(0);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-96 max-w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              AI助手
            </h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ×
            </button>
          </div>

          {showModelManager ? (
            // 模型管理界面
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">AI模型管理</h4>
                <button
                  onClick={() => setShowModelManager(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  返回
                </button>
              </div>

              {/* 现有模型列表 */}
              <div className="max-h-40 overflow-y-auto space-y-2">
                {models.map((model) => (
                  <div
                    key={model.id}
                    className={`p-3 border rounded-lg ${
                      model.is_default
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{model.name}</div>
                        <div className="text-xs text-gray-500">{model.model_name}</div>
                        {model.description && (
                          <div className="text-xs text-gray-400 mt-1">{model.description}</div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {model.is_default && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            默认
                          </span>
                        )}
                        <button
                          onClick={() => handleSelectModel(model)}
                          className="text-xs bg-green-100 hover:bg-green-200 text-green-800 px-2 py-1 rounded"
                        >
                          使用
                        </button>
                        <button
                          onClick={() => handleDeleteModel(model.id)}
                          className="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded"
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 添加新模型表单 */}
              <div className="border-t pt-4">
                <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-3">添加新模型</h5>
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="模型名称"
                    value={newModel.name}
                    onChange={(e) => setNewModel({ ...newModel, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                  />
                  <input
                    type="password"
                    placeholder="API密钥"
                    value={newModel.api_key}
                    onChange={(e) => setNewModel({ ...newModel, api_key: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                  />
                  <input
                    type="text"
                    placeholder="API基础URL（可选）"
                    value={newModel.base_url}
                    onChange={(e) => setNewModel({ ...newModel, base_url: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                  />
                  <input
                    type="text"
                    placeholder="模型名称（如：gpt-3.5-turbo）"
                    value={newModel.model_name}
                    onChange={(e) => setNewModel({ ...newModel, model_name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                  />
                  <select
                    value={newModel.model_type}
                    onChange={(e) => setNewModel({ ...newModel, model_type: e.target.value as 'text' | 'image' | 'multimodal' })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                  >
                    <option value="text" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">文本模型</option>
                    <option value="image" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">图像模型</option>
                    <option value="multimodal" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">多模态模型</option>
                  </select>
                  <textarea
                    placeholder="描述（可选）"
                    value={newModel.description}
                    onChange={(e) => setNewModel({ ...newModel, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                    rows={2}
                  />
                  <button
                    onClick={handleSaveNewModel}
                    className="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors text-sm"
                  >
                    保存模型
                  </button>
                </div>
              </div>
            </div>
          ) : !isConfigured || showConfig ? (
            // 模型选择界面
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">选择AI模型</h4>
                <button
                  onClick={() => setShowModelManager(true)}
                  className="text-sm text-blue-500 hover:text-blue-700"
                >
                  管理模型
                </button>
              </div>

              {/* 模型类型筛选 */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">类型:</span>
                <select
                  value={modelTypeFilter}
                  onChange={(e) => setModelTypeFilter(e.target.value as any)}
                  className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">全部</option>
                  <option value="text" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">文本模型</option>
                  <option value="image" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">图像模型</option>
                  <option value="multimodal" className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700">多模态模型</option>
                </select>
              </div>

              <div className="space-y-3">
                {(() => {
                  const filteredModels = modelTypeFilter === 'all'
                    ? models
                    : models.filter(model => model.model_type === modelTypeFilter);

                  return filteredModels.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <p>{modelTypeFilter === 'all' ? '暂无可用模型' : `暂无${modelTypeFilter === 'text' ? '文本' : modelTypeFilter === 'image' ? '图像' : '多模态'}模型`}</p>
                      <button
                        onClick={() => setShowModelManager(true)}
                        className="mt-2 text-blue-500 hover:text-blue-700 underline"
                      >
                        添加模型
                      </button>
                    </div>
                  ) : (
                    filteredModels.map((model) => (
                    <div
                      key={model.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedModel?.id === model.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      }`}
                      onClick={() => handleSelectModel(model)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900 dark:text-gray-100">
                            {model.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {model.model_name}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <span className={`text-xs px-2 py-1 rounded ${
                              model.model_type === 'text'
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
                                : model.model_type === 'image'
                                ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
                                : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                            }`}>
                              {model.model_type === 'text' ? '文本' : model.model_type === 'image' ? '图像' : '多模态'}
                            </span>
                          </div>
                          {model.description && (
                            <div className="text-xs text-gray-400 mt-1">
                              {model.description}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {model.is_default && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              默认
                            </span>
                          )}
                          {!model.api_key && (
                            <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                              未配置
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                  );
                })()}
              </div>

              {selectedModel && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    当前选择: {selectedModel.name}
                  </div>
                </div>
              )}
            </div>
          ) : (
            // AI功能界面
            <div className="space-y-4">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                选择AI功能来优化您的文档：
              </div>

              {/* 处理过程显示 */}
              <ProcessingSteps
                currentStep={processingStep}
                progress={processingProgress}
                isVisible={isProcessing}
              />

              {/* 文档优化 */}
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">文档优化</h4>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleAIAction('grammar')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-blue-700 dark:text-blue-300">语法检查</div>
                    <div className="text-xs text-blue-600 dark:text-blue-400">修正语法错误</div>
                  </button>

                  <button
                    onClick={() => handleAIAction('format')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-green-700 dark:text-green-300">格式优化</div>
                    <div className="text-xs text-green-600 dark:text-green-400">改善文档结构</div>
                  </button>
                </div>

                {/* 精准格式优化 */}
                <div className="mt-3">
                  <button
                    onClick={() => setShowPreciseFormat(true)}
                    disabled={isProcessing}
                    className="w-full p-3 text-left bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 hover:from-purple-100 hover:to-pink-100 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30 rounded-lg transition-colors disabled:opacity-50 border border-purple-200 dark:border-purple-700"
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-lg">🎯</span>
                      <div>
                        <div className="font-medium text-purple-700 dark:text-purple-300">精准格式优化</div>
                        <div className="text-xs text-purple-600 dark:text-purple-400">专业标准，具体明确，达到完美格式</div>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* 风格调整 */}
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">风格调整</h4>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleAIAction('style-formal')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-purple-700 dark:text-purple-300">正式风格</div>
                    <div className="text-xs text-purple-600 dark:text-purple-400">商务正式</div>
                  </button>
                  
                  <button
                    onClick={() => handleAIAction('style-casual')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-orange-700 dark:text-orange-300">轻松风格</div>
                    <div className="text-xs text-orange-600 dark:text-orange-400">友好亲切</div>
                  </button>
                </div>
              </div>

              {/* 内容处理 */}
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">内容处理</h4>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleAIAction('expand')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-indigo-700 dark:text-indigo-300">内容扩展</div>
                    <div className="text-xs text-indigo-600 dark:text-indigo-400">添加更多细节</div>
                  </button>
                  
                  <button
                    onClick={() => handleAIAction('summarize')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-teal-50 dark:bg-teal-900/20 hover:bg-teal-100 dark:hover:bg-teal-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-teal-700 dark:text-teal-300">内容总结</div>
                    <div className="text-xs text-teal-600 dark:text-teal-400">提取关键要点</div>
                  </button>
                </div>
              </div>

              {/* 翻译 */}
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">翻译</h4>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleAIAction('translate-en')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-red-700 dark:text-red-300">翻译为英文</div>
                    <div className="text-xs text-red-600 dark:text-red-400">English</div>
                  </button>
                  
                  <button
                    onClick={() => handleAIAction('translate-zh')}
                    disabled={isProcessing}
                    className="p-3 text-left bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg transition-colors disabled:opacity-50"
                  >
                    <div className="font-medium text-yellow-700 dark:text-yellow-300">翻译为中文</div>
                    <div className="text-xs text-yellow-600 dark:text-yellow-400">中文</div>
                  </button>
                </div>
              </div>

              {isProcessing && (
                <div className="text-center py-4">
                  <div className="text-blue-500">AI正在处理中...</div>
                </div>
              )}

              <div className="pt-4 border-t border-gray-200 dark:border-gray-600 flex justify-between">
                <button
                  onClick={() => setShowConfig(true)}
                  className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  重新选择模型
                </button>
                <button
                  onClick={() => setShowModelManager(true)}
                  className="text-sm text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  管理模型
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 精准格式优化面板 */}
      {showPreciseFormat && (
        <PreciseFormatPanel
          content={content}
          onContentChange={onContentChange}
          onClose={() => setShowPreciseFormat(false)}
        />
      )}
    </div>
  );
};
