export interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  content: string;
  tags: string[];
}

export const documentTemplates: DocumentTemplate[] = [
  // 简历模板
  {
    id: 'resume-modern',
    name: '现代简历',
    description: '简洁现代的个人简历模板',
    category: '简历',
    icon: '👤',
    tags: ['简历', '求职', '个人'],
    content: `# 张三

**前端开发工程师**

📧 <EMAIL> | 📱 138-0000-0000 | 🌐 github.com/zhangsan

---

## 个人简介

具有3年前端开发经验的工程师，熟练掌握React、Vue等现代前端框架，有丰富的项目开发和团队协作经验。

## 工作经验

### 前端开发工程师 | ABC科技有限公司
*2021.06 - 至今*

- 负责公司主要产品的前端开发和维护
- 参与产品需求分析和技术方案设计
- 优化前端性能，提升用户体验

### 初级前端开发工程师 | XYZ互联网公司
*2020.07 - 2021.05*

- 参与多个Web项目的开发
- 学习并应用现代前端开发技术
- 与设计师和后端工程师密切合作

## 教育背景

### 计算机科学与技术学士 | 某某大学
*2016.09 - 2020.06*

- 主修课程：数据结构、算法、软件工程、数据库原理
- GPA: 3.8/4.0

## 技能清单

### 编程语言
- JavaScript/TypeScript
- HTML5/CSS3
- Python (基础)

### 框架和库
- React.js
- Vue.js
- Node.js
- Express.js

### 工具和平台
- Git/GitHub
- Webpack/Vite
- Docker (基础)
- AWS (基础)

## 项目经验

### 电商管理系统
*技术栈: React + TypeScript + Ant Design*

- 开发了完整的电商后台管理系统
- 实现了商品管理、订单处理、数据统计等功能
- 项目获得了客户的高度认可

### 个人博客网站
*技术栈: Vue.js + Nuxt.js*

- 设计并开发了响应式个人博客
- 集成了评论系统和搜索功能
- 月访问量达到10,000+

## 获奖经历

- 2022年度优秀员工
- 大学期间获得编程竞赛二等奖

---

*更新时间: ${new Date().toLocaleDateString('zh-CN')}*`
  },

  // 技术文档模板
  {
    id: 'tech-doc',
    name: '技术文档',
    description: '标准的技术文档模板',
    category: '技术',
    icon: '📚',
    tags: ['技术', '文档', 'API'],
    content: `# 项目名称

## 概述

简要描述项目的目的、功能和主要特性。

## 快速开始

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装

\`\`\`bash
# 克隆项目
git clone https://github.com/username/project.git

# 进入项目目录
cd project

# 安装依赖
npm install

# 启动开发服务器
npm run dev
\`\`\`

## 项目结构

\`\`\`
project/
├── src/
│   ├── components/     # 组件
│   ├── pages/         # 页面
│   ├── utils/         # 工具函数
│   └── styles/        # 样式文件
├── public/            # 静态资源
├── docs/              # 文档
└── tests/             # 测试文件
\`\`\`

## API 文档

### 用户相关接口

#### 获取用户信息

\`\`\`http
GET /api/users/:id
\`\`\`

**参数说明:**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id   | string | 是 | 用户ID |

**响应示例:**

\`\`\`json
{
  "code": 200,
  "data": {
    "id": "123",
    "name": "张三",
    "email": "<EMAIL>"
  },
  "message": "success"
}
\`\`\`

## 配置说明

### 环境变量

创建 \`.env\` 文件并配置以下变量：

\`\`\`env
# 数据库配置
DATABASE_URL=mongodb://localhost:27017/myapp

# API密钥
API_KEY=your_api_key_here

# 端口号
PORT=3000
\`\`\`

## 部署指南

### 生产环境部署

1. 构建项目
\`\`\`bash
npm run build
\`\`\`

2. 启动生产服务器
\`\`\`bash
npm start
\`\`\`

### Docker 部署

\`\`\`bash
# 构建镜像
docker build -t myapp .

# 运行容器
docker run -p 3000:3000 myapp
\`\`\`

## 常见问题

### Q: 如何解决端口冲突？
A: 修改 \`.env\` 文件中的 \`PORT\` 变量。

### Q: 数据库连接失败怎么办？
A: 检查数据库服务是否启动，确认连接字符串是否正确。

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (\`git checkout -b feature/AmazingFeature\`)
3. 提交更改 (\`git commit -m 'Add some AmazingFeature'\`)
4. 推送到分支 (\`git push origin feature/AmazingFeature\`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 作者: 张三
- 邮箱: <EMAIL>
- 项目链接: https://github.com/username/project`
  },

  // 博客文章模板
  {
    id: 'blog-post',
    name: '博客文章',
    description: '标准的博客文章模板',
    category: '写作',
    icon: '✍️',
    tags: ['博客', '文章', '写作'],
    content: `# 文章标题

> 一句话概括文章的核心观点或吸引读者的引言。

![封面图片](https://via.placeholder.com/800x400)

## 前言

在这里简要介绍文章的背景、写作动机或者要解决的问题。

## 正文

### 第一个要点

详细阐述第一个主要观点，可以包含：

- 具体的例子
- 数据支撑
- 个人经验

\`\`\`javascript
// 如果是技术文章，可以包含代码示例
function example() {
  console.log("Hello, World!");
}
\`\`\`

### 第二个要点

继续展开第二个重要观点。

> 💡 **小贴士**: 可以使用引用块来突出重要信息。

### 第三个要点

进一步深入讨论。

## 实践案例

分享一个具体的案例或实际应用：

1. **背景描述**: 说明案例的背景情况
2. **解决方案**: 详细描述采用的方法
3. **结果分析**: 分析最终的效果和收获

## 总结

总结文章的主要观点：

- 要点一的总结
- 要点二的总结  
- 要点三的总结

## 延伸阅读

- [相关文章1](https://example.com)
- [相关文章2](https://example.com)
- [参考资料](https://example.com)

---

**关于作者**

我是一名[职业/身份]，专注于[专业领域]。如果你对这篇文章有任何问题或想法，欢迎在评论区交流！

**标签**: #标签1 #标签2 #标签3

*发布时间: ${new Date().toLocaleDateString('zh-CN')}*`
  },

  // 会议记录模板
  {
    id: 'meeting-notes',
    name: '会议记录',
    description: '标准的会议记录模板',
    category: '商务',
    icon: '📝',
    tags: ['会议', '记录', '商务'],
    content: `# 会议记录

## 会议信息

- **会议主题**: [会议主题]
- **会议时间**: ${new Date().toLocaleDateString('zh-CN')} [开始时间] - [结束时间]
- **会议地点**: [会议室/线上会议链接]
- **主持人**: [主持人姓名]
- **记录人**: [记录人姓名]

## 参会人员

### 必须参加
- [姓名] - [职位/部门]
- [姓名] - [职位/部门]

### 可选参加
- [姓名] - [职位/部门]

### 缺席人员
- [姓名] - [缺席原因]

## 会议议程

1. **开场和介绍** (5分钟)
2. **上次会议回顾** (10分钟)
3. **主要议题讨论** (30分钟)
4. **决策和行动项** (10分钟)
5. **下次会议安排** (5分钟)

## 讨论内容

### 议题一: [议题标题]

**背景**: 
[议题背景描述]

**讨论要点**:
- 要点1
- 要点2
- 要点3

**结论**: 
[达成的结论或共识]

### 议题二: [议题标题]

**背景**: 
[议题背景描述]

**讨论要点**:
- 要点1
- 要点2

**结论**: 
[达成的结论或共识]

## 决策事项

| 决策内容 | 负责人 | 截止时间 | 状态 |
|----------|--------|----------|------|
| [决策1] | [负责人] | [日期] | 待执行 |
| [决策2] | [负责人] | [日期] | 待执行 |

## 行动项

| 任务描述 | 负责人 | 截止时间 | 优先级 | 备注 |
|----------|--------|----------|--------|------|
| [任务1] | [负责人] | [日期] | 高 | [备注] |
| [任务2] | [负责人] | [日期] | 中 | [备注] |

## 问题和风险

### 待解决问题
1. **问题1**: [问题描述]
   - 影响: [影响分析]
   - 建议解决方案: [方案]

2. **问题2**: [问题描述]
   - 影响: [影响分析]
   - 建议解决方案: [方案]

### 识别的风险
- **风险1**: [风险描述] - 应对措施: [措施]
- **风险2**: [风险描述] - 应对措施: [措施]

## 下次会议

- **时间**: [日期和时间]
- **地点**: [地点]
- **主要议题**: 
  1. [议题1]
  2. [议题2]

## 附件

- [附件1名称](链接)
- [附件2名称](链接)

---

**会议记录确认**

本记录已于 [日期] 发送给所有参会人员确认。

如有异议，请在 [日期] 前反馈。`
  },

  // 项目计划模板
  {
    id: 'project-plan',
    name: '项目计划',
    description: '完整的项目计划模板',
    category: '项目管理',
    icon: '📋',
    tags: ['项目', '计划', '管理'],
    content: `# 项目计划书

## 项目概述

### 项目基本信息
- **项目名称**: [项目名称]
- **项目经理**: [项目经理姓名]
- **项目发起人**: [发起人姓名]
- **计划开始时间**: [开始日期]
- **计划结束时间**: [结束日期]
- **项目预算**: [预算金额]

### 项目背景
[描述项目的背景、起因和必要性]

### 项目目标
- **主要目标**: [核心目标描述]
- **具体目标**:
  1. [具体目标1]
  2. [具体目标2]
  3. [具体目标3]

### 项目范围
**包含内容**:
- [范围内容1]
- [范围内容2]

**不包含内容**:
- [范围外内容1]
- [范围外内容2]

## 项目团队

### 项目组织架构
- **项目发起人**: [姓名] - [职责]
- **项目经理**: [姓名] - [职责]
- **技术负责人**: [姓名] - [职责]
- **业务负责人**: [姓名] - [职责]

### 团队成员
| 姓名 | 角色 | 职责 | 投入度 |
|------|------|------|--------|
| [姓名] | [角色] | [职责描述] | [百分比] |
| [姓名] | [角色] | [职责描述] | [百分比] |

## 项目计划

### 项目阶段
1. **需求分析阶段** ([开始日期] - [结束日期])
   - 需求收集
   - 需求分析
   - 需求确认

2. **设计阶段** ([开始日期] - [结束日期])
   - 系统设计
   - 界面设计
   - 数据库设计

3. **开发阶段** ([开始日期] - [结束日期])
   - 前端开发
   - 后端开发
   - 集成测试

4. **测试阶段** ([开始日期] - [结束日期])
   - 功能测试
   - 性能测试
   - 用户验收测试

5. **部署上线** ([开始日期] - [结束日期])
   - 生产环境部署
   - 数据迁移
   - 用户培训

### 关键里程碑
| 里程碑 | 计划完成时间 | 交付物 | 负责人 |
|--------|--------------|--------|--------|
| 需求确认 | [日期] | 需求规格说明书 | [负责人] |
| 设计完成 | [日期] | 设计文档 | [负责人] |
| 开发完成 | [日期] | 系统代码 | [负责人] |
| 测试完成 | [日期] | 测试报告 | [负责人] |
| 项目上线 | [日期] | 生产系统 | [负责人] |

## 风险管理

### 风险识别
| 风险描述 | 概率 | 影响 | 风险等级 | 应对策略 | 负责人 |
|----------|------|------|----------|----------|--------|
| [风险1] | 中 | 高 | 高 | [应对措施] | [负责人] |
| [风险2] | 低 | 中 | 低 | [应对措施] | [负责人] |

### 风险监控
- 每周风险评估会议
- 风险状态跟踪表更新
- 应急预案准备

## 质量管理

### 质量标准
- 功能完整性: 100%
- 性能要求: [具体指标]
- 可用性要求: [具体指标]
- 安全性要求: [具体指标]

### 质量保证措施
- 代码审查
- 自动化测试
- 持续集成
- 用户验收测试

## 沟通管理

### 沟通计划
| 沟通内容 | 频率 | 参与人员 | 沟通方式 |
|----------|------|----------|----------|
| 项目周报 | 每周 | 项目团队 | 邮件 |
| 进度会议 | 每周 | 核心成员 | 会议 |
| 里程碑汇报 | 按需 | 管理层 | 演示 |

### 项目文档
- 项目计划书
- 需求规格说明书
- 设计文档
- 测试计划
- 用户手册

## 预算管理

### 预算分解
| 费用类别 | 预算金额 | 实际支出 | 差异 |
|----------|----------|----------|------|
| 人力成本 | [金额] | [金额] | [差异] |
| 硬件设备 | [金额] | [金额] | [差异] |
| 软件许可 | [金额] | [金额] | [差异] |
| 其他费用 | [金额] | [金额] | [差异] |
| **总计** | [总金额] | [总金额] | [总差异] |

## 项目监控

### 进度跟踪
- 每日站会
- 周进度报告
- 月度项目评审

### 关键指标
- 进度完成率
- 质量指标
- 成本控制
- 风险状态

---

**文档版本**: v1.0  
**最后更新**: ${new Date().toLocaleDateString('zh-CN')}  
**更新人**: [更新人姓名]`
  }
];

export const getTemplatesByCategory = (category?: string): DocumentTemplate[] => {
  if (!category) return documentTemplates;
  return documentTemplates.filter(template => template.category === category);
};

export const getTemplateById = (id: string): DocumentTemplate | undefined => {
  return documentTemplates.find(template => template.id === id);
};

export const getCategories = (): string[] => {
  const categories = documentTemplates.map(template => template.category);
  return Array.from(new Set(categories));
};

export const searchTemplates = (query: string): DocumentTemplate[] => {
  const lowercaseQuery = query.toLowerCase();
  return documentTemplates.filter(template => 
    template.name.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};
