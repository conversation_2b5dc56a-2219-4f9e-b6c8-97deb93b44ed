# Document Converter

一个强大的文档格式转换工具，支持PDF、Word(.docx)、Excel(.xlsx)、TXT、Markdown(.md)之间的互相转换。

## 功能特性

- 🔄 **多格式支持**: PDF ↔ Word ↔ Excel ↔ TXT ↔ Markdown
- 🚀 **高效转换**: 基于成熟的开源库，转换质量高
- 🎯 **批量处理**: 支持批量文件转换
- 🛠️ **命令行工具**: 简单易用的CLI界面
- 📝 **格式保持**: 尽可能保持原文档的格式和结构
- 🧠 **智能识别**: 自动检测文件格式和编码
- ⚡ **并行处理**: 支持多线程批量转换

## 支持的转换格式

| 源格式 | 目标格式 | 状态 |
|--------|----------|------|
| PDF | Word, Excel, TXT, Markdown | ✅ |
| Word | PDF, Excel, TXT, Markdown | ✅ |
| Excel | PDF, Word, TXT, Markdown | ✅ |
| TXT | PDF, Word, Excel, Markdown | ✅ |
| Markdown | PDF, Word, Excel, TXT | ✅ |

## 安装

```bash
# 克隆仓库
git clone https://github.com/your-username/document-converter.git
cd document-converter

# 安装依赖
pip install -r requirements.txt

# 或者直接安装
pip install -e .
```

## 快速开始

### 命令行使用

```bash
# 查看帮助
python -m document_converter.cli --help

# 基本转换
python -m document_converter.cli convert input.pdf output.docx

# 批量转换
python -m document_converter.cli batch "*.pdf" -o ./converted -f docx

# 查看文件信息
python -m document_converter.cli info document.pdf

# 查看支持的格式
python -m document_converter.cli formats

# 指定转换选项
python -m document_converter.cli convert input.md output.pdf --toc --quality high
```

### Python API

```python
from document_converter import DocumentConverter

converter = DocumentConverter()

# 单文件转换
converter.convert('input.pdf', 'output.docx')

# 批量转换
converter.batch_convert(['file1.pdf', 'file2.md'], 
                       output_format='docx', 
                       output_dir='./converted')

# 获取文件信息
info = converter.get_file_info('document.pdf')
print(f"格式: {info['format']}, 大小: {info['size']} 字节")

# 验证转换是否支持
if converter.validate_conversion('pdf', 'docx'):
    print("支持PDF转Word")
```

## 运行演示

```bash
# 运行演示脚本
python examples/demo.py
```

## 测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_core.py -v
```

## 项目结构

```
document_converter/
├── document_converter/          # 主要代码
│   ├── __init__.py
│   ├── core.py                 # 核心转换器
│   ├── cli.py                  # 命令行界面
│   ├── exceptions.py           # 自定义异常
│   ├── converters/             # 各格式转换器
│   │   ├── base_converter.py   # 转换器基类
│   │   ├── pdf_converter.py    # PDF转换器
│   │   ├── docx_converter.py   # Word转换器
│   │   ├── xlsx_converter.py   # Excel转换器
│   │   ├── txt_converter.py    # 文本转换器
│   │   └── md_converter.py     # Markdown转换器
│   └── utils/                  # 工具函数
│       ├── file_utils.py       # 文件处理工具
│       └── conversion_utils.py # 转换工具
├── tests/                      # 测试文件
├── examples/                   # 示例文件
├── requirements.txt            # 依赖列表
└── README.md                   # 说明文档
```

## 依赖要求

- Python 3.8+
- 核心依赖：
  - `python-docx` - Word文档处理
  - `openpyxl` - Excel文档处理
  - `PyPDF2` / `pdfplumber` - PDF文档处理
  - `reportlab` - PDF生成
  - `pandas` - 数据处理
  - `markdown` - Markdown解析
  - `click` - 命令行界面
- 可选依赖：
  - `pandoc` - 高质量文档转换（推荐）
  - `LibreOffice` - Office文档转换

## 转换示例

### 1. Markdown 转 PDF
```bash
python -m document_converter.cli convert document.md document.pdf --toc
```

### 2. Excel 转 Markdown
```bash
python -m document_converter.cli convert data.xlsx data.md
```

### 3. 批量转换
```bash
python -m document_converter.cli batch "docs/*.txt" -o converted -f md
```

## 注意事项

1. **PDF转换**: PDF文本提取质量取决于原文档的结构
2. **格式保持**: 复杂格式转换可能会丢失部分样式
3. **编码处理**: 自动检测文本文件编码，支持UTF-8、GBK等
4. **依赖工具**: 某些高级功能需要安装pandoc或LibreOffice
5. **文件大小**: 大文件转换可能需要较长时间

## 开发

### 添加新的转换器

1. 继承 `BaseConverter` 类
2. 实现必要的转换方法
3. 在 `conversion_utils.py` 中注册转换器
4. 添加相应的测试

### 代码质量

```bash
# 代码格式化
black document_converter/

# 代码检查
flake8 document_converter/

# 测试覆盖率
pytest --cov=document_converter tests/
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
