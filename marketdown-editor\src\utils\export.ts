import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType } from 'docx';
import { saveAs } from 'file-saver';

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'html' | 'txt';
  title?: string;
  author?: string;
  pageSize?: 'A4' | 'Letter' | 'A3';
  margin?: number;
  fontSize?: number;
  fontFamily?: string;
  includeStyles?: boolean;
  tableTheme?: 'professional' | 'modern' | 'minimal' | 'colorful';
}

// 表格主题配置 - 修复黑色背景问题
const tableThemes = {
  professional: {
    headerBg: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',
    headerColor: '#ffffff',
    borderColor: '#cbd5e0',
    evenRowBg: '#f7fafc',
    oddRowBg: '#ffffff',
    hoverBg: '#edf2f7',
    textColor: '#2d3748',
    wordHeaderBg: '4A5568',
    wordEvenBg: 'F7FAFC',
    wordOddBg: 'FFFFFF',
  },
  modern: {
    headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    headerColor: '#ffffff',
    borderColor: '#e2e8f0',
    evenRowBg: '#f8fafc',
    oddRowBg: '#ffffff',
    hoverBg: '#ebf8ff',
    textColor: '#2d3748',
    wordHeaderBg: '667EEA',
    wordEvenBg: 'F8FAFC',
    wordOddBg: 'FFFFFF',
  },
  minimal: {
    headerBg: 'linear-gradient(135deg, #a0aec0 0%, #718096 100%)',
    headerColor: '#ffffff',
    borderColor: '#e2e8f0',
    evenRowBg: '#f9fafb',
    oddRowBg: '#ffffff',
    hoverBg: '#f1f5f9',
    textColor: '#374151',
    wordHeaderBg: 'A0AEC0',
    wordEvenBg: 'F9FAFB',
    wordOddBg: 'FFFFFF',
  },
  colorful: {
    headerBg: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
    headerColor: '#ffffff',
    borderColor: '#fed7d7',
    evenRowBg: '#fef5e7',
    oddRowBg: '#ffffff',
    hoverBg: '#ffeaa7',
    textColor: '#2d3748',
    wordHeaderBg: 'F56565',
    wordEvenBg: 'FEF5E7',
    wordOddBg: 'FFFFFF',
  },
};

// Markdown解析器，专门处理表格
interface ParsedElement {
  type: 'heading' | 'paragraph' | 'table' | 'list' | 'code' | 'blockquote';
  level?: number;
  content: string;
  rows?: string[][];
  isOrdered?: boolean;
}

const parseMarkdown = (content: string): ParsedElement[] => {
  const lines = content.split('\n');
  const elements: ParsedElement[] = [];
  let i = 0;

  while (i < lines.length) {
    const line = lines[i].trim();

    if (line === '') {
      i++;
      continue;
    }

    // 标题
    if (line.startsWith('#')) {
      const level = line.match(/^#+/)?.[0].length || 1;
      elements.push({
        type: 'heading',
        level,
        content: line.substring(level).trim(),
      });
      i++;
    }
    // 表格
    else if (line.includes('|') && lines[i + 1]?.includes('|') && lines[i + 1]?.includes('-')) {
      const tableRows: string[][] = [];

      // 解析表头
      const headerCells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
      tableRows.push(headerCells);

      i++; // 跳过分隔行
      i++; // 移动到数据行

      // 解析数据行
      while (i < lines.length && lines[i].includes('|')) {
        const rowCells = lines[i].split('|').map(cell => cell.trim()).filter(cell => cell !== '');
        if (rowCells.length > 0) {
          tableRows.push(rowCells);
        }
        i++;
      }

      elements.push({
        type: 'table',
        content: '',
        rows: tableRows,
      });
    }
    // 代码块
    else if (line.startsWith('```')) {
      let codeContent = '';
      i++; // 跳过开始标记
      while (i < lines.length && !lines[i].startsWith('```')) {
        codeContent += lines[i] + '\n';
        i++;
      }
      i++; // 跳过结束标记

      elements.push({
        type: 'code',
        content: codeContent.trim(),
      });
    }
    // 引用
    else if (line.startsWith('>')) {
      let quoteContent = line.substring(1).trim();
      i++;
      while (i < lines.length && lines[i].startsWith('>')) {
        quoteContent += '\n' + lines[i].substring(1).trim();
        i++;
      }

      elements.push({
        type: 'blockquote',
        content: quoteContent,
      });
    }
    // 列表
    else if (line.match(/^[\*\-\+]\s/) || line.match(/^\d+\.\s/)) {
      const isOrdered = line.match(/^\d+\.\s/) !== null;
      let listContent = line;
      i++;

      while (i < lines.length && (lines[i].match(/^[\*\-\+]\s/) || lines[i].match(/^\d+\.\s/) || lines[i].startsWith('  '))) {
        listContent += '\n' + lines[i];
        i++;
      }

      elements.push({
        type: 'list',
        content: listContent,
        isOrdered,
      });
    }
    // 普通段落
    else {
      let paragraphContent = line;
      i++;

      // 合并连续的非空行为一个段落
      while (i < lines.length && lines[i].trim() !== '' && !lines[i].startsWith('#') && !lines[i].includes('|') && !lines[i].startsWith('```') && !lines[i].startsWith('>') && !lines[i].match(/^[\*\-\+]\s/) && !lines[i].match(/^\d+\.\s/)) {
        paragraphContent += ' ' + lines[i].trim();
        i++;
      }

      elements.push({
        type: 'paragraph',
        content: paragraphContent,
      });
    }
  }

  return elements;
};

// 优化表格数据，确保所有行都有相同的列数
const normalizeTableData = (rows: string[][]): string[][] => {
  if (rows.length === 0) return rows;

  const maxCols = Math.max(...rows.map(row => row.length));

  return rows.map(row => {
    const normalizedRow = [...row];
    while (normalizedRow.length < maxCols) {
      normalizedRow.push('');
    }
    return normalizedRow.map(cell => cell.trim());
  });
};

// 计算表格列宽
const calculateColumnWidths = (rows: string[][]): number[] => {
  if (rows.length === 0) return [];

  const maxCols = Math.max(...rows.map(row => row.length));
  const columnWidths: number[] = new Array(maxCols).fill(0);

  // 计算每列的最大字符长度
  rows.forEach(row => {
    row.forEach((cell, colIndex) => {
      const cellLength = cell.length;
      if (cellLength > columnWidths[colIndex]) {
        columnWidths[colIndex] = cellLength;
      }
    });
  });

  // 转换为百分比，确保总和为100%
  const totalLength = columnWidths.reduce((sum, width) => sum + width, 0);
  if (totalLength === 0) {
    return new Array(maxCols).fill(100 / maxCols);
  }

  return columnWidths.map(width => Math.max((width / totalLength) * 100, 10)); // 最小10%
};

// 确保文字对比度的辅助函数
const ensureTextContrast = (backgroundColor: string, lightText: string = '#ffffff', darkText: string = '#2d3748') => {
  // 简单的对比度检查，深色背景用浅色文字，浅色背景用深色文字
  const bgLower = backgroundColor.toLowerCase();
  if (bgLower.includes('#000') || bgLower.includes('black') || bgLower.includes('dark')) {
    return lightText;
  }
  return darkText;
};

// 创建优化的HTML内容用于PDF导出
const createOptimizedHTML = (content: string, options: ExportOptions): string => {
  const elements = parseMarkdown(content);
  let html = '';

  for (const element of elements) {
    switch (element.type) {
      case 'heading':
        html += `<h${element.level}>${element.content}</h${element.level}>`;
        break;

      case 'table':
        if (element.rows && element.rows.length > 0) {
          const normalizedRows = normalizeTableData(element.rows);
          const columnWidths = calculateColumnWidths(normalizedRows);

          html += '<table class="export-table">';
          normalizedRows.forEach((row, index) => {
            const tag = index === 0 ? 'th' : 'td';
            html += '<tr>';
            row.forEach((cell, colIndex) => {
              const width = columnWidths[colIndex] || (100 / row.length);
              html += `<${tag} style="width: ${width.toFixed(1)}%">${cell || ''}</${tag}>`;
            });
            html += '</tr>';
          });
          html += '</table>';
        }
        break;

      case 'paragraph':
        // 处理内联格式
        let text = element.content
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.*?)\*/g, '<em>$1</em>')
          .replace(/`(.*?)`/g, '<code>$1</code>');
        html += `<p>${text}</p>`;
        break;

      case 'code':
        html += `<pre><code>${element.content}</code></pre>`;
        break;

      case 'blockquote':
        html += `<blockquote>${element.content}</blockquote>`;
        break;

      case 'list':
        const listItems = element.content.split('\n').filter(line => line.trim());
        const listTag = element.isOrdered ? 'ol' : 'ul';
        html += `<${listTag}>`;
        listItems.forEach(item => {
          const cleanItem = item.replace(/^[\*\-\+\d\.]\s*/, '');
          html += `<li>${cleanItem}</li>`;
        });
        html += `</${listTag}>`;
        break;
    }
  }

  return html;
};

// PDF导出
export const exportToPDF = async (content: string, htmlContent: string, options: ExportOptions = { format: 'pdf' }) => {
  try {
    // 使用优化的HTML内容
    const optimizedHTML = createOptimizedHTML(content, options);

    // 创建临时div来渲染HTML内容
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = optimizedHTML;
    tempDiv.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: 750px;
      padding: 40px;
      font-family: ${options.fontFamily || 'Arial, sans-serif'};
      font-size: ${options.fontSize || 14}px;
      line-height: 1.6;
      color: #333;
      background: white;
      box-sizing: border-box;
    `;
    
    // 添加样式
    if (options.includeStyles !== false) {
      tempDiv.style.cssText += `
        h1 { font-size: 24px; font-weight: bold; margin: 20px 0 10px 0; }
        h2 { font-size: 20px; font-weight: bold; margin: 18px 0 8px 0; }
        h3 { font-size: 16px; font-weight: bold; margin: 16px 0 6px 0; }
        p { margin: 8px 0; }
        ul, ol { margin: 8px 0; padding-left: 20px; }
        li { margin: 4px 0; }
        code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        blockquote { border-left: 4px solid #ddd; margin: 10px 0; padding-left: 16px; font-style: italic; }
        .export-table {
          border-collapse: collapse;
          width: 100%;
          margin: 24px 0;
          font-size: ${Math.max((options.fontSize || 14), 12)}px;
          font-family: ${options.fontFamily || 'Arial, "Microsoft YaHei", sans-serif'};
          table-layout: auto;
          break-inside: avoid;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        .export-table th,
        .export-table td {
          border: 1px solid ${tableThemes[options.tableTheme || 'modern'].borderColor};
          padding: 12px 16px;
          text-align: left;
          vertical-align: middle;
          word-wrap: break-word;
          overflow-wrap: break-word;
          hyphens: auto;
          line-height: 1.5;
          color: ${tableThemes[options.tableTheme || 'modern'].textColor};
        }
        .export-table th {
          background: ${tableThemes[options.tableTheme || 'modern'].headerBg};
          font-weight: 600;
          color: ${tableThemes[options.tableTheme || 'modern'].headerColor};
          text-align: center;
          font-size: ${Math.max((options.fontSize || 14), 12)}px;
          text-shadow: 0 1px 2px rgba(0,0,0,0.1);
          border-bottom: 2px solid ${tableThemes[options.tableTheme || 'modern'].borderColor};
        }
        .export-table tr:nth-child(even) td {
          background-color: ${tableThemes[options.tableTheme || 'modern'].evenRowBg};
        }
        .export-table tr:nth-child(odd) td {
          background-color: ${tableThemes[options.tableTheme || 'modern'].oddRowBg};
        }
        .export-table tr:hover td {
          background-color: ${tableThemes[options.tableTheme || 'modern'].hoverBg};
        }
        .export-table td {
          border-left: 1px solid #e1e5e9;
          border-right: 1px solid #e1e5e9;
        }
        .export-table tr:last-child td {
          border-bottom: 2px solid #667eea;
        }
        /* 确保表格在PDF中正确分页 */
        .export-table tr {
          page-break-inside: avoid;
        }
        /* 强制文字可见性 */
        .export-table td {
          color: ${tableThemes[options.tableTheme || 'modern'].textColor} !important;
          background-color: transparent;
        }
        .export-table tr:nth-child(even) td {
          background-color: ${tableThemes[options.tableTheme || 'modern'].evenRowBg} !important;
          color: ${tableThemes[options.tableTheme || 'modern'].textColor} !important;
        }
        .export-table tr:nth-child(odd) td {
          background-color: ${tableThemes[options.tableTheme || 'modern'].oddRowBg} !important;
          color: ${tableThemes[options.tableTheme || 'modern'].textColor} !important;
        }
        /* 数字列右对齐 */
        .export-table td:has-text(/^\d+$/) {
          text-align: right;
        }
        /* 防止任何黑色背景 */
        .export-table * {
          background-color: inherit !important;
        }
      `;
    }
    
    document.body.appendChild(tempDiv);
    
    // 使用html2canvas转换为图片，优化表格渲染
    const canvas = await html2canvas(tempDiv, {
      scale: 3, // 提高分辨率
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: 750,
      height: tempDiv.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: 750,
      windowHeight: tempDiv.scrollHeight,
    });
    
    document.body.removeChild(tempDiv);
    
    // 创建PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: options.pageSize?.toLowerCase() || 'a4',
    });
    
    const imgData = canvas.toDataURL('image/png');
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 295; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    
    let position = 0;
    
    // 添加页面
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;
    
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }
    
    // 添加元数据
    if (options.title) {
      pdf.setProperties({
        title: options.title,
        author: options.author || 'Martetdown Editor',
        creator: 'Martetdown Editor',
      });
    }
    
    // 保存文件
    const fileName = `${options.title || 'document'}.pdf`;
    pdf.save(fileName);
    
    return true;
  } catch (error) {
    console.error('PDF export failed:', error);
    throw new Error('PDF导出失败');
  }
};

// Word导出
export const exportToWord = async (content: string, options: ExportOptions = { format: 'docx' }) => {
  try {
    const elements = parseMarkdown(content);
    const documentChildren: any[] = [];

    for (const element of elements) {
      switch (element.type) {
        case 'heading':
          const headingLevel = element.level === 1 ? HeadingLevel.HEADING_1 :
                              element.level === 2 ? HeadingLevel.HEADING_2 :
                              element.level === 3 ? HeadingLevel.HEADING_3 :
                              element.level === 4 ? HeadingLevel.HEADING_4 :
                              element.level === 5 ? HeadingLevel.HEADING_5 :
                              HeadingLevel.HEADING_6;

          documentChildren.push(new Paragraph({
            text: element.content,
            heading: headingLevel,
          }));
          break;

        case 'table':
          if (element.rows && element.rows.length > 0) {
            const normalizedRows = normalizeTableData(element.rows);
            const columnWidths = calculateColumnWidths(normalizedRows);
            const maxCols = normalizedRows[0]?.length || 0;

            const tableRows = normalizedRows.map((row, rowIndex) => {
              const cells = row.map((cellText, colIndex) => {
                // 根据内容长度计算列宽
                const widthPercent = columnWidths[colIndex] || (100 / maxCols);
                const colWidth = Math.floor((9000 * widthPercent) / 100); // 转换为DXA单位

                return new TableCell({
                  children: [new Paragraph({
                    children: [new TextRun({
                      text: cellText || '',
                      bold: rowIndex === 0, // 表头加粗
                      color: rowIndex === 0 ? 'FFFFFF' : '2D3748', // 表头白色文字，内容深灰色
                      size: rowIndex === 0 ? (options.fontSize || 14) * 2 : (options.fontSize || 14) * 2 - 2,
                    })],
                    spacing: {
                      before: 120,
                      after: 120,
                    },
                    alignment: rowIndex === 0 ? 'center' : 'left', // 表头居中
                  })],
                  width: {
                    size: Math.max(colWidth, 1000), // 最小宽度
                    type: WidthType.DXA,
                  },
                  margins: {
                    top: 120,
                    bottom: 120,
                    left: 150,
                    right: 150,
                  },
                  verticalAlign: 'center',
                  shading: rowIndex === 0 ? {
                    fill: tableThemes[options.tableTheme || 'modern'].wordHeaderBg,
                    type: 'solid',
                  } : rowIndex % 2 === 0 ? {
                    fill: tableThemes[options.tableTheme || 'modern'].wordEvenBg,
                    type: 'solid',
                  } : {
                    fill: tableThemes[options.tableTheme || 'modern'].wordOddBg,
                    type: 'solid',
                  },
                });
              });

              return new TableRow({
                children: cells,
                tableHeader: rowIndex === 0,
                height: {
                  value: 400, // 最小行高
                  rule: 'atLeast',
                },
              });
            });

            documentChildren.push(new Table({
              rows: tableRows,
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              borders: {
                top: { style: 'single', size: 8, color: '4A90E2' },
                bottom: { style: 'single', size: 8, color: '4A90E2' },
                left: { style: 'single', size: 6, color: '4A90E2' },
                right: { style: 'single', size: 6, color: '4A90E2' },
                insideHorizontal: { style: 'single', size: 3, color: 'E1E5E9' },
                insideVertical: { style: 'single', size: 3, color: 'E1E5E9' },
              },
              layout: 'fixed', // 固定表格布局
            }));

            // 在表格后添加空行
            documentChildren.push(new Paragraph({ text: '' }));
          }
          break;

        case 'code':
          documentChildren.push(new Paragraph({
            children: [new TextRun({
              text: element.content,
              font: 'Courier New',
              size: (options.fontSize || 14) * 2 - 4, // Word uses half-points
            })],
            style: 'Code',
          }));
          break;

        case 'blockquote':
          documentChildren.push(new Paragraph({
            text: element.content,
            style: 'Quote',
          }));
          break;

        case 'list':
          // 简单处理列表项
          const listItems = element.content.split('\n').filter(line => line.trim());
          for (const item of listItems) {
            const cleanItem = item.replace(/^[\*\-\+\d\.]\s*/, '');
            documentChildren.push(new Paragraph({
              text: `• ${cleanItem}`,
            }));
          }
          break;

        case 'paragraph':
          // 处理粗体和斜体
          const textRuns: TextRun[] = [];
          let text = element.content;

          // 简单的格式处理
          const parts = text.split(/(\*\*.*?\*\*|\*.*?\*|`.*?`)/);

          for (const part of parts) {
            if (part.startsWith('**') && part.endsWith('**')) {
              textRuns.push(new TextRun({
                text: part.slice(2, -2),
                bold: true,
              }));
            } else if (part.startsWith('*') && part.endsWith('*')) {
              textRuns.push(new TextRun({
                text: part.slice(1, -1),
                italics: true,
              }));
            } else if (part.startsWith('`') && part.endsWith('`')) {
              textRuns.push(new TextRun({
                text: part.slice(1, -1),
                font: 'Courier New',
              }));
            } else if (part) {
              textRuns.push(new TextRun({ text: part }));
            }
          }

          documentChildren.push(new Paragraph({
            children: textRuns.length > 0 ? textRuns : [new TextRun({ text: element.content })],
          }));
          break;
      }
    }
    
    const doc = new Document({
      sections: [{
        properties: {},
        children: documentChildren,
      }],
      creator: 'Martetdown Editor',
      title: options.title || 'Document',
      description: 'Generated by Martetdown Editor',
      styles: {
        paragraphStyles: [
          {
            id: 'Code',
            name: 'Code',
            basedOn: 'Normal',
            next: 'Normal',
            run: {
              font: 'Courier New',
              size: (options.fontSize || 14) * 2 - 4,
            },
            paragraph: {
              spacing: {
                before: 200,
                after: 200,
              },
            },
          },
          {
            id: 'Quote',
            name: 'Quote',
            basedOn: 'Normal',
            next: 'Normal',
            run: {
              italics: true,
              color: '666666',
            },
            paragraph: {
              spacing: {
                before: 200,
                after: 200,
              },
              indent: {
                left: 720, // 0.5 inch
              },
            },
          },
        ],
      },
    });
    
    const blob = await Packer.toBlob(doc);
    const fileName = `${options.title || 'document'}.docx`;
    saveAs(blob, fileName);
    
    return true;
  } catch (error) {
    console.error('Word export failed:', error);
    throw new Error('Word导出失败');
  }
};

// HTML导出
export const exportToHTML = (content: string, htmlContent: string, options: ExportOptions = { format: 'html' }) => {
  try {
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${options.title || 'Document'}</title>
    <style>
        body {
            font-family: ${options.fontFamily || 'Arial, sans-serif'};
            font-size: ${options.fontSize || 14}px;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: white;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 12px;
        }
        h1 { font-size: 2em; border-bottom: 2px solid #eee; padding-bottom: 8px; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 4px; }
        h3 { font-size: 1.25em; }
        p { margin: 12px 0; }
        ul, ol { margin: 12px 0; padding-left: 24px; }
        li { margin: 4px 0; }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        pre {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        pre code {
            background: none;
            padding: 0;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 16px 0;
            padding-left: 16px;
            color: #666;
            font-style: italic;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 24px 0;
            font-size: ${options.fontSize || 14}px;
            font-family: ${options.fontFamily || 'Arial, "Microsoft YaHei", sans-serif'};
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 12px;
            overflow: hidden;
            background: #ffffff;
        }
        th, td {
            border: none;
            padding: 16px 20px;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
            position: relative;
            color: ${tableThemes[options.tableTheme || 'modern'].textColor};
        }
        th {
            background: ${tableThemes[options.tableTheme || 'modern'].headerBg};
            font-weight: 600;
            color: ${tableThemes[options.tableTheme || 'modern'].headerColor};
            text-align: center;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            font-size: ${(options.fontSize || 14) + 1}px;
            letter-spacing: 0.5px;
        }
        td {
            border-bottom: 1px solid ${tableThemes[options.tableTheme || 'modern'].borderColor};
            transition: all 0.2s ease;
        }
        tr:nth-child(even) td {
            background-color: ${tableThemes[options.tableTheme || 'modern'].evenRowBg};
        }
        tr:nth-child(odd) td {
            background-color: ${tableThemes[options.tableTheme || 'modern'].oddRowBg};
        }
        tr:hover td {
            background-color: ${tableThemes[options.tableTheme || 'modern'].hoverBg} !important;
            transform: scale(1.01);
        }
        tr:last-child td {
            border-bottom: 3px solid #667eea;
        }
        /* 第一列加粗 */
        td:first-child {
            font-weight: 600;
            color: #2d3748;
        }
        /* 响应式表格 */
        @media (max-width: 768px) {
            table {
                font-size: 12px;
                margin: 16px 0;
            }
            th, td {
                padding: 12px 8px;
            }
        }
        @media print {
            table {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            tr:hover td {
                background-color: inherit !important;
                transform: none;
            }
        }
        a {
            color: #3498db;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        @media print {
            body { margin: 0; padding: 20px; }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
    
    const blob = new Blob([htmlTemplate], { type: 'text/html;charset=utf-8' });
    const fileName = `${options.title || 'document'}.html`;
    saveAs(blob, fileName);
    
    return true;
  } catch (error) {
    console.error('HTML export failed:', error);
    throw new Error('HTML导出失败');
  }
};

// 纯文本导出
export const exportToText = (content: string, options: ExportOptions = { format: 'txt' }) => {
  try {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const fileName = `${options.title || 'document'}.txt`;
    saveAs(blob, fileName);
    
    return true;
  } catch (error) {
    console.error('Text export failed:', error);
    throw new Error('文本导出失败');
  }
};
