'use client';

import React from 'react';

interface TableThemePreviewProps {
  theme: 'professional' | 'modern' | 'minimal' | 'colorful';
  className?: string;
}

const themeStyles = {
  professional: {
    headerBg: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
    headerColor: '#ffffff',
    evenRowBg: '#f8f9fa',
    oddRowBg: '#ffffff',
    borderColor: '#bdc3c7',
  },
  modern: {
    headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    headerColor: '#ffffff',
    evenRowBg: '#f8fafc',
    oddRowBg: '#ffffff',
    borderColor: '#e1e5e9',
  },
  minimal: {
    headerBg: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
    headerColor: '#ffffff',
    evenRowBg: '#fdfdfd',
    oddRowBg: '#ffffff',
    borderColor: '#ecf0f1',
  },
  colorful: {
    headerBg: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
    headerColor: '#ffffff',
    evenRowBg: '#fff5f5',
    oddRowBg: '#ffffff',
    borderColor: '#fab1a0',
  },
};

export const TableThemePreview: React.FC<TableThemePreviewProps> = ({ 
  theme, 
  className = '' 
}) => {
  const style = themeStyles[theme];
  
  return (
    <div className={`overflow-hidden rounded-lg shadow-sm ${className}`}>
      <table className="w-full text-xs border-collapse">
        <thead>
          <tr>
            <th 
              className="px-3 py-2 text-center font-semibold border"
              style={{ 
                background: style.headerBg,
                color: style.headerColor,
                borderColor: style.borderColor,
              }}
            >
              姓名
            </th>
            <th 
              className="px-3 py-2 text-center font-semibold border"
              style={{ 
                background: style.headerBg,
                color: style.headerColor,
                borderColor: style.borderColor,
              }}
            >
              职位
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td 
              className="px-3 py-1 border text-center"
              style={{ 
                backgroundColor: style.oddRowBg,
                borderColor: style.borderColor,
              }}
            >
              张三
            </td>
            <td 
              className="px-3 py-1 border text-center"
              style={{ 
                backgroundColor: style.oddRowBg,
                borderColor: style.borderColor,
              }}
            >
              工程师
            </td>
          </tr>
          <tr>
            <td 
              className="px-3 py-1 border text-center"
              style={{ 
                backgroundColor: style.evenRowBg,
                borderColor: style.borderColor,
              }}
            >
              李四
            </td>
            <td 
              className="px-3 py-1 border text-center"
              style={{ 
                backgroundColor: style.evenRowBg,
                borderColor: style.borderColor,
              }}
            >
              设计师
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};
