"""
PDF格式转换器
"""

import os
import tempfile
from typing import Optional
import PyPDF2
import pdfplumber

from .base_converter import BaseConverter
from ..exceptions import ConversionError


class PDFConverter(BaseConverter):
    """PDF转换器"""
    
    @property
    def supported_input_formats(self) -> list:
        return ['pdf']
    
    @property
    def supported_output_formats(self) -> list:
        return ['docx', 'xlsx', 'txt', 'md']
    
    def convert_to_pdf(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        PDF转PDF（复制文件）
        """
        try:
            import shutil
            shutil.copy2(input_path, output_path)
            return os.path.exists(output_path)
        except Exception as e:
            self.logger.error(f"PDF复制失败: {str(e)}")
            return False
    
    def convert_to_docx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        PDF转Word
        """
        try:
            from docx import Document
            
            # 提取PDF文本
            text_content = self._extract_text_from_pdf(input_path)
            
            if not text_content:
                self.logger.warning("PDF中未提取到文本内容")
                return False
            
            # 创建Word文档
            doc = Document()
            
            # 按段落分割文本
            paragraphs = text_content.split('\n\n')
            
            for para_text in paragraphs:
                para_text = para_text.strip()
                if para_text:
                    # 检测是否为标题（简单启发式）
                    if self._is_likely_heading(para_text):
                        doc.add_heading(para_text, level=1)
                    else:
                        doc.add_paragraph(para_text)
            
            # 保存文档
            doc.save(output_path)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"PDF转Word失败: {str(e)}")
            return False
    
    def convert_to_xlsx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        PDF转Excel
        尝试提取表格数据
        """
        try:
            import pandas as pd
            
            # 使用pdfplumber提取表格
            tables = self._extract_tables_from_pdf(input_path)
            
            if not tables:
                # 如果没有表格，将文本内容放入Excel
                text_content = self._extract_text_from_pdf(input_path)
                if text_content:
                    df = pd.DataFrame({'Content': [text_content]})
                    df.to_excel(output_path, index=False)
                    return True
                else:
                    return False
            
            # 创建Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for i, table in enumerate(tables):
                    if table:
                        df = pd.DataFrame(table)
                        sheet_name = f'Table_{i+1}' if len(tables) > 1 else 'Sheet1'
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"PDF转Excel失败: {str(e)}")
            return False
    
    def convert_to_txt(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        PDF转纯文本
        """
        try:
            # 提取文本
            text_content = self._extract_text_from_pdf(input_path)
            
            if not text_content:
                self.logger.warning("PDF中未提取到文本内容")
                return False
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"PDF转文本失败: {str(e)}")
            return False
    
    def convert_to_md(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        PDF转Markdown
        """
        try:
            # 提取文本
            text_content = self._extract_text_from_pdf(input_path)
            
            if not text_content:
                self.logger.warning("PDF中未提取到文本内容")
                return False
            
            # 转换为Markdown格式
            markdown_content = self._convert_text_to_markdown(text_content)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"PDF转Markdown失败: {str(e)}")
            return False
    
    def _extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        从PDF提取文本
        """
        text_content = ""
        
        try:
            # 首先尝试使用pdfplumber（更好的文本提取）
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n\n"
            
            if text_content.strip():
                return text_content.strip()
            
        except Exception as e:
            self.logger.warning(f"pdfplumber提取失败，尝试PyPDF2: {str(e)}")
        
        try:
            # 备用方法：使用PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n\n"
            
            return text_content.strip()
            
        except Exception as e:
            self.logger.error(f"PyPDF2提取也失败: {str(e)}")
            return ""
    
    def _extract_tables_from_pdf(self, pdf_path: str) -> list:
        """
        从PDF提取表格
        """
        tables = []
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_tables = page.extract_tables()
                    if page_tables:
                        tables.extend(page_tables)
            
            return tables
            
        except Exception as e:
            self.logger.error(f"表格提取失败: {str(e)}")
            return []
    
    def _is_likely_heading(self, text: str) -> bool:
        """
        判断文本是否可能是标题
        """
        # 简单的启发式规则
        text = text.strip()
        
        # 短文本且不以句号结尾
        if len(text) < 100 and not text.endswith('.'):
            return True
        
        # 全大写文本
        if text.isupper() and len(text) < 50:
            return True
        
        # 包含章节编号
        if any(keyword in text.lower() for keyword in ['chapter', '章', '第', 'section']):
            return True
        
        return False
    
    def _convert_text_to_markdown(self, text: str) -> str:
        """
        将纯文本转换为Markdown格式
        """
        lines = text.split('\n')
        markdown_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                markdown_lines.append('')
                continue
            
            # 检测标题
            if self._is_likely_heading(line):
                markdown_lines.append(f"# {line}")
            else:
                markdown_lines.append(line)
        
        return '\n'.join(markdown_lines)
