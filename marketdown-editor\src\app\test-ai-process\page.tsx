'use client';

import { useState, useEffect } from 'react';
import { ProcessingSteps } from '@/components/AIAssistant/ProcessingSteps';

export default function TestAIProcess() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);

  const simulateAIProcess = async () => {
    setIsProcessing(true);
    setProgress(0);

    // 步骤1：初始化
    setCurrentStep('正在初始化AI请求...');
    for (let i = 0; i <= 25; i += 5) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 步骤2：分析内容
    setCurrentStep('正在分析文档内容...');
    for (let i = 25; i <= 50; i += 5) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // 步骤3：AI处理
    setCurrentStep('AI正在生成优化内容...');
    for (let i = 50; i <= 80; i += 5) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 400));
    }

    // 步骤4：应用结果
    setCurrentStep('正在应用处理结果...');
    for (let i = 80; i <= 95; i += 5) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 步骤5：完成
    setCurrentStep('处理完成！');
    setProgress(100);
    await new Promise(resolve => setTimeout(resolve, 1000));

    setIsProcessing(false);
    setCurrentStep('');
    setProgress(0);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            🤖 AI处理过程演示
          </h1>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-800 mb-2">✨ 新功能特点</h2>
            <ul className="text-blue-700 space-y-1">
              <li>• <strong>可视化进度</strong>：实时显示AI处理的每个步骤</li>
              <li>• <strong>详细状态</strong>：清楚展示当前正在执行的操作</li>
              <li>• <strong>进度条动画</strong>：流畅的进度条和状态转换</li>
              <li>• <strong>步骤说明</strong>：每个阶段都有详细的说明文字</li>
              <li>• <strong>美观界面</strong>：渐变色彩和动画效果</li>
            </ul>
          </div>

          <div className="flex gap-4 mb-6">
            <button
              onClick={simulateAIProcess}
              disabled={isProcessing}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                isProcessing
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {isProcessing ? '处理中...' : '开始AI处理演示'}
            </button>
            
            {!isProcessing && (
              <button
                onClick={() => {
                  setIsProcessing(true);
                  setCurrentStep('正在准备AI请求...');
                  setProgress(45);
                }}
                className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-md font-medium transition-colors"
              >
                显示中间状态
              </button>
            )}
          </div>
        </div>

        {/* 处理过程显示 */}
        <ProcessingSteps 
          currentStep={currentStep}
          progress={progress}
          isVisible={isProcessing}
        />

        {/* 功能说明 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🎯 处理步骤说明
            </h2>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold">1</div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">初始化阶段</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">准备AI请求，验证内容格式</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold">2</div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">内容分析</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">理解文档结构，分析语义内容</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold">3</div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">AI处理</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">生成优化建议，处理内容</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-bold">4</div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">应用结果</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">整理AI输出，应用到文档</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600">✓</div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">完成</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">处理完成，内容已优化</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🎨 界面特性
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">动态进度条</h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  渐变色彩的进度条，带有流光动画效果
                </p>
              </div>
              
              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <h3 className="font-medium text-green-900 dark:text-green-100 mb-1">状态指示</h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  每个步骤都有清晰的图标和状态显示
                </p>
              </div>
              
              <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-1">实时反馈</h3>
                <p className="text-sm text-purple-700 dark:text-purple-300">
                  实时更新处理状态和进度信息
                </p>
              </div>
              
              <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <h3 className="font-medium text-orange-900 dark:text-orange-100 mb-1">智能提示</h3>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  根据进度显示不同的提示信息
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 技术实现 */}
        <div className="mt-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            🔧 技术实现
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">
                ⚛️
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">React组件</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                模块化的ProcessingSteps组件
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">
                🎨
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">Tailwind CSS</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                响应式设计和动画效果
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">
                ⚡
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">TypeScript</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                类型安全的状态管理
              </p>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            📖 在主应用中的使用
          </h2>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <span className="text-2xl">1️⃣</span>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">打开AI助手</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  在主编辑器中点击"AI助手"按钮
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <span className="text-2xl">2️⃣</span>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">选择功能</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  选择语法检查、风格调整、翻译等功能
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <span className="text-2xl">3️⃣</span>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">观看处理过程</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  实时查看AI处理的详细步骤和进度
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
