// Markdown语法工具函数

export interface MarkdownAction {
  name: string;
  icon: string;
  shortcut?: string;
  action: (textarea: HTMLTextAreaElement) => void;
}

// 插入文本到光标位置
export const insertText = (textarea: HTMLTextAreaElement, text: string, selectText = false) => {
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const beforeText = textarea.value.substring(0, start);
  const afterText = textarea.value.substring(end);

  textarea.value = beforeText + text + afterText;
  
  if (selectText) {
    textarea.setSelectionRange(start, start + text.length);
  } else {
    textarea.setSelectionRange(start + text.length, start + text.length);
  }
  
  textarea.focus();
  textarea.dispatchEvent(new Event('input', { bubbles: true }));
};

// 包装选中文本
export const wrapText = (
  textarea: HTMLTextAreaElement, 
  before: string, 
  after: string = before,
  placeholder = ''
) => {
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const selectedText = textarea.value.substring(start, end);
  const beforeText = textarea.value.substring(0, start);
  const afterText = textarea.value.substring(end);

  const textToWrap = selectedText || placeholder;
  const newText = beforeText + before + textToWrap + after + afterText;
  
  textarea.value = newText;
  
  if (!selectedText && placeholder) {
    // 如果没有选中文本且有占位符，选中占位符
    textarea.setSelectionRange(start + before.length, start + before.length + placeholder.length);
  } else {
    // 否则将光标放在包装文本之后
    textarea.setSelectionRange(start + before.length + textToWrap.length + after.length, start + before.length + textToWrap.length + after.length);
  }
  
  textarea.focus();
  textarea.dispatchEvent(new Event('input', { bubbles: true }));
};

// 在行首插入文本
export const insertAtLineStart = (textarea: HTMLTextAreaElement, prefix: string) => {
  const start = textarea.selectionStart;
  const value = textarea.value;
  
  // 找到当前行的开始位置
  let lineStart = start;
  while (lineStart > 0 && value[lineStart - 1] !== '\n') {
    lineStart--;
  }
  
  // 检查是否已经有前缀
  const lineContent = value.substring(lineStart, value.indexOf('\n', lineStart) || value.length);
  if (lineContent.startsWith(prefix)) {
    // 如果已经有前缀，移除它
    const beforeLine = value.substring(0, lineStart);
    const afterPrefix = value.substring(lineStart + prefix.length);
    textarea.value = beforeLine + afterPrefix;
    textarea.setSelectionRange(start - prefix.length, start - prefix.length);
  } else {
    // 如果没有前缀，添加它
    const beforeLine = value.substring(0, lineStart);
    const afterLine = value.substring(lineStart);
    textarea.value = beforeLine + prefix + afterLine;
    textarea.setSelectionRange(start + prefix.length, start + prefix.length);
  }
  
  textarea.focus();
  textarea.dispatchEvent(new Event('input', { bubbles: true }));
};

// 插入表格
export const insertTable = (textarea: HTMLTextAreaElement, rows = 3, cols = 3) => {
  let table = '\n';
  
  // 表头
  table += '|';
  for (let i = 0; i < cols; i++) {
    table += ` Header ${i + 1} |`;
  }
  table += '\n';
  
  // 分隔符
  table += '|';
  for (let i = 0; i < cols; i++) {
    table += ' --- |';
  }
  table += '\n';
  
  // 数据行
  for (let i = 0; i < rows - 1; i++) {
    table += '|';
    for (let j = 0; j < cols; j++) {
      table += ` Cell ${i + 1}-${j + 1} |`;
    }
    table += '\n';
  }
  
  table += '\n';
  insertText(textarea, table);
};

// 插入链接
export const insertLink = (textarea: HTMLTextAreaElement) => {
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const selectedText = textarea.value.substring(start, end);
  
  if (selectedText) {
    wrapText(textarea, '[', '](url)', selectedText);
  } else {
    wrapText(textarea, '[', '](url)', 'link text');
  }
};

// 插入图片
export const insertImage = (textarea: HTMLTextAreaElement) => {
  wrapText(textarea, '![', '](image-url)', 'alt text');
};

// 插入代码块
export const insertCodeBlock = (textarea: HTMLTextAreaElement, language = '') => {
  const codeBlock = `\n\`\`\`${language}\ncode here\n\`\`\`\n`;
  insertText(textarea, codeBlock);
};

// Markdown操作定义
export const markdownActions: MarkdownAction[] = [
  {
    name: 'Bold',
    icon: 'B',
    shortcut: 'Ctrl+B',
    action: (textarea) => wrapText(textarea, '**', '**', 'bold text'),
  },
  {
    name: 'Italic',
    icon: 'I',
    shortcut: 'Ctrl+I',
    action: (textarea) => wrapText(textarea, '*', '*', 'italic text'),
  },
  {
    name: 'Strikethrough',
    icon: 'S',
    action: (textarea) => wrapText(textarea, '~~', '~~', 'strikethrough text'),
  },
  {
    name: 'Heading 1',
    icon: 'H1',
    action: (textarea) => insertAtLineStart(textarea, '# '),
  },
  {
    name: 'Heading 2',
    icon: 'H2',
    action: (textarea) => insertAtLineStart(textarea, '## '),
  },
  {
    name: 'Heading 3',
    icon: 'H3',
    action: (textarea) => insertAtLineStart(textarea, '### '),
  },
  {
    name: 'Unordered List',
    icon: 'UL',
    action: (textarea) => insertAtLineStart(textarea, '- '),
  },
  {
    name: 'Ordered List',
    icon: 'OL',
    action: (textarea) => insertAtLineStart(textarea, '1. '),
  },
  {
    name: 'Quote',
    icon: 'Q',
    action: (textarea) => insertAtLineStart(textarea, '> '),
  },
  {
    name: 'Code',
    icon: 'C',
    action: (textarea) => wrapText(textarea, '`', '`', 'code'),
  },
  {
    name: 'Code Block',
    icon: 'CB',
    action: (textarea) => insertCodeBlock(textarea),
  },
  {
    name: 'Link',
    icon: 'L',
    shortcut: 'Ctrl+K',
    action: insertLink,
  },
  {
    name: 'Image',
    icon: 'IMG',
    action: insertImage,
  },
  {
    name: 'Table',
    icon: 'T',
    action: (textarea) => insertTable(textarea),
  },
  {
    name: 'Horizontal Rule',
    icon: 'HR',
    action: (textarea) => insertText(textarea, '\n---\n'),
  },
];

// 获取文档统计信息
export const getDocumentStats = (content: string) => {
  const words = content.trim() ? content.trim().split(/\s+/).length : 0;
  const characters = content.length;
  const charactersNoSpaces = content.replace(/\s/g, '').length;
  const lines = content.split('\n').length;
  const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim()).length;
  
  // 估算阅读时间（假设每分钟200词）
  const readingTime = Math.ceil(words / 200);
  
  return {
    words,
    characters,
    charactersNoSpaces,
    lines,
    paragraphs,
    readingTime,
  };
};
