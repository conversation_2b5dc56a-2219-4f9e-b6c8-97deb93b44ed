'use client';

import React from 'react';
import { useEditorStore } from '@/stores/useEditorStore';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {
  const { editorConfig, setEditorConfig } = useEditorStore();

  const toggleTheme = () => {
    const newTheme = editorConfig.theme === 'light' ? 'dark' : 'light';
    setEditorConfig({ theme: newTheme });
    
    // 更新HTML类名以应用主题
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  return (
    <div className={`min-h-screen bg-white dark:bg-gray-900 transition-colors ${className}`}>
      {/* 顶部导航栏 */}
      <header className="h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            Martetdown
          </h1>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Markdown Editor
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={toggleTheme}
            className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            title={`Switch to ${editorConfig.theme === 'light' ? 'dark' : 'light'} theme`}
          >
            {editorConfig.theme === 'light' ? '🌙' : '☀️'}
          </button>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="h-[calc(100vh-3.5rem)]">
        {children}
      </main>
    </div>
  );
};
