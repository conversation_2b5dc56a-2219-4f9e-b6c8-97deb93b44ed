'use client';

import { useState } from 'react';
import { PreciseFormatPanel } from '@/components/AIAssistant/PreciseFormatPanel';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const sampleContent = `# 人工智能在教育领域的应用研究

## 摘要
本文探讨了人工智能技术在现代教育中的应用现状和发展前景。通过分析当前AI教育工具的特点，我们发现AI技术能够显著提升教学效率和学习体验。

## 1 引言
随着科技的快速发展，人工智能已经成为推动教育变革的重要力量。据统计，全球AI教育市场预计将在2025年达到200亿美元的规模。

### 1.1 研究背景
教育行业面临着个性化学习需求增长、师资力量不足等挑战。AI技术的引入为解决这些问题提供了新的思路。

### 1.2 研究目的
本研究旨在：
1. 分析AI在教育中的应用现状
2. 探讨AI教育工具的优势和局限性
3. 提出未来发展建议

## 2 AI教育应用现状

### 2.1 智能辅导系统
目前市场上的智能辅导系统主要包括：
- **自适应学习平台**：根据学生能力调整学习内容
- **智能答疑系统**：24小时在线解答学生问题
- **学习分析工具**：追踪学习进度，提供个性化建议

### 2.2 应用效果分析

| 应用领域 | 提升效果 | 用户满意度 | 普及率 |
|----------|----------|------------|--------|
| 语言学习 | 35% | 4.2/5 | 68% |
| 数学辅导 | 42% | 4.5/5 | 55% |
| 编程教育 | 28% | 4.1/5 | 45% |

## 3 技术优势与挑战

### 3.1 主要优势
1. **个性化学习**：AI能够根据每个学生的学习特点制定专属学习计划
2. **即时反馈**：学生可以立即获得学习反馈，及时调整学习策略
3. **资源优化**：AI帮助教师更好地分配教学资源

### 3.2 面临挑战
- 数据隐私保护问题
- 技术成本较高
- 教师接受度有待提升

## 4 未来发展趋势

根据专家预测，AI教育将朝着以下方向发展：

> "未来的AI教育将更加注重情感计算和社交学习，不仅关注知识传授，更关注学生的全面发展。" —— 教育技术专家李明（2024）

### 4.1 技术发展方向
- 多模态交互技术
- 情感识别与响应
- 虚拟现实结合

### 4.2 应用场景扩展
预计到2030年，AI教育将覆盖：
- K-12基础教育：覆盖率达到80%
- 高等教育：覆盖率达到90%
- 职业培训：覆盖率达到75%

## 5 结论

AI技术在教育领域的应用前景广阔，但仍需要在技术完善、政策支持、教师培训等方面持续努力。只有这样，才能真正实现AI赋能教育的目标。

## 参考文献

1. 张三, 李四. 人工智能教育应用研究[J]. 教育技术学报, 2024, 15(2): 23-35.
2. Smith, J. AI in Education: Current Trends and Future Prospects[J]. Educational Technology Review, 2024, 28(3): 45-62.
3. 王五. 智能教育系统设计与实现[M]. 北京: 教育科学出版社, 2024.

---

**注：** 本文档格式需要优化，包括标题层级、表格样式、引用格式等方面。`;

export default function TestPreciseFormat() {
  const [content, setContent] = useState(sampleContent);
  const [showFormatPanel, setShowFormatPanel] = useState(false);
  const [originalContent, setOriginalContent] = useState(sampleContent);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const resetContent = () => {
    setContent(originalContent);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            🎯 精准格式优化测试
          </h1>
          
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-purple-800 mb-2">🚀 革命性格式优化</h2>
            <ul className="text-purple-700 space-y-1">
              <li>• <strong>告别模糊指令</strong>：不再使用"优化格式"这类模糊表述</li>
              <li>• <strong>具体明确标准</strong>：提供详细的格式规范和实施细节</li>
              <li>• <strong>专业文档标准</strong>：学术论文、商务报告、简历等专业标准</li>
              <li>• <strong>AI自检验证</strong>：优化后自动检查是否符合标准</li>
              <li>• <strong>达到完美状态</strong>：真正实现"完美格式"的目标</li>
            </ul>
          </div>

          <div className="flex gap-4 mb-6">
            <button
              onClick={() => setShowFormatPanel(true)}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-md font-medium transition-colors"
            >
              🎯 开始精准格式优化
            </button>
            
            <button
              onClick={resetContent}
              className="px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-md font-medium transition-colors"
            >
              重置内容
            </button>
          </div>
        </div>

        {/* 对比说明 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              ❌ 传统格式优化问题
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <h3 className="font-medium text-red-800">模糊指令</h3>
                <p className="text-sm text-red-600">"请优化格式" - AI不知道具体要做什么</p>
              </div>
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <h3 className="font-medium text-red-800">标准不明</h3>
                <p className="text-sm text-red-600">没有明确的格式标准和规范</p>
              </div>
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <h3 className="font-medium text-red-800">效果不佳</h3>
                <p className="text-sm text-red-600">优化结果不符合预期，需要反复调整</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              ✅ 精准格式优化优势
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h3 className="font-medium text-green-800">具体明确</h3>
                <p className="text-sm text-green-600">详细的格式规范，每个要求都可操作</p>
              </div>
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h3 className="font-medium text-green-800">专业标准</h3>
                <p className="text-sm text-green-600">基于学术、商务等专业文档标准</p>
              </div>
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h3 className="font-medium text-green-800">自动检验</h3>
                <p className="text-sm text-green-600">AI自动检查优化结果是否符合标准</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 编辑器 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              📝 文档编辑器
            </h2>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 font-mono text-sm resize-none"
              placeholder="输入需要优化格式的文档内容..."
            />
          </div>

          {/* 预览 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              👀 预览效果
            </h2>
            <div className="h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-700">
              <div className="prose dark:prose-invert max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>

        {/* 格式标准说明 */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            📋 支持的格式标准
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-2xl">🎓</span>
                <h3 className="font-medium text-blue-900 dark:text-blue-100">学术论文</h3>
              </div>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• 三号宋体加粗标题</li>
                <li>• 五号宋体正文</li>
                <li>• 1.5倍行间距</li>
                <li>• 规范的引用格式</li>
                <li>• 表格图片标准化</li>
              </ul>
            </div>
            
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-2xl">💼</span>
                <h3 className="font-medium text-green-900 dark:text-green-100">商务报告</h3>
              </div>
              <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• 微软雅黑字体</li>
                <li>• 蓝色主题配色</li>
                <li>• 数据突出显示</li>
                <li>• 现代表格样式</li>
                <li>• 专业排版布局</li>
              </ul>
            </div>
            
            <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-2xl">📄</span>
                <h3 className="font-medium text-purple-900 dark:text-purple-100">简历文档</h3>
              </div>
              <ul className="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                <li>• 简洁专业布局</li>
                <li>• 模块化结构</li>
                <li>• 重点信息突出</li>
                <li>• 统一时间格式</li>
                <li>• 技能标签展示</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 使用流程 */}
        <div className="mt-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            🔄 使用流程
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">1</div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">选择标准</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">选择适合的文档格式标准</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">2</div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">格式检查</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">AI分析当前格式问题</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">3</div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">精准优化</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">按照标准精确调整格式</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white text-xl mx-auto mb-2">4</div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">验证结果</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">自动检验是否达到标准</p>
            </div>
          </div>
        </div>
      </div>

      {/* 精准格式优化面板 */}
      {showFormatPanel && (
        <PreciseFormatPanel
          content={content}
          onContentChange={handleContentChange}
          onClose={() => setShowFormatPanel(false)}
        />
      )}
    </div>
  );
}
