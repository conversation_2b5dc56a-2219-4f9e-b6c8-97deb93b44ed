import OpenAI from 'openai';

export interface AIConfig {
  apiKey: string;
  baseURL?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AIRequest {
  type: 'grammar' | 'style' | 'expand' | 'summarize' | 'translate' | 'toc' | 'format';
  content: string;
  options?: {
    targetLanguage?: string;
    style?: string;
    tone?: string;
    length?: 'shorter' | 'longer' | 'same';
  };
}

export interface AIResponse {
  success: boolean;
  result?: string;
  error?: string;
  suggestions?: string[];
}

class AIService {
  private client: OpenAI | null = null;
  private config: AIConfig | null = null;

  // 初始化AI服务
  initialize(config: AIConfig) {
    try {
      this.config = config;
      this.client = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseURL,
        dangerouslyAllowBrowser: true, // 注意：生产环境中应该通过后端代理
      });
      return true;
    } catch (error) {
      console.error('AI service initialization failed:', error);
      return false;
    }
  }

  // 检查是否已初始化
  isInitialized(): boolean {
    return this.client !== null && this.config !== null;
  }

  // 通用AI请求方法
  async makeRequest(request: AIRequest): Promise<AIResponse> {
    if (!this.isInitialized()) {
      return {
        success: false,
        error: '请先配置AI服务',
      };
    }

    try {
      const prompt = this.buildPrompt(request);
      
      const response = await this.client!.chat.completions.create({
        model: this.config!.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt(request.type),
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: this.config!.temperature || 0.7,
        max_tokens: this.config!.maxTokens || 2000,
      });

      const result = response.choices[0]?.message?.content;
      
      if (!result) {
        return {
          success: false,
          error: 'AI服务返回空结果',
        };
      }

      return {
        success: true,
        result: result.trim(),
      };
    } catch (error: any) {
      console.error('AI request failed:', error);
      return {
        success: false,
        error: error.message || 'AI请求失败',
      };
    }
  }

  // 构建提示词
  private buildPrompt(request: AIRequest): string {
    const { type, content, options } = request;

    switch (type) {
      case 'grammar':
        return `请检查并修正以下文本的语法错误，保持原意不变：\n\n${content}`;
      
      case 'style':
        const style = options?.style || '正式';
        return `请将以下文本改写为${style}风格，保持核心内容不变：\n\n${content}`;
      
      case 'expand':
        return `请扩展以下内容，添加更多细节和解释，使其更加丰富：\n\n${content}`;
      
      case 'summarize':
        return `请总结以下内容的要点：\n\n${content}`;
      
      case 'translate':
        const targetLang = options?.targetLanguage || '英文';
        return `请将以下内容翻译为${targetLang}：\n\n${content}`;
      
      case 'toc':
        return `请为以下Markdown文档生成目录结构，使用Markdown格式：\n\n${content}`;
      
      case 'format':
        return `请优化以下Markdown文档的格式和结构，使其更加清晰易读：\n\n${content}`;
      
      default:
        return content;
    }
  }

  // 获取系统提示词
  private getSystemPrompt(type: string): string {
    const basePrompt = '你是一个专业的文档编辑助手，专门帮助用户改进Markdown文档。';
    
    switch (type) {
      case 'grammar':
        return `${basePrompt}你的任务是检查和修正语法错误，包括拼写、标点、语法结构等。请只返回修正后的文本，不要添加额外说明。`;
      
      case 'style':
        return `${basePrompt}你的任务是调整文本的写作风格和语调。请保持内容的准确性，只调整表达方式。`;
      
      case 'expand':
        return `${basePrompt}你的任务是扩展内容，添加相关的细节、例子和解释，使文档更加完整和有用。`;
      
      case 'summarize':
        return `${basePrompt}你的任务是提取和总结关键信息，创建简洁明了的摘要。`;
      
      case 'translate':
        return `${basePrompt}你的任务是进行准确的翻译，保持原文的语调和格式。`;
      
      case 'toc':
        return `${basePrompt}你的任务是分析文档结构，生成清晰的目录。请使用标准的Markdown目录格式。`;
      
      case 'format':
        return `${basePrompt}你的任务是优化Markdown文档的格式，包括标题层级、段落结构、列表格式等。`;
      
      default:
        return basePrompt;
    }
  }

  // 语法检查
  async checkGrammar(content: string): Promise<AIResponse> {
    return this.makeRequest({
      type: 'grammar',
      content,
    });
  }

  // 风格调整
  async adjustStyle(content: string, style: string = '正式'): Promise<AIResponse> {
    return this.makeRequest({
      type: 'style',
      content,
      options: { style },
    });
  }

  // 内容扩展
  async expandContent(content: string): Promise<AIResponse> {
    return this.makeRequest({
      type: 'expand',
      content,
    });
  }

  // 内容总结
  async summarizeContent(content: string): Promise<AIResponse> {
    return this.makeRequest({
      type: 'summarize',
      content,
    });
  }

  // 翻译
  async translateContent(content: string, targetLanguage: string): Promise<AIResponse> {
    return this.makeRequest({
      type: 'translate',
      content,
      options: { targetLanguage },
    });
  }

  // 生成目录
  async generateTOC(content: string): Promise<AIResponse> {
    return this.makeRequest({
      type: 'toc',
      content,
    });
  }

  // 格式优化
  async formatDocument(content: string): Promise<AIResponse> {
    return this.makeRequest({
      type: 'format',
      content,
    });
  }

  // AI智能自动修复
  async intelligentAutoFix(content: string, customPrompt?: string): Promise<AIResponse> {
    if (!this.isInitialized()) {
      return {
        success: false,
        error: '请先配置AI服务',
      };
    }

    try {
      // 构建基础修复要求
      let fixingRequirements = `**修复要求：**
1. **标题格式**：确保标题符号后有空格，移除标题结尾的标点符号
2. **中英文排版**：在中文与英文字母之间添加空格
3. **数字排版**：在中文与数字之间添加空格
4. **空格规范**：移除多余的连续空格，清理行尾空格
5. **空行处理**：移除多余的连续空行，保持适当的段落间距
6. **列表格式**：统一列表缩进，使用标准的Markdown列表格式
7. **标点符号**：规范中英文标点符号的使用
8. **链接格式**：确保链接格式正确
9. **代码块**：确保代码块格式正确
10. **表格格式**：规范表格的对齐和格式`;

      // 如果有自定义提示，添加到修复要求中
      if (customPrompt && customPrompt.trim()) {
        fixingRequirements += `

**用户特殊要求：**
${customPrompt.trim()}
请特别关注用户的特殊要求，在执行标准修复的同时，优先满足用户的自定义指导。`;
      }

      const prompt = `请作为一个专业的Markdown文档格式修复专家，对以下文档进行智能自动修复。

${fixingRequirements}

**重要说明：**
- 只修复格式问题，不要改变文档的内容和含义
- 保持原有的文档结构和层次
- 返回修复后的完整文档内容
- 不要添加任何解释说明，只返回修复后的Markdown内容
- 确保修复后的文档在语义上与原文档完全一致

**待修复文档：**
${content}`;

      const response = await this.client!.chat.completions.create({
        model: this.config!.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的Markdown文档格式修复专家。你的任务是修复文档中的格式问题，但不改变内容的含义。请严格按照用户的要求进行修复，只返回修复后的文档内容，不要添加任何额外的说明。',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1, // 使用较低的温度以确保一致性
        max_tokens: this.config!.maxTokens || 4000,
      });

      const result = response.choices[0]?.message?.content;

      if (!result) {
        return {
          success: false,
          error: 'AI服务返回空结果',
        };
      }

      return {
        success: true,
        result: result.trim(),
      };
    } catch (error: any) {
      console.error('AI智能自动修复失败:', error);
      return {
        success: false,
        error: error.message || 'AI智能自动修复失败',
      };
    }
  }

  // 精准格式优化
  async formatDocumentPrecise(content: string, formatType: string, customPrompt?: string): Promise<AIResponse> {
    if (!this.isInitialized()) {
      return {
        success: false,
        error: '请先配置AI服务',
      };
    }

    try {
      // 使用自定义提示词或构建默认提示词
      if (customPrompt) {
        // 使用自定义提示词直接调用OpenAI
        const response = await this.client!.chat.completions.create({
          model: this.config!.model || 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: '你是一个专业的文档编辑助手，专门帮助用户改进Markdown文档的格式和结构。请按照用户的要求进行精准的格式优化。',
            },
            {
              role: 'user',
              content: customPrompt,
            },
          ],
          temperature: this.config!.temperature || 0.7,
          max_tokens: this.config!.maxTokens || 2000,
        });

        const result = response.choices[0]?.message?.content;

        if (!result) {
          return {
            success: false,
            error: 'AI服务返回空结果',
          };
        }

        return {
          success: true,
          result: result.trim(),
        };
      } else {
        // 使用标准格式优化
        return this.makeRequest({
          type: 'format',
          content,
        });
      }
    } catch (error: any) {
      console.error('精准格式优化失败:', error);
      return {
        success: false,
        error: error.message || '格式优化失败'
      };
    }
  }

  // 构建精准格式优化提示词
  private buildPreciseFormatPrompt(content: string, formatType: string): string {
    const formatTypeNames = {
      'academic': '学术论文',
      'business': '商务报告',
      'resume': '简历文档',
      'general': '通用文档'
    };

    const typeName = formatTypeNames[formatType as keyof typeof formatTypeNames] || '专业文档';

    return `请按照${typeName}格式标准精准优化这份文档，具体要求：

**核心目标：**
使文档达到清晰易读、层次分明、符合${typeName}场景规范的效果，避免格式混乱、排版松散或重点不突出的问题。

**标题层级规范：**
- 一级标题：# 大号字体加粗，居中或左对齐，段前距适当
- 二级标题：## 中号字体加粗，左对齐，段前距1行
- 三级标题：### 小号字体加粗，左对齐，段前距0.5行
- 标题后不加标点符号，编号格式统一

**正文格式要求：**
- 字体字号统一，行间距1.5倍，段间距0.5行
- 首行缩进2字符，避免单字成行
- 段落长度适中（3-8行），超长段落建议拆分
- 中英文混排时，中英文间空1格，数字与中文间空1格

**重点内容处理：**
- 关键数据用**加粗**标注
- 重要信息用**加粗**突出
- 引用内容用*斜体*+双引号，标注来源
- 注意事项用特殊标记突出

**特殊元素规范：**
- 表格：整体居中，边框统一，表头加粗居中，数据对齐
- 列表：有序用"1. 2. 3."，无序用"•"，层级缩进一致
- 图片：居中放置，宽度适当，下方标注说明
- 代码：等宽字体，背景区分，单独成段

**自查要求：**
优化后请确认：
1. 格式前后一致，无多余空行空格
2. 标题层级清晰，编号统一
3. 重点内容突出，易于识别
4. 整体美观专业，符合规范

文档内容：
${content}

请返回优化后的文档，并在最后简要说明主要修改内容。`;
  }

  // 格式检查
  async checkDocumentFormat(content: string, formatType: string): Promise<AIResponse> {
    const prompt = `请检查以下文档的格式规范性，针对${formatType}文档标准进行评估：

**检查维度：**
1. **标题格式**：层级是否清晰，字体字号是否统一，编号是否规范
2. **正文格式**：字体、行距、段距是否一致，缩进是否正确
3. **特殊元素**：表格、列表、图片格式是否规范
4. **整体布局**：排版是否美观，重点是否突出
5. **细节规范**：标点符号、空格、对齐等细节是否正确

**评估标准：**
- 优秀：格式完全符合规范，美观专业
- 良好：格式基本规范，有少量细节问题
- 一般：格式有明显问题，需要优化
- 较差：格式混乱，需要大幅调整

文档内容：
${content}

请提供：
1. 总体评估等级
2. 具体问题清单
3. 改进建议
4. 优化重点`;

    if (!this.isInitialized()) {
      return {
        success: false,
        error: '请先配置AI服务',
      };
    }

    try {
      // 使用OpenAI客户端进行格式检查
      const response = await this.client!.chat.completions.create({
        model: this.config!.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的文档格式检查专家，专门评估Markdown文档的格式规范性。',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: this.config!.temperature || 0.7,
        max_tokens: this.config!.maxTokens || 2000,
      });

      const result = response.choices[0]?.message?.content;

      if (!result) {
        return {
          success: false,
          error: 'AI服务返回空结果',
        };
      }

      return {
        success: true,
        result: result.trim(),
      };
    } catch (error: any) {
      console.error('格式检查失败:', error);
      return {
        success: false,
        error: error.message || '格式检查失败'
      };
    }
  }
}

// 创建单例实例
export const aiService = new AIService();

// 默认配置
export const defaultAIConfig: Partial<AIConfig> = {
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 2000,
};

// 保存AI配置到本地存储
export const saveAIConfig = (config: AIConfig) => {
  try {
    localStorage.setItem('martetdown-ai-config', JSON.stringify(config));
    return true;
  } catch (error) {
    console.error('Failed to save AI config:', error);
    return false;
  }
};

// 从本地存储加载AI配置
export const loadAIConfig = (): AIConfig | null => {
  try {
    const stored = localStorage.getItem('martetdown-ai-config');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Failed to load AI config:', error);
  }
  return null;
};
