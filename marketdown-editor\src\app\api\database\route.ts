import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// MySQL数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  port: parseInt(process.env.DB_PORT || '13306'),
  user: process.env.DB_USER || 'Martetdown',
  password: process.env.DB_PASSWORD || 'qwer4321',
  database: process.env.DB_NAME || 'martetdown',
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
};

// 获取数据库连接
async function getConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    return connection;
  } catch (error) {
    console.error('MySQL连接失败:', error);
    throw new Error('数据库连接失败');
  }
}

// 初始化数据库表
async function initializeTables(connection: mysql.Connection) {
  try {
    // 创建AI模型表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS ai_models (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        api_key TEXT NOT NULL,
        base_url VARCHAR(500),
        model_name VARCHAR(255) NOT NULL,
        model_type ENUM('text', 'image', 'multimodal') DEFAULT 'text',
        temperature DECIMAL(3,2) DEFAULT 0.7,
        max_tokens INT DEFAULT 2000,
        description TEXT,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_is_default (is_default),
        INDEX idx_model_type (model_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建文档表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        content LONGTEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_title (title),
        INDEX idx_updated_at (updated_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建设置表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS settings (
        \`key\` VARCHAR(255) PRIMARY KEY,
        value JSON,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建模板表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        category VARCHAR(100),
        content LONGTEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('数据库表初始化完成');
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    throw error;
  }
}

// GET - 检查连接状态
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');

  try {
    const connection = await getConnection();

    switch (action) {
      case 'check':
        await connection.ping();
        await connection.end();
        return NextResponse.json({ 
          success: true, 
          message: 'MySQL连接成功',
          config: {
            host: dbConfig.host,
            port: dbConfig.port,
            user: dbConfig.user,
            database: dbConfig.database
          }
        });

      case 'models':
        const [rows] = await connection.execute('SELECT * FROM ai_models ORDER BY created_at DESC');
        await connection.end();
        return NextResponse.json({ success: true, data: rows });

      default:
        await connection.end();
        return NextResponse.json({ success: false, message: '未知操作' });
    }
  } catch (error) {
    console.error('数据库操作失败:', error);
    return NextResponse.json({ 
      success: false, 
      message: error instanceof Error ? error.message : '数据库操作失败' 
    }, { status: 500 });
  }
}

// POST - 执行数据库操作
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    const connection = await getConnection();

    switch (action) {
      case 'init':
        await initializeTables(connection);
        
        // 检查是否需要添加默认模型
        const [existingModels] = await connection.execute('SELECT COUNT(*) as count FROM ai_models');
        const count = (existingModels as any[])[0].count;
        
        if (count === 0) {
          // 添加默认模型
          const defaultModels = [
            {
              name: 'OpenAI GPT-3.5 Turbo',
              api_key: '',
              base_url: 'https://api.openai.com/v1',
              model_name: 'gpt-3.5-turbo',
              model_type: 'text',
              temperature: 0.7,
              max_tokens: 2000,
              description: 'OpenAI官方GPT-3.5 Turbo模型，性价比高',
              is_default: true,
            },
            {
              name: 'OpenAI GPT-4',
              api_key: '',
              base_url: 'https://api.openai.com/v1',
              model_name: 'gpt-4',
              model_type: 'text',
              temperature: 0.7,
              max_tokens: 2000,
              description: 'OpenAI最新GPT-4模型，能力更强',
              is_default: false,
            },
          ];

          for (const model of defaultModels) {
            await connection.execute(`
              INSERT INTO ai_models (name, api_key, base_url, model_name, model_type, temperature, max_tokens, description, is_default)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              model.name,
              model.api_key,
              model.base_url,
              model.model_name,
              model.model_type,
              model.temperature,
              model.max_tokens,
              model.description,
              model.is_default
            ]);
          }
        }

        await connection.end();
        return NextResponse.json({ 
          success: true, 
          message: 'MySQL数据库初始化完成',
          defaultModelsAdded: count === 0
        });

      case 'migrate':
        // 这里应该从IndexedDB获取数据并迁移到MySQL
        // 由于IndexedDB只能在客户端访问，我们需要客户端先获取数据然后发送到这里
        const { aiModels, settings } = data;
        
        let migratedModels = 0;
        let migratedSettings = 0;
        let failedModels = 0;
        let failedSettings = 0;

        // 迁移AI模型
        if (aiModels && Array.isArray(aiModels)) {
          for (const model of aiModels) {
            try {
              // 如果设置为默认，先取消其他默认设置
              if (model.is_default) {
                await connection.execute('UPDATE ai_models SET is_default = FALSE');
              }

              await connection.execute(`
                INSERT INTO ai_models (name, api_key, base_url, model_name, model_type, temperature, max_tokens, description, is_default)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                model.name,
                model.api_key,
                model.base_url || null,
                model.model_name,
                model.model_type,
                model.temperature,
                model.max_tokens,
                model.description || null,
                model.is_default
              ]);
              migratedModels++;
            } catch (error) {
              console.error(`AI模型 "${model.name}" 迁移失败:`, error);
              failedModels++;
            }
          }
        }

        // 迁移设置
        if (settings && typeof settings === 'object') {
          for (const [key, value] of Object.entries(settings)) {
            try {
              await connection.execute(`
                INSERT INTO settings (\`key\`, value) VALUES (?, ?)
                ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = CURRENT_TIMESTAMP
              `, [key, JSON.stringify(value)]);
              migratedSettings++;
            } catch (error) {
              console.error(`设置 "${key}" 迁移失败:`, error);
              failedSettings++;
            }
          }
        }

        await connection.end();
        return NextResponse.json({
          success: true,
          message: '数据迁移完成',
          result: {
            aiModels: { success: migratedModels, failed: failedModels },
            settings: { success: migratedSettings, failed: failedSettings }
          }
        });

      case 'clear':
        await connection.execute('DELETE FROM ai_models');
        await connection.execute('DELETE FROM documents');
        await connection.execute('DELETE FROM settings');
        await connection.execute('DELETE FROM templates');
        await connection.end();
        return NextResponse.json({ success: true, message: 'MySQL数据已清空' });

      default:
        await connection.end();
        return NextResponse.json({ success: false, message: '未知操作' });
    }
  } catch (error) {
    console.error('数据库操作失败:', error);
    return NextResponse.json({ 
      success: false, 
      message: error instanceof Error ? error.message : '数据库操作失败' 
    }, { status: 500 });
  }
}
