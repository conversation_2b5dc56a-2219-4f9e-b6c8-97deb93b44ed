'use client';

import React, { useState, useEffect } from 'react';
import { 
  databaseManager, 
  DatabaseType, 
  getCurrentDatabaseType, 
  switchToMySQL, 
  switchToIndexedDB, 
  isMySQLAvailable 
} from '@/utils/databaseAdapter';

interface DatabaseSettingsProps {
  onDatabaseChange?: (type: DatabaseType) => void;
  className?: string;
}

export const DatabaseSettings: React.FC<DatabaseSettingsProps> = ({
  onDatabaseChange,
  className = ''
}) => {
  const [currentType, setCurrentType] = useState<DatabaseType>('indexeddb');
  const [isLoading, setIsLoading] = useState(false);
  const [mysqlAvailable, setMysqlAvailable] = useState(false);
  const [status, setStatus] = useState('');

  // 检查MySQL可用性
  const checkMySQLAvailability = async () => {
    try {
      const available = await isMySQLAvailable();
      setMysqlAvailable(available);
      return available;
    } catch (error) {
      console.error('检查MySQL可用性失败:', error);
      setMysqlAvailable(false);
      return false;
    }
  };

  // 切换数据库
  const handleDatabaseSwitch = async (type: DatabaseType) => {
    if (currentType === type) return;

    setIsLoading(true);
    setStatus(`正在切换到 ${type === 'mysql' ? 'MySQL' : 'IndexedDB'}...`);

    try {
      if (type === 'mysql') {
        // 检查MySQL是否可用
        const available = await checkMySQLAvailability();
        if (!available) {
          setStatus('❌ MySQL连接失败，无法切换');
          setIsLoading(false);
          return;
        }
        await switchToMySQL();
      } else {
        await switchToIndexedDB();
      }

      setCurrentType(type);
      setStatus(`✅ 已切换到 ${type === 'mysql' ? 'MySQL' : 'IndexedDB'}`);
      
      if (onDatabaseChange) {
        onDatabaseChange(type);
      }
    } catch (error) {
      console.error('数据库切换失败:', error);
      setStatus(`❌ 切换失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    const init = async () => {
      const type = getCurrentDatabaseType();
      setCurrentType(type);
      await checkMySQLAvailability();
    };
    
    init();
  }, []);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          🗄️ 数据库设置
        </h3>
        <div className="flex items-center gap-2">
          <div className={`px-2 py-1 rounded text-xs font-medium ${
            currentType === 'mysql'
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          }`}>
            当前: {currentType === 'mysql' ? 'MySQL' : 'IndexedDB'}
          </div>
        </div>
      </div>

      {/* 数据库选项 */}
      <div className="space-y-3">
        {/* IndexedDB选项 */}
        <div className={`border rounded-lg p-3 cursor-pointer transition-colors ${
          currentType === 'indexeddb'
            ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
            : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
        }`}
        onClick={() => handleDatabaseSwitch('indexeddb')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-4 h-4 rounded-full border-2 ${
                currentType === 'indexeddb'
                  ? 'border-green-500 bg-green-500'
                  : 'border-gray-300 dark:border-gray-600'
              }`}>
                {currentType === 'indexeddb' && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">IndexedDB</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  浏览器本地数据库，无需配置
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs rounded">
                ✅ 可用
              </span>
            </div>
          </div>
        </div>

        {/* MySQL选项 */}
        <div className={`border rounded-lg p-3 cursor-pointer transition-colors ${
          currentType === 'mysql'
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : mysqlAvailable
              ? 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
              : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-60'
        }`}
        onClick={() => mysqlAvailable && handleDatabaseSwitch('mysql')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-4 h-4 rounded-full border-2 ${
                currentType === 'mysql'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300 dark:border-gray-600'
              }`}>
                {currentType === 'mysql' && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">MySQL</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  远程MySQL数据库，支持多设备同步
                </p>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  110.42.40.185:13306
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className={`px-2 py-1 text-xs rounded ${
                mysqlAvailable
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {mysqlAvailable ? '✅ 可用' : '❌ 不可用'}
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  checkMySQLAvailability();
                }}
                className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                disabled={isLoading}
              >
                重新检查
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 状态显示 */}
      {(status || isLoading) && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center gap-2">
            {isLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            )}
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {status || '处理中...'}
            </span>
          </div>
        </div>
      )}

      {/* 数据库信息 */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          数据库特性对比
        </h4>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <div className="font-medium text-gray-700 dark:text-gray-300 mb-1">IndexedDB</div>
            <ul className="space-y-1 text-gray-600 dark:text-gray-400">
              <li>• 本地存储</li>
              <li>• 无需配置</li>
              <li>• 离线可用</li>
              <li>• 单设备数据</li>
            </ul>
          </div>
          <div>
            <div className="font-medium text-gray-700 dark:text-gray-300 mb-1">MySQL</div>
            <ul className="space-y-1 text-gray-600 dark:text-gray-400">
              <li>• 远程存储</li>
              <li>• 需要网络</li>
              <li>• 多设备同步</li>
              <li>• 数据备份</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
        <div className="flex gap-2">
          <button
            onClick={() => window.open('/database-manager', '_blank')}
            className="flex-1 px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded transition-colors"
          >
            🔧 数据库管理
          </button>
          {currentType === 'mysql' && (
            <button
              onClick={() => window.open('/database-manager', '_blank')}
              className="flex-1 px-3 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded transition-colors"
            >
              📊 迁移数据
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
