@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 修复下拉框选项颜色问题 */
select option {
  background-color: white;
  color: black;
}

@media (prefers-color-scheme: dark) {
  select option {
    background-color: #374151;
    color: white;
  }
}

/* 确保下拉框在暗色模式下可见 */
.dark select option {
  background-color: #374151 !important;
  color: white !important;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 编辑器样式 */
.markdown-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}

.markdown-editor:focus {
  outline: none;
}

/* 预览样式增强 */
.prose {
  max-width: none;
}

.prose pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.dark .prose pre {
  background-color: #161b22;
}

.prose code {
  background-color: rgba(175, 184, 193, 0.2);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.875em;
}

.dark .prose code {
  background-color: rgba(110, 118, 129, 0.4);
}

.prose blockquote {
  border-left: 4px solid #d1d5db;
  margin: 1.5em 0;
  padding-left: 1em;
  font-style: italic;
}

.dark .prose blockquote {
  border-left-color: #4b5563;
}

/* 表格样式 */
.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
}

.prose th,
.prose td {
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  text-align: left;
}

.dark .prose th,
.dark .prose td {
  border-color: #4b5563;
}

.prose th {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .prose th {
  background-color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-layout {
    flex-direction: column;
  }

  .file-manager {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .dark .file-manager {
    border-bottom-color: #374151;
  }

  .editor-content {
    flex-direction: column;
  }

  .editor-pane,
  .preview-pane {
    width: 100% !important;
    height: 50vh;
  }

  .toolbar {
    flex-wrap: wrap;
    gap: 4px;
  }

  .toolbar button {
    min-width: 32px;
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 打印样式 */
@media print {
  .file-manager,
  .toolbar,
  .status-bar {
    display: none !important;
  }

  .editor-layout {
    height: auto !important;
  }

  .preview-pane {
    width: 100% !important;
    height: auto !important;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 焦点样式 */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 选择文本样式 */
::selection {
  background-color: #3b82f6;
  color: white;
}

.dark ::selection {
  background-color: #60a5fa;
  color: #1f2937;
}
