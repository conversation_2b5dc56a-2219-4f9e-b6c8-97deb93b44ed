"""
转换器基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import os
import logging

from ..exceptions import ConversionError, FileNotFoundError
from ..utils.file_utils import validate_file, ensure_output_dir


class BaseConverter(ABC):
    """转换器基类"""
    
    def __init__(self, **kwargs):
        """
        初始化转换器
        
        Args:
            **kwargs: 转换器配置参数
        """
        self.config = kwargs
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def supported_input_formats(self) -> list:
        """支持的输入格式"""
        pass
    
    @property
    @abstractmethod
    def supported_output_formats(self) -> list:
        """支持的输出格式"""
        pass
    
    @abstractmethod
    def convert_to_pdf(self, input_path: str, output_path: str, **kwargs) -> bool:
        """转换为PDF格式"""
        pass
    
    @abstractmethod
    def convert_to_docx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """转换为Word格式"""
        pass
    
    @abstractmethod
    def convert_to_xlsx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """转换为Excel格式"""
        pass
    
    @abstractmethod
    def convert_to_txt(self, input_path: str, output_path: str, **kwargs) -> bool:
        """转换为文本格式"""
        pass
    
    @abstractmethod
    def convert_to_md(self, input_path: str, output_path: str, **kwargs) -> bool:
        """转换为Markdown格式"""
        pass
    
    def convert(self, input_path: str, output_path: str, target_format: str = None, **kwargs) -> bool:
        """
        通用转换方法
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_format: 目标格式，如果为None则从output_path推断
            **kwargs: 转换参数
            
        Returns:
            转换是否成功
            
        Raises:
            FileNotFoundError: 输入文件不存在
            ConversionError: 转换失败
        """
        # 验证输入文件
        if not validate_file(input_path):
            raise FileNotFoundError(f"输入文件无效或不存在: {input_path}")
        
        # 推断目标格式
        if target_format is None:
            _, ext = os.path.splitext(output_path.lower())
            target_format = ext.lstrip('.')
        
        target_format = target_format.lower()
        
        # 确保输出目录存在
        ensure_output_dir(output_path)
        
        # 根据目标格式调用相应的转换方法
        conversion_methods = {
            'pdf': self.convert_to_pdf,
            'docx': self.convert_to_docx,
            'xlsx': self.convert_to_xlsx,
            'txt': self.convert_to_txt,
            'md': self.convert_to_md,
            'markdown': self.convert_to_md
        }
        
        if target_format not in conversion_methods:
            raise ConversionError(f"不支持的目标格式: {target_format}")
        
        try:
            self.logger.info(f"开始转换: {input_path} -> {output_path} ({target_format})")
            result = conversion_methods[target_format](input_path, output_path, **kwargs)
            
            if result:
                self.logger.info(f"转换成功: {output_path}")
            else:
                self.logger.error(f"转换失败: {input_path} -> {output_path}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"转换过程中发生错误: {str(e)}")
            raise ConversionError(f"转换失败: {str(e)}")
    
    def validate_conversion(self, input_format: str, target_format: str) -> bool:
        """
        验证是否支持指定的转换
        
        Args:
            input_format: 输入格式
            target_format: 目标格式
            
        Returns:
            是否支持转换
        """
        input_format = input_format.lower()
        target_format = target_format.lower()
        
        return (input_format in self.supported_input_formats and 
                target_format in self.supported_output_formats)
    
    def get_conversion_info(self) -> Dict[str, Any]:
        """
        获取转换器信息
        
        Returns:
            转换器信息字典
        """
        return {
            'name': self.__class__.__name__,
            'supported_input_formats': self.supported_input_formats,
            'supported_output_formats': self.supported_output_formats,
            'config': self.config
        }
