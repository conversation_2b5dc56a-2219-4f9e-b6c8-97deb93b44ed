# 智能文档编辑器使用指南

欢迎使用Martetdown智能文档编辑器！本指南将帮助您快速上手所有功能。

## 一、基础功能

### （一）编辑功能

#### 1. 实时预览
编辑器支持实时预览功能，您可以在左侧编辑，右侧实时查看渲染效果。

#### 2. 语法高亮
支持完整的Markdown语法高亮，让您的编辑体验更加舒适。

### （二）格式支持

#### 1. 标题层级
支持1-6级标题，使用#号来定义标题层级。

#### 2. 文本格式
- **粗体文本**
- *斜体文本*
- `行内代码`
- ~~删除线~~

#### 3. 列表格式
1. 有序列表项目1
2. 有序列表项目2
   - 嵌套无序列表
   - 另一个嵌套项目

#### 4. 代码块
```javascript
function hello() {
    console.log("Hello, World!");
}
```

## 二、高级功能

### （一）AI智能助手

#### 1. 智能修复
AI可以自动检测并修复文档中的格式问题。

#### 2. 内容优化
提供智能的内容建议和优化方案。

### （二）文档分析

#### 1. 结构分析
自动分析文档结构，生成目录和统计信息。

#### 2. 格式检查
检测文档中的格式问题并提供修复建议。

### （三）导出功能

#### 1. 多格式导出
支持导出为PDF、Word、HTML等多种格式。

#### 2. 自定义样式
可以自定义导出文档的样式和格式。

## 三、目录功能

### （一）自动生成目录

#### 1. 目录结构
系统会自动分析文档中的标题，生成层级目录结构。

#### 2. 智能排版
目录会根据标题层级自动缩进和排版。

### （二）目录导航

#### 1. 快速跳转
点击目录项可以快速跳转到对应的文档位置。

#### 2. 搜索功能
在目录页面可以搜索特定的章节标题。

### （三）目录页面

#### 1. 独立显示
目录可以在独立页面中显示，提供更好的浏览体验。

#### 2. 统计信息
显示各级标题的数量统计和文档结构信息。

## 四、使用技巧

### （一）快捷键

#### 1. 编辑快捷键
- Ctrl+B：加粗
- Ctrl+I：斜体
- Ctrl+S：保存

#### 2. 导航快捷键
- Ctrl+F：查找
- Ctrl+H：替换

### （二）最佳实践

#### 1. 标题规范
建议使用规范的标题层级，避免跳级使用。

#### 2. 内容组织
合理组织文档结构，使用适当的标题和段落。

## 五、常见问题

### （一）格式问题

#### 1. 标题格式
确保标题符号后有空格，如"# 标题"而不是"#标题"。

#### 2. 列表格式
列表项目前要有空格，保持格式一致。

### （二）功能问题

#### 1. AI功能
如果AI功能不可用，请检查网络连接和API配置。

#### 2. 导出功能
导出功能需要现代浏览器支持，建议使用最新版本浏览器。

---

**提示**：本文档演示了完整的标题层级结构，您可以使用目录功能来快速导航到任意章节。点击"智能排版"按钮，然后选择"目录生成"标签页，点击"📚 目录页"按钮即可打开独立的目录页面。
