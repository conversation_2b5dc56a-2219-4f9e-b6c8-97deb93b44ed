'use client';

import { useState } from 'react';
import { ExportDialog } from '@/components/ui/ExportDialog';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const sampleMarkdown = `# 测试文档

这是一个包含表格的测试文档，用于验证导出功能。

## 简单表格

| 姓名 | 年龄 | 职业 |
|------|------|------|
| 张三 | 25 | 工程师 |
| 李四 | 30 | 设计师 |
| 王五 | 28 | 产品经理 |

## 复杂表格

| 项目名称 | 开始时间 | 结束时间 | 负责人 | 状态 | 备注 |
|----------|----------|----------|--------|------|------|
| 网站重构 | 2024-01-01 | 2024-03-31 | 张三 | 进行中 | 前端框架升级 |
| 移动应用开发 | 2024-02-15 | 2024-06-30 | 李四 | 计划中 | React Native |
| 数据分析平台 | 2024-01-15 | 2024-05-15 | 王五 | 已完成 | 使用Python和Django |
| API接口优化 | 2024-03-01 | 2024-04-30 | 赵六 | 进行中 | 性能提升50% |

## 包含长文本的表格

| 功能模块 | 详细描述 | 技术栈 |
|----------|----------|--------|
| 用户管理 | 包括用户注册、登录、权限管理、个人信息编辑等功能。支持多种登录方式，如邮箱、手机号、第三方登录等。 | React + TypeScript + Redux |
| 内容管理 | 支持富文本编辑、图片上传、文件管理、内容审核等功能。提供强大的编辑器和媒体库管理。 | TinyMCE + AWS S3 + Node.js |
| 数据统计 | 实时数据展示、图表分析、报表生成、数据导出等功能。支持多维度数据分析和可视化展示。 | D3.js + Chart.js + Python |

## 其他内容

### 代码示例

\`\`\`javascript
function exportTable() {
  console.log('导出表格功能');
  return true;
}
\`\`\`

### 引用

> 这是一个引用文本，用于测试导出功能中的引用样式。

### 列表

1. 第一项
2. 第二项
3. 第三项

- 无序列表项1
- 无序列表项2
- 无序列表项3

**粗体文本** 和 *斜体文本* 以及 \`行内代码\`。
`;

export default function TestExport() {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [content, setContent] = useState(sampleMarkdown);

  // 生成HTML内容用于导出
  const generateHTMLContent = () => {
    // 简单的Markdown到HTML转换
    return content
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            表格导出测试
          </h1>
          <div className="flex gap-4">
            <button
              onClick={() => setShowExportDialog(true)}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
            >
              测试导出
            </button>
            <button
              onClick={() => setContent(sampleMarkdown)}
              className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors"
            >
              重置内容
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 编辑器 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Markdown 编辑器
            </h2>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 font-mono text-sm resize-none"
              placeholder="输入Markdown内容..."
            />
          </div>

          {/* 预览 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              预览效果
            </h2>
            <div className="h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-700">
              <div className="prose dark:prose-invert max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>

        {/* 导出说明 */}
        {/* 表格主题展示 */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            🎨 表格主题样式
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { theme: 'professional', name: '专业风格', desc: '商务正式，适合报告' },
              { theme: 'modern', name: '现代风格', desc: '时尚渐变，视觉突出' },
              { theme: 'minimal', name: '简约风格', desc: '简洁清爽，易于阅读' },
              { theme: 'colorful', name: '彩色风格', desc: '活泼鲜艳，吸引眼球' },
            ].map((item) => (
              <div key={item.theme} className="text-center">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                  {item.name}
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-2">
                  <table className="w-full text-xs border-collapse">
                    <thead>
                      <tr>
                        <th className={`px-2 py-1 text-center font-semibold border ${
                          item.theme === 'professional' ? 'bg-gray-700 text-white' :
                          item.theme === 'modern' ? 'bg-blue-500 text-white' :
                          item.theme === 'minimal' ? 'bg-gray-400 text-white' :
                          'bg-red-500 text-white'
                        }`}>
                          姓名
                        </th>
                        <th className={`px-2 py-1 text-center font-semibold border ${
                          item.theme === 'professional' ? 'bg-gray-700 text-white' :
                          item.theme === 'modern' ? 'bg-blue-500 text-white' :
                          item.theme === 'minimal' ? 'bg-gray-400 text-white' :
                          'bg-red-500 text-white'
                        }`}>
                          职位
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="px-2 py-1 border text-center bg-white">张三</td>
                        <td className="px-2 py-1 border text-center bg-white">工程师</td>
                      </tr>
                      <tr>
                        <td className={`px-2 py-1 border text-center ${
                          item.theme === 'professional' ? 'bg-gray-50' :
                          item.theme === 'modern' ? 'bg-blue-50' :
                          item.theme === 'minimal' ? 'bg-gray-50' :
                          'bg-red-50'
                        }`}>李四</td>
                        <td className={`px-2 py-1 border text-center ${
                          item.theme === 'professional' ? 'bg-gray-50' :
                          item.theme === 'modern' ? 'bg-blue-50' :
                          item.theme === 'minimal' ? 'bg-gray-50' :
                          'bg-red-50'
                        }`}>设计师</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {item.desc}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            📋 导出功能说明
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">📄 PDF导出</h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                ✅ 高分辨率渲染<br/>
                ✅ 清晰表格边框<br/>
                ✅ 智能列宽分配<br/>
                ✅ 专业表头样式
              </p>
            </div>
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">📝 Word导出</h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                ✅ 原生表格格式<br/>
                ✅ 自动列宽调整<br/>
                ✅ 表头背景样式<br/>
                ✅ 斑马纹效果
              </p>
            </div>
            <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-2">🌐 HTML导出</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                ✅ 响应式设计<br/>
                ✅ 悬停效果<br/>
                ✅ 渐变表头<br/>
                ✅ 移动端适配
              </p>
            </div>
            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">🎨 主题系统</h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                ✅ 4种预设主题<br/>
                ✅ 统一样式风格<br/>
                ✅ 专业配色方案<br/>
                ✅ 一键切换主题
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 导出对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        content={content}
        htmlContent={generateHTMLContent()}
      />
    </div>
  );
}
