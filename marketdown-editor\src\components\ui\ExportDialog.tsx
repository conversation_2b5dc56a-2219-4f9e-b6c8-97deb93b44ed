'use client';

import React, { useState } from 'react';
import { ExportOptions, exportToPDF, exportToWord, exportToHTML, exportToText } from '@/utils/export';
import { TableThemePreview } from './TableThemePreview';
import { CopyButton } from './CopyButton';

interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  htmlContent: string;
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onClose,
  content,
  htmlContent,
}) => {
  const [format, setFormat] = useState<'pdf' | 'docx' | 'html' | 'txt'>('pdf');
  const [title, setTitle] = useState('');
  const [author, setAuthor] = useState('');
  const [pageSize, setPageSize] = useState<'A4' | 'Letter' | 'A3'>('A4');
  const [fontSize, setFontSize] = useState(14);
  const [fontFamily, setFontFamily] = useState('Arial, sans-serif');
  const [includeStyles, setIncludeStyles] = useState(true);
  const [tableTheme, setTableTheme] = useState<'professional' | 'modern' | 'minimal' | 'colorful'>('modern');
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);
    
    const options: ExportOptions = {
      format,
      title: title || 'Untitled Document',
      author,
      pageSize,
      fontSize,
      fontFamily,
      includeStyles,
      tableTheme,
    };

    try {
      switch (format) {
        case 'pdf':
          await exportToPDF(content, htmlContent, options);
          break;
        case 'docx':
          await exportToWord(content, options);
          break;
        case 'html':
          exportToHTML(content, htmlContent, options);
          break;
        case 'txt':
          exportToText(content, options);
          break;
      }
      
      onClose();
    } catch (error) {
      alert(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsExporting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-96 max-w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            导出文档
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ×
          </button>
        </div>

        <div className="space-y-4">
          {/* 格式选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              导出格式
            </label>
            <div className="grid grid-cols-2 gap-2">
              {[
                { value: 'pdf', label: 'PDF', icon: '📄' },
                { value: 'docx', label: 'Word', icon: '📝' },
                { value: 'html', label: 'HTML', icon: '🌐' },
                { value: 'txt', label: '纯文本', icon: '📋' },
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFormat(option.value as any)}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    format === option.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">{option.icon}</div>
                    <div className="text-sm font-medium">{option.label}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 文档信息 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              文档标题
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="输入文档标题..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              作者
            </label>
            <input
              type="text"
              value={author}
              onChange={(e) => setAuthor(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="输入作者名称..."
            />
          </div>

          {/* PDF/Word 特定设置 */}
          {(format === 'pdf' || format === 'docx') && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  页面大小
                </label>
                <select
                  value={pageSize}
                  onChange={(e) => setPageSize(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="A4">A4</option>
                  <option value="Letter">Letter</option>
                  <option value="A3">A3</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  字体大小
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="range"
                    min="10"
                    max="24"
                    value={fontSize}
                    onChange={(e) => setFontSize(Number(e.target.value))}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[3rem]">
                    {fontSize}px
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  字体
                </label>
                <select
                  value={fontFamily}
                  onChange={(e) => setFontFamily(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="Arial, sans-serif">Arial</option>
                  <option value="'Times New Roman', serif">Times New Roman</option>
                  <option value="'Courier New', monospace">Courier New</option>
                  <option value="'Microsoft YaHei', sans-serif">微软雅黑</option>
                  <option value="'SimSun', serif">宋体</option>
                </select>
              </div>

              {/* 表格主题选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  表格主题
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { value: 'professional', label: '专业', color: '#2c3e50', desc: '商务风格' },
                    { value: 'modern', label: '现代', color: '#667eea', desc: '时尚渐变' },
                    { value: 'minimal', label: '简约', color: '#95a5a6', desc: '简洁清爽' },
                    { value: 'colorful', label: '彩色', color: '#ff6b6b', desc: '活泼鲜艳' },
                  ].map((theme) => (
                    <button
                      key={theme.value}
                      onClick={() => setTableTheme(theme.value as any)}
                      className={`p-3 rounded-lg border-2 transition-colors ${
                        tableTheme === theme.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      }`}
                    >
                      <div className="text-center">
                        <TableThemePreview
                          theme={theme.value as any}
                          className="mb-2"
                        />
                        <div className="text-sm font-medium">{theme.label}</div>
                        <div className="text-xs text-gray-500">{theme.desc}</div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* 样式选项 */}
          {(format === 'pdf' || format === 'html') && (
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={includeStyles}
                  onChange={(e) => setIncludeStyles(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">包含样式</span>
              </label>
            </div>
          )}

          {/* 预览信息 */}
          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <div>字符数: {content.length}</div>
              <div>单词数: {content.trim() ? content.trim().split(/\s+/).length : 0}</div>
              <div>行数: {content.split('\n').length}</div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col gap-3 mt-6">
          {/* 复制到Word按钮 */}
          <CopyButton
            content={content}
            theme={tableTheme}
            size="md"
            variant="outline"
            className="w-full border-green-500 text-green-600 hover:bg-green-500 hover:text-white"
          />

          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleExport}
              disabled={isExporting}
              className="flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isExporting ? '导出中...' : '导出文件'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
