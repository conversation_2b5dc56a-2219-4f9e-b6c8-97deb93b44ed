# 自定义AI修复提示示例

## 常用自定义提示示例：

### 1. 技术文档专用
```
保持所有技术术语的英文原文，不要在技术术语前后添加空格
统一使用中文标点符号
确保代码块都有正确的语言标识
```

### 2. 学术论文格式
```
统一使用全角标点符号
保持引用格式不变
确保图表标题格式一致
数字和单位之间保持空格
```

### 3. 博客文章优化
```
优化标题层级，确保逻辑清晰
统一列表格式，使用数字编号
强调重点内容的格式
确保链接描述清晰
```

### 4. API文档规范
```
统一参数描述格式
确保所有代码示例都有语言标识
保持HTTP状态码格式一致
统一错误信息格式
```

### 5. 产品说明书
```
统一产品名称格式
确保步骤编号清晰
保持警告和提示的格式一致
优化表格的可读性
```

### 6. 翻译文档优化
```
保持原文的专业术语不变
统一中英文混排的空格规则
确保翻译对照格式清晰
保持原文的段落结构
```

## 使用方法：

1. 点击"🤖 AI修复"按钮旁边的"💬"按钮
2. 在弹出的文本框中输入您的自定义要求
3. 点击"🚀 开始AI修复"按钮
4. AI会根据您的指导进行更精准的修复

## 提示编写技巧：

- 使用具体明确的描述
- 一行一个要求，便于AI理解
- 可以指定特殊的格式规则
- 可以要求保持某些内容不变
- 可以指定特定的修复优先级
