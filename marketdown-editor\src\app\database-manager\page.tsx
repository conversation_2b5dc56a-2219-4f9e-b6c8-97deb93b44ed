'use client';

import { useState, useEffect } from 'react';
import { dbManager } from '@/utils/indexedDB';

export default function DatabaseManager() {
  const [status, setStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [mysqlConnected, setMysqlConnected] = useState(false);
  const [migrationResult, setMigrationResult] = useState<any>(null);
  const [models, setModels] = useState<any[]>([]);

  // 检查MySQL连接状态
  const checkConnection = async () => {
    setIsLoading(true);
    setStatus('检查MySQL连接...');

    try {
      const response = await fetch('/api/database?action=check');
      const result = await response.json();

      setMysqlConnected(result.success);
      setStatus(result.success ? '✅ MySQL连接成功' : `❌ MySQL连接失败: ${result.message}`);
    } catch (error) {
      setMysqlConnected(false);
      setStatus(`❌ MySQL连接失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化MySQL数据库
  const initializeDatabase = async () => {
    setIsLoading(true);
    setStatus('初始化MySQL数据库...');

    try {
      const response = await fetch('/api/database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'init' })
      });

      const result = await response.json();

      if (result.success) {
        setStatus('✅ MySQL数据库初始化完成');
        await loadModels();
      } else {
        setStatus(`❌ 数据库初始化失败: ${result.message}`);
      }
    } catch (error) {
      setStatus(`❌ 数据库初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 执行数据迁移
  const executeMigration = async () => {
    setIsLoading(true);
    setStatus('开始数据迁移...');

    try {
      // 首先从IndexedDB获取数据
      setStatus('正在从IndexedDB获取数据...');
      await dbManager.init();

      const aiModels = await dbManager.getAllAIModels();
      const settings: any = {};

      // 获取常见设置
      const settingKeys = ['theme', 'language', 'editor-config', 'auto-save', 'font-size'];
      for (const key of settingKeys) {
        try {
          const value = await dbManager.getSetting(key);
          if (value !== null) {
            settings[key] = value;
          }
        } catch (error) {
          console.error(`获取设置 ${key} 失败:`, error);
        }
      }

      setStatus('正在迁移数据到MySQL...');

      // 发送数据到服务器进行迁移
      const response = await fetch('/api/database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'migrate',
          data: { aiModels, settings }
        })
      });

      const result = await response.json();

      if (result.success) {
        setMigrationResult({
          aiModels: result.result.aiModels,
          settings: result.result.settings,
          totalTime: 0 // 服务器端不计算总时间
        });
        setStatus('✅ 数据迁移完成!');
        await loadModels();
      } else {
        setStatus(`❌ 数据迁移失败: ${result.message}`);
      }
    } catch (error) {
      setStatus(`❌ 数据迁移失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 验证迁移结果
  const validateMigration = async () => {
    setIsLoading(true);
    setStatus('验证迁移结果...');

    try {
      // 获取IndexedDB中的数据
      await dbManager.init();
      const indexedModels = await dbManager.getAllAIModels();

      // 获取MySQL中的数据
      const response = await fetch('/api/database?action=models');
      const result = await response.json();

      if (result.success) {
        const mysqlModels = result.data;
        const aiModelsMatch = indexedModels.length === mysqlModels.length;
        setStatus(`验证完成: AI模型${aiModelsMatch ? '✅' : '❌'} (IndexedDB: ${indexedModels.length}, MySQL: ${mysqlModels.length})`);
      } else {
        setStatus(`❌ 验证失败: ${result.message}`);
      }
    } catch (error) {
      setStatus(`❌ 验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载AI模型
  const loadModels = async () => {
    try {
      const response = await fetch('/api/database?action=models');
      const result = await response.json();

      if (result.success) {
        setModels(result.data);
      } else {
        console.error('加载AI模型失败:', result.message);
      }
    } catch (error) {
      console.error('加载AI模型失败:', error);
    }
  };

  // 清空MySQL数据
  const clearMySQLData = async () => {
    if (!confirm('确定要清空MySQL数据吗？此操作不可恢复！')) {
      return;
    }

    setIsLoading(true);
    setStatus('清空MySQL数据...');

    try {
      const response = await fetch('/api/database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'clear' })
      });

      const result = await response.json();

      if (result.success) {
        setStatus('✅ MySQL数据已清空');
        setModels([]);
        setMigrationResult(null);
      } else {
        setStatus(`❌ 清空数据失败: ${result.message}`);
      }
    } catch (error) {
      setStatus(`❌ 清空数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 页面加载时检查连接
  useEffect(() => {
    checkConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            🗄️ 数据库管理器
          </h1>

          {/* 连接状态 */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              连接状态
            </h2>
            <div className="flex items-center gap-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                mysqlConnected 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                MySQL: {mysqlConnected ? '已连接' : '未连接'}
              </div>
              <button
                onClick={checkConnection}
                disabled={isLoading}
                className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm disabled:opacity-50"
              >
                重新检查
              </button>
            </div>
          </div>

          {/* 数据库配置信息 */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              MySQL配置信息
            </h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">主机:</span>
                <span className="ml-2 text-gray-900 dark:text-white">110.42.40.185</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">端口:</span>
                <span className="ml-2 text-gray-900 dark:text-white">13306</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">用户:</span>
                <span className="ml-2 text-gray-900 dark:text-white">Martetdown</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">数据库:</span>
                <span className="ml-2 text-gray-900 dark:text-white">martetdown</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              数据库操作
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button
                onClick={initializeDatabase}
                disabled={isLoading || !mysqlConnected}
                className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                初始化数据库
              </button>
              <button
                onClick={executeMigration}
                disabled={isLoading || !mysqlConnected}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                执行迁移
              </button>
              <button
                onClick={validateMigration}
                disabled={isLoading || !mysqlConnected}
                className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                验证迁移
              </button>
              <button
                onClick={clearMySQLData}
                disabled={isLoading || !mysqlConnected}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                清空数据
              </button>
            </div>
          </div>

          {/* 状态显示 */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              操作状态
            </h2>
            <div className="flex items-center gap-2">
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              )}
              <span className="text-gray-900 dark:text-white">{status || '等待操作...'}</span>
            </div>
          </div>

          {/* 迁移结果 */}
          {migrationResult && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                迁移结果
              </h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">AI模型:</span>
                  <span className="ml-2 text-green-600 dark:text-green-400">
                    成功 {migrationResult.aiModels.success}, 失败 {migrationResult.aiModels.failed}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">设置:</span>
                  <span className="ml-2 text-green-600 dark:text-green-400">
                    成功 {migrationResult.settings.success}, 失败 {migrationResult.settings.failed}
                  </span>
                </div>
                <div className="col-span-2">
                  <span className="font-medium text-gray-700 dark:text-gray-300">总耗时:</span>
                  <span className="ml-2 text-gray-900 dark:text-white">{migrationResult.totalTime}ms</span>
                </div>
              </div>
            </div>
          )}

          {/* AI模型列表 */}
          {models.length > 0 && (
            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                MySQL中的AI模型 ({models.length})
              </h2>
              <div className="space-y-2">
                {models.map((model, index) => (
                  <div key={model.id || index} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border">
                    <div>
                      <span className="font-medium text-gray-900 dark:text-white">{model.name}</span>
                      <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">({model.model_name})</span>
                      {model.is_default && (
                        <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded">
                          默认
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      ID: {model.id}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
