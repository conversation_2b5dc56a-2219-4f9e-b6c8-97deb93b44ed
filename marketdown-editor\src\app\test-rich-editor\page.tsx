'use client';

import { useState } from 'react';
import { AdvancedRichTextEditor } from '@/components/ui/AdvancedRichTextEditor';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const sampleContent = `# 富文本编辑器测试

## 功能特点

这是一个功能强大的**富文本编辑器**，支持*Markdown*语法和可视化编辑。

### 支持的格式

1. **文本格式**：粗体、斜体、删除线
2. **标题**：一级到三级标题
3. **列表**：有序列表和无序列表
4. **链接**：[示例链接](https://example.com)
5. **图片**：![示例图片](https://via.placeholder.com/300x200)
6. **代码**：行内代码和代码块

### 代码示例

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

### 引用

> 这是一个引用示例
> 支持多行引用

### 表格

| 功能 | 支持 | 说明 |
|------|------|------|
| 粗体 | ✅ | **粗体文字** |
| 斜体 | ✅ | *斜体文字* |
| 代码 | ✅ | \`行内代码\` |
| 链接 | ✅ | [链接文字](url) |

---

### 快捷键

- **Ctrl+B**：粗体
- **Ctrl+I**：斜体
- **Ctrl+K**：插入链接
- **Ctrl+\`**：行内代码

### 待办事项

- [x] 基础文本格式
- [x] 标题支持
- [x] 列表支持
- [ ] 表格编辑
- [ ] 图片上传
- [ ] 更多快捷键`;

export default function TestRichEditor() {
  const [content, setContent] = useState(sampleContent);
  const [showPreview, setShowPreview] = useState(true);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            🎨 富文本编辑器测试
          </h1>
          
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-purple-800 mb-2">✨ 高级富文本编辑器特点</h2>
            <ul className="text-purple-700 space-y-1">
              <li>• <strong>双模式编辑</strong>：支持Markdown和富文本可视化编辑</li>
              <li>• <strong>完整字体控制</strong>：字体、字号、颜色、背景色设置</li>
              <li>• <strong>丰富文本格式</strong>：粗体、斜体、下划线、删除线、上标、下标</li>
              <li>• <strong>专业排版</strong>：对齐方式、缩进、列表、引用、代码块</li>
              <li>• <strong>媒体支持</strong>：图片、链接、表格插入和编辑</li>
              <li>• <strong>编辑辅助</strong>：撤销重做、格式清除、查找替换、全选</li>
              <li>• <strong>快捷键支持</strong>：完整的键盘快捷键操作</li>
            </ul>
          </div>

          <div className="flex gap-4 mb-6">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                showPreview
                  ? 'bg-blue-500 hover:bg-blue-600 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              {showPreview ? '隐藏预览' : '显示预览'}
            </button>
            
            <button
              onClick={() => setContent(sampleContent)}
              className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md font-medium transition-colors"
            >
              重置内容
            </button>
          </div>
        </div>

        <div className={`grid gap-6 ${showPreview ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'}`}>
          {/* 富文本编辑器 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-4 border-b border-gray-200 dark:border-gray-600">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                🎨 富文本编辑器
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                支持Markdown和可视化编辑，工具栏提供常用格式化功能
              </p>
            </div>
            <div className="p-4">
              <AdvancedRichTextEditor
                content={content}
                onChange={setContent}
                className="border border-gray-300 dark:border-gray-600 rounded-md"
              />
            </div>
          </div>

          {/* 预览 */}
          {showPreview && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-4 border-b border-gray-200 dark:border-gray-600">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  👀 实时预览
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Markdown渲染效果预览
                </p>
              </div>
              <div className="p-4">
                <div className="h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-700">
                  <div className="prose dark:prose-invert max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {content}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 功能说明 */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              🎨 文本样式控制
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li>• <strong>字体选择</strong> 微软雅黑、宋体等</li>
              <li>• <strong>字号调整</strong> 初号到小六号</li>
              <li>• <strong>字重控制</strong> 粗体、正常、细体</li>
              <li>• <strong>字形设置</strong> 斜体、正常</li>
              <li>• <strong>颜色设置</strong> 文字色、背景色</li>
              <li>• <strong>文本装饰</strong> 下划线、删除线</li>
              <li>• <strong>上下标</strong> X²、H₂O格式</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              📐 结构排版功能
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li>• <strong>对齐方式</strong> 左、右、居中、两端</li>
              <li>• <strong>缩进控制</strong> 增加、减少缩进</li>
              <li>• <strong>列表结构</strong> 有序、无序列表</li>
              <li>• <strong>标题层级</strong> H1-H6标题</li>
              <li>• <strong>引用格式</strong> 块引用样式</li>
              <li>• <strong>代码块</strong> 等宽字体代码</li>
              <li>• <strong>分隔线</strong> 水平分割线</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              🖼️ 媒体与元素
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li>• <strong>图片插入</strong> 本地/网络图片</li>
              <li>• <strong>链接创建</strong> 超链接插入</li>
              <li>• <strong>表格编辑</strong> 自定义行列表格</li>
              <li>• <strong>表格样式</strong> 边框、背景设置</li>
              <li>• <strong>媒体嵌入</strong> 多媒体内容</li>
              <li>• <strong>特殊符号</strong> 数学、希腊字母</li>
              <li>• <strong>公式支持</strong> LaTeX数学公式</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              ⚡ 交互与辅助
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li>• <strong>撤销重做</strong> Ctrl+Z/Y</li>
              <li>• <strong>查找替换</strong> 文本查找</li>
              <li>• <strong>全选操作</strong> Ctrl+A</li>
              <li>• <strong>格式清除</strong> 一键清除样式</li>
              <li>• <strong>快捷键</strong> 完整键盘支持</li>
              <li>• <strong>自动保存</strong> 实时保存内容</li>
              <li>• <strong>格式兼容</strong> 多格式导入导出</li>
            </ul>
          </div>
        </div>

        {/* 快捷键说明 */}
        <div className="mt-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            ⌨️ 完整快捷键列表
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">文本格式</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• <strong>Ctrl+B</strong> 粗体</li>
                <li>• <strong>Ctrl+I</strong> 斜体</li>
                <li>• <strong>Ctrl+U</strong> 下划线</li>
                <li>• <strong>Ctrl+Shift+S</strong> 删除线</li>
                <li>• <strong>Ctrl+Shift+=</strong> 上标</li>
                <li>• <strong>Ctrl+=</strong> 下标</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">编辑操作</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• <strong>Ctrl+Z</strong> 撤销</li>
                <li>• <strong>Ctrl+Y</strong> 重做</li>
                <li>• <strong>Ctrl+A</strong> 全选</li>
                <li>• <strong>Ctrl+C</strong> 复制</li>
                <li>• <strong>Ctrl+V</strong> 粘贴</li>
                <li>• <strong>Ctrl+X</strong> 剪切</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">对齐与列表</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• <strong>Ctrl+L</strong> 左对齐</li>
                <li>• <strong>Ctrl+E</strong> 居中对齐</li>
                <li>• <strong>Ctrl+R</strong> 右对齐</li>
                <li>• <strong>Ctrl+J</strong> 两端对齐</li>
                <li>• <strong>Tab</strong> 增加缩进</li>
                <li>• <strong>Shift+Tab</strong> 减少缩进</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            📖 使用说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">在主应用中使用</h4>
              <ol className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>1. 在主编辑器工具栏中找到编辑器模式切换按钮</li>
                <li>2. 点击"🎨 富文本"切换到富文本编辑模式</li>
                <li>3. 使用工具栏按钮或快捷键进行格式化</li>
                <li>4. 随时可以切换回"📝 Markdown"模式</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">字体与颜色设置</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• 点击"字体"按钮打开字体面板</li>
                <li>• 选择字体族和字号大小</li>
                <li>• 点击"🎨"按钮设置文字和背景颜色</li>
                <li>• 使用颜色预设或自定义颜色</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">表格与媒体</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• 点击"⊞"按钮插入表格</li>
                <li>• 选择合适的行列数量</li>
                <li>• 使用"🔗"插入链接</li>
                <li>• 使用"🖼️"插入图片</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
