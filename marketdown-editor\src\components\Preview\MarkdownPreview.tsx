'use client';

import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import { useEditorStore } from '@/stores/useEditorStore';

interface MarkdownPreviewProps {
  className?: string;
}

export const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ className = '' }) => {
  const { currentDocument, editorConfig } = useEditorStore();

  const markdownContent = useMemo(() => {
    return currentDocument?.content || '';
  }, [currentDocument?.content]);

  // 自定义组件渲染
  const components = useMemo(() => ({
    // 自定义代码块渲染
    code: ({ node, inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <pre className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
          <code className={className} {...props}>
            {children}
          </code>
        </pre>
      ) : (
        <code 
          className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm" 
          {...props}
        >
          {children}
        </code>
      );
    },
    
    // 自定义表格渲染
    table: ({ children }: any) => (
      <div className="overflow-x-auto my-4">
        <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
          {children}
        </table>
      </div>
    ),
    
    th: ({ children }: any) => (
      <th className="border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-4 py-2 text-left font-semibold">
        {children}
      </th>
    ),
    
    td: ({ children }: any) => (
      <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
        {children}
      </td>
    ),
    
    // 自定义引用块渲染
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 dark:bg-blue-900/20 italic">
        {children}
      </blockquote>
    ),
    
    // 自定义标题渲染
    h1: ({ children }: any) => (
      <h1 className="text-3xl font-bold mt-8 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
        {children}
      </h1>
    ),
    
    h2: ({ children }: any) => (
      <h2 className="text-2xl font-bold mt-6 mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
        {children}
      </h2>
    ),
    
    h3: ({ children }: any) => (
      <h3 className="text-xl font-bold mt-5 mb-3">{children}</h3>
    ),
    
    h4: ({ children }: any) => (
      <h4 className="text-lg font-bold mt-4 mb-2">{children}</h4>
    ),
    
    h5: ({ children }: any) => (
      <h5 className="text-base font-bold mt-3 mb-2">{children}</h5>
    ),
    
    h6: ({ children }: any) => (
      <h6 className="text-sm font-bold mt-2 mb-2">{children}</h6>
    ),
    
    // 自定义链接渲染
    a: ({ href, children }: any) => (
      <a 
        href={href} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-blue-600 dark:text-blue-400 hover:underline"
      >
        {children}
      </a>
    ),
    
    // 自定义列表渲染
    ul: ({ children }: any) => (
      <ul className="list-disc list-inside my-4 space-y-1">{children}</ul>
    ),
    
    ol: ({ children }: any) => (
      <ol className="list-decimal list-inside my-4 space-y-1">{children}</ol>
    ),
    
    // 自定义段落渲染
    p: ({ children }: any) => (
      <p className="my-4 leading-relaxed">{children}</p>
    ),
    
    // 自定义图片渲染
    img: ({ src, alt }: any) => (
      <img 
        src={src} 
        alt={alt} 
        className="max-w-full h-auto rounded-lg shadow-md my-4"
        loading="lazy"
      />
    ),
  }), []);

  if (!currentDocument) {
    return (
      <div className={`flex items-center justify-center h-full text-gray-500 ${className}`}>
        <div className="text-center">
          <p className="text-lg mb-2">No content to preview</p>
          <p className="text-sm">Start writing to see the preview</p>
        </div>
      </div>
    );
  }

  if (!markdownContent.trim()) {
    return (
      <div className={`flex items-center justify-center h-full text-gray-500 ${className}`}>
        <div className="text-center">
          <p className="text-lg mb-2">Empty document</p>
          <p className="text-sm">Write some markdown to see the preview</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full overflow-auto ${className}`}>
      <div className="p-6 max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkBreaks]}
          rehypePlugins={[rehypeHighlight, rehypeRaw]}
          components={components}
          className="prose prose-gray dark:prose-invert max-w-none"
        >
          {markdownContent}
        </ReactMarkdown>
      </div>
    </div>
  );
};
