'use client';

import { useState, useCallback, useRef } from 'react';

export interface Version {
  id: string;
  content: string;
  timestamp: Date;
  description?: string;
}

interface UseVersionHistoryOptions {
  maxVersions?: number;
  autoSaveInterval?: number;
}

export const useVersionHistory = (
  initialContent: string = '',
  options: UseVersionHistoryOptions = {}
) => {
  const { maxVersions = 50, autoSaveInterval = 5000 } = options;
  
  const [versions, setVersions] = useState<Version[]>([
    {
      id: 'initial',
      content: initialContent,
      timestamp: new Date(),
      description: '初始版本',
    },
  ]);
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const lastSaveTime = useRef<number>(Date.now());
  const autoSaveTimer = useRef<NodeJS.Timeout>();

  // 获取当前版本
  const getCurrentVersion = useCallback((): Version => {
    return versions[currentIndex];
  }, [versions, currentIndex]);

  // 获取当前内容
  const getCurrentContent = useCallback((): string => {
    return getCurrentVersion().content;
  }, [getCurrentVersion]);

  // 添加新版本
  const addVersion = useCallback((content: string, description?: string) => {
    const newVersion: Version = {
      id: `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      timestamp: new Date(),
      description,
    };

    setVersions(prev => {
      // 如果当前不在最新版本，删除后面的版本
      const newVersions = prev.slice(0, currentIndex + 1);
      newVersions.push(newVersion);
      
      // 限制版本数量
      if (newVersions.length > maxVersions) {
        return newVersions.slice(-maxVersions);
      }
      
      return newVersions;
    });

    setCurrentIndex(prev => {
      const newIndex = Math.min(prev + 1, maxVersions - 1);
      return newIndex;
    });

    lastSaveTime.current = Date.now();
  }, [currentIndex, maxVersions]);

  // 自动保存版本
  const autoSave = useCallback((content: string) => {
    if (autoSaveTimer.current) {
      clearTimeout(autoSaveTimer.current);
    }

    autoSaveTimer.current = setTimeout(() => {
      const currentContent = getCurrentContent();
      if (content !== currentContent && content.trim() !== '') {
        addVersion(content, '自动保存');
      }
    }, autoSaveInterval);
  }, [autoSaveInterval, getCurrentContent, addVersion]);

  // 手动保存版本
  const saveVersion = useCallback((content: string, description?: string) => {
    if (autoSaveTimer.current) {
      clearTimeout(autoSaveTimer.current);
    }
    addVersion(content, description || '手动保存');
  }, [addVersion]);

  // 撤销（回到上一个版本）
  const undo = useCallback((): string | null => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      return versions[currentIndex - 1].content;
    }
    return null;
  }, [currentIndex, versions]);

  // 重做（前进到下一个版本）
  const redo = useCallback((): string | null => {
    if (currentIndex < versions.length - 1) {
      setCurrentIndex(prev => prev + 1);
      return versions[currentIndex + 1].content;
    }
    return null;
  }, [currentIndex, versions]);

  // 跳转到指定版本
  const goToVersion = useCallback((versionId: string): string | null => {
    const index = versions.findIndex(v => v.id === versionId);
    if (index !== -1) {
      setCurrentIndex(index);
      return versions[index].content;
    }
    return null;
  }, [versions]);

  // 删除版本
  const deleteVersion = useCallback((versionId: string): boolean => {
    if (versions.length <= 1) return false; // 至少保留一个版本
    
    const index = versions.findIndex(v => v.id === versionId);
    if (index === -1) return false;

    setVersions(prev => prev.filter(v => v.id !== versionId));
    
    // 调整当前索引
    if (index <= currentIndex) {
      setCurrentIndex(prev => Math.max(0, prev - 1));
    }
    
    return true;
  }, [versions, currentIndex]);

  // 比较两个版本
  const compareVersions = useCallback((versionId1: string, versionId2: string) => {
    const version1 = versions.find(v => v.id === versionId1);
    const version2 = versions.find(v => v.id === versionId2);
    
    if (!version1 || !version2) return null;
    
    return {
      version1,
      version2,
      // 简单的差异检测（可以使用更复杂的diff算法）
      differences: getDifferences(version1.content, version2.content),
    };
  }, [versions]);

  // 获取版本统计
  const getStats = useCallback(() => {
    return {
      totalVersions: versions.length,
      currentIndex,
      canUndo: currentIndex > 0,
      canRedo: currentIndex < versions.length - 1,
      oldestVersion: versions[0],
      newestVersion: versions[versions.length - 1],
      currentVersion: getCurrentVersion(),
    };
  }, [versions, currentIndex, getCurrentVersion]);

  // 清理定时器
  const cleanup = useCallback(() => {
    if (autoSaveTimer.current) {
      clearTimeout(autoSaveTimer.current);
    }
  }, []);

  return {
    versions,
    currentIndex,
    getCurrentVersion,
    getCurrentContent,
    addVersion,
    autoSave,
    saveVersion,
    undo,
    redo,
    goToVersion,
    deleteVersion,
    compareVersions,
    getStats,
    cleanup,
  };
};

// 简单的文本差异检测
function getDifferences(text1: string, text2: string) {
  const lines1 = text1.split('\n');
  const lines2 = text2.split('\n');
  
  const differences: Array<{
    type: 'added' | 'removed' | 'unchanged';
    content: string;
    lineNumber: number;
  }> = [];
  
  const maxLines = Math.max(lines1.length, lines2.length);
  
  for (let i = 0; i < maxLines; i++) {
    const line1 = lines1[i];
    const line2 = lines2[i];
    
    if (line1 === undefined) {
      differences.push({
        type: 'added',
        content: line2,
        lineNumber: i + 1,
      });
    } else if (line2 === undefined) {
      differences.push({
        type: 'removed',
        content: line1,
        lineNumber: i + 1,
      });
    } else if (line1 !== line2) {
      differences.push({
        type: 'removed',
        content: line1,
        lineNumber: i + 1,
      });
      differences.push({
        type: 'added',
        content: line2,
        lineNumber: i + 1,
      });
    } else {
      differences.push({
        type: 'unchanged',
        content: line1,
        lineNumber: i + 1,
      });
    }
  }
  
  return differences;
}
