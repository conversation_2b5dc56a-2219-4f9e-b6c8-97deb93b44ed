'use client';

import { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useKeyboardShortcuts, createEditorShortcuts } from '@/hooks/useKeyboardShortcuts';
import { FindReplaceDialog } from '@/components/ui/FindReplaceDialog';
import { ExportDialog } from '@/components/ui/ExportDialog';
import { AIPanel } from '@/components/AIAssistant';
import { TranslateWidget } from '@/components/ui/TranslateWidget';
import { CopyButton, QuickCopyButton } from '@/components/ui/CopyButton';
import { AdvancedRichTextEditor } from '@/components/ui/AdvancedRichTextEditor';
import { UEditor } from '@/components/ui/UEditor';
import { IntelligentFormatting } from '@/components/ui/IntelligentFormatting';
import { useAutoSave } from '@/hooks/useAutoSave';
import { useTheme } from '@/hooks/useTheme';
import { useEditorNavigation } from '@/hooks/useEditorNavigation';


export default function Home() {
  const [content, setContent] = useState('# Welcome to Martetdown\n\nStart typing your markdown here...\n\n## Features\n\n- **Real-time preview**\n- *Markdown syntax support*\n- `Code highlighting`\n\n### Try it out!\n\nEdit the text on the left and see the preview on the right.');
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [showFindReplace, setShowFindReplace] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [fontSize, setFontSize] = useState(14);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [useRichEditor, setUseRichEditor] = useState(false);
  const [viewMode, setViewMode] = useState<'split' | 'edit' | 'preview'>('split');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const lineNumbersRef = useRef<HTMLDivElement>(null);

  // 主题功能
  const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();

  // 自动保存功能
  const autoSave = useAutoSave(content, {
    key: 'martetdown-content',
    delay: 2000,
    enabled: true,
  });

  // 编辑器导航功能
  const editorNavigation = useEditorNavigation({
    textareaRef,
    content
  });

  // 页面加载时恢复内容
  useEffect(() => {
    const savedContent = autoSave.loadFromStorage();
    if (savedContent && savedContent !== content) {
      const shouldRestore = confirm('检测到未保存的内容，是否恢复？');
      if (shouldRestore) {
        setContent(savedContent);
      }
    }
  }, []);

  // 同步行号滚动
  const syncLineNumbers = () => {
    if (!textareaRef.current || !lineNumbersRef.current) return;

    const textarea = textareaRef.current;
    const lineNumbers = lineNumbersRef.current;

    lineNumbers.scrollTop = textarea.scrollTop;
  };

  // 更新行号
  const updateLineNumbers = () => {
    const lines = content.split('\n').length;
    return Array.from({ length: lines }, (_, i) => i + 1);
  };

  // 生成HTML内容用于导出
  const generateHTMLContent = () => {
    // 简单的Markdown到HTML转换（避免SSR问题）
    return content
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  };

  const insertText = (before: string, after: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    const newContent = content.substring(0, start) + before + selectedText + after + content.substring(end);

    setContent(newContent);

    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);
    }, 0);
  };

  const downloadFile = () => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const loadFile = (event?: React.ChangeEvent<HTMLInputElement>) => {
    const file = event?.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        setContent(text);
      };
      reader.readAsText(file);
    }
  };

  const triggerFileOpen = () => {
    fileInputRef.current?.click();
  };

  const newDocument = () => {
    if (confirm('创建新文档？未保存的更改将丢失。')) {
      setContent('# 新文档\n\n开始编写您的内容...');
    }
  };

  // 快捷键配置
  const shortcuts = createEditorShortcuts({
    bold: () => insertText('**', '**'),
    italic: () => insertText('*', '*'),
    save: downloadFile,
    open: triggerFileOpen,
    newDocument,
    find: () => setShowFindReplace(true),
    replace: () => setShowFindReplace(true),
    undo: () => {}, // TODO: 实现撤销功能
    redo: () => {}, // TODO: 实现重做功能
    selectAll: () => textareaRef.current?.select(),
    copy: () => navigator.clipboard?.writeText(textareaRef.current?.value || ''),
    paste: () => {}, // 浏览器默认处理
    cut: () => {}, // 浏览器默认处理
  });

  useKeyboardShortcuts(shortcuts);

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      {!isFocusMode && (
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Martetdown
          </h1>

          {/* Toolbar */}
          <div className="flex items-center gap-2">
            {/* 主题切换 */}
            <div className="flex items-center gap-1 mr-4">
              <button
                onClick={() => setTheme('light')}
                className={`px-2 py-1 rounded text-sm ${
                  theme === 'light'
                    ? 'bg-yellow-200 text-yellow-800'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="亮色主题"
              >
                ☀️
              </button>
              <button
                onClick={() => setTheme('dark')}
                className={`px-2 py-1 rounded text-sm ${
                  theme === 'dark'
                    ? 'bg-blue-200 text-blue-800'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="暗色主题"
              >
                🌙
              </button>
              <button
                onClick={() => setTheme('system')}
                className={`px-2 py-1 rounded text-sm ${
                  theme === 'system'
                    ? 'bg-green-200 text-green-800'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="跟随系统"
              >
                🖥️
              </button>
            </div>

            {/* 翻译组件 */}
            <TranslateWidget className="mr-4" />
            {/* File operations */}
            <label className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium cursor-pointer">
              打开
              <input
                ref={fileInputRef}
                type="file"
                accept=".md,.txt,.markdown"
                onChange={loadFile}
                className="hidden"
              />
            </label>
            <button
              onClick={downloadFile}
              className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm font-medium"
              title="保存 (Ctrl+S)"
            >
              保存
            </button>
            <button
              onClick={newDocument}
              className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium"
              title="新建 (Ctrl+N)"
            >
              新建
            </button>

            <button
              onClick={() => setShowExportDialog(true)}
              className="px-3 py-1 bg-indigo-500 hover:bg-indigo-600 text-white rounded text-sm font-medium"
              title="导出文档"
            >
              导出
            </button>

            {/* 复制到Word按钮 */}
            <CopyButton
              content={content}
              theme="modern"
              size="sm"
              variant="outline"
              className="border-green-500 text-green-600 hover:bg-green-500 hover:text-white"
            />

            {/* 编辑器模式切换 */}
            <button
              onClick={() => setUseRichEditor(!useRichEditor)}
              className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                useRichEditor
                  ? 'bg-purple-500 hover:bg-purple-600 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300'
              }`}
              title={useRichEditor ? '切换到Markdown编辑器' : '切换到富文本编辑器'}
            >
              {useRichEditor ? '🎨 富文本' : '📝 Markdown'}
            </button>

            <button
              onClick={() => setShowAIPanel(true)}
              className="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded text-sm font-medium"
              title="AI助手"
            >
              🤖 AI
            </button>

            <IntelligentFormatting
              content={content}
              onContentChange={setContent}
              className="ml-2"
            />

            <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

            {/* Formatting */}
            <button
              onClick={() => insertText('**', '**')}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium"
              title="粗体 (Ctrl+B)"
            >
              B
            </button>
            <button
              onClick={() => insertText('*', '*')}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium"
              title="斜体 (Ctrl+I)"
            >
              I
            </button>
            <button
              onClick={() => insertText('`', '`')}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium"
              title="代码"
            >
              代码
            </button>
            <button
              onClick={() => insertText('\n## ', '')}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm font-medium"
              title="标题"
            >
              H2
            </button>

            <button
              onClick={() => setShowFindReplace(true)}
              className="px-3 py-1 bg-orange-100 hover:bg-orange-200 dark:bg-orange-800 dark:hover:bg-orange-700 rounded text-sm font-medium"
              title="查找替换 (Ctrl+F)"
            >
              查找
            </button>

            <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

            {/* 编辑器设置 */}
            <button
              onClick={() => setShowLineNumbers(!showLineNumbers)}
              className={`px-3 py-1 rounded text-sm font-medium ${
                showLineNumbers
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
              }`}
              title="切换行号"
            >
              行号
            </button>

            <div className="flex items-center gap-1">
              <button
                onClick={() => setFontSize(Math.max(10, fontSize - 1))}
                className="px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                title="减小字体"
              >
                A-
              </button>
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[2rem] text-center">
                {fontSize}
              </span>
              <button
                onClick={() => setFontSize(Math.min(24, fontSize + 1))}
                className="px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                title="增大字体"
              >
                A+
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

            {/* 视图模式 */}
            <div className="flex items-center gap-1">
              <button
                onClick={() => setViewMode('edit')}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  viewMode === 'edit'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="仅编辑"
              >
                编辑
              </button>
              <button
                onClick={() => setViewMode('split')}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  viewMode === 'split'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="分屏"
              >
                分屏
              </button>
              <button
                onClick={() => setViewMode('preview')}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  viewMode === 'preview'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="仅预览"
              >
                预览
              </button>
            </div>

            <button
              onClick={() => setIsFocusMode(!isFocusMode)}
              className={`px-3 py-1 rounded text-sm font-medium ${
                isFocusMode
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
              }`}
              title="专注模式"
            >
              专注
            </button>

            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className={`px-3 py-1 rounded text-sm font-medium ${
                isFullscreen
                  ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600'
              }`}
              title="全屏模式"
            >
              {isFullscreen ? '退出' : '全屏'}
            </button>

            <button
              onClick={() => setShowShortcuts(!showShortcuts)}
              className="px-3 py-1 bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-800 dark:hover:bg-yellow-700 rounded text-sm font-medium"
              title="快捷键帮助"
            >
              ?
            </button>
          </div>
        </div>
      </header>
      )}

      {/* Main Content */}
      <div className={`flex ${isFocusMode ? 'h-screen' : 'h-[calc(100vh-73px)]'}`}>
        {/* Editor */}
        {(viewMode === 'edit' || viewMode === 'split') && (
          <div className={`${viewMode === 'split' ? 'w-1/2' : 'w-full'} ${viewMode === 'split' ? 'border-r border-gray-200 dark:border-gray-700' : ''} relative flex`}>
          {/* 行号 */}
          {showLineNumbers && (
            <div
              ref={lineNumbersRef}
              className="w-12 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-hidden"
              style={{ fontSize: `${fontSize}px` }}
            >
              <div className="py-6 px-2 font-mono text-gray-500 dark:text-gray-400 text-right leading-relaxed select-none">
                {updateLineNumbers().map((lineNum) => (
                  <div key={lineNum} className="whitespace-nowrap">
                    {lineNum}
                  </div>
                ))}
              </div>
            </div>
          )}

          {useRichEditor ? (
            <UEditor
              content={content}
              onChange={setContent}
              className="flex-1 h-full"
              height={600}
            />
          ) : (
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onScroll={syncLineNumbers}
              className={`flex-1 h-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono resize-none border-none outline-none leading-relaxed no-translate ${
                showLineNumbers ? 'pl-4 pr-6 py-6' : 'p-6'
              }`}
              style={{ fontSize: `${fontSize}px` }}
              placeholder="开始输入您的Markdown内容..."
            />
          )}

          {/* 快捷键帮助面板 */}
          {showShortcuts && (
            <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-4 max-w-xs z-10">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">快捷键</h3>
                <button
                  onClick={() => setShowShortcuts(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ×
                </button>
              </div>
              <div className="space-y-2 text-sm">
                {shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">{shortcut.description}</span>
                    <span className="font-mono text-gray-800 dark:text-gray-200">
                      Ctrl+{shortcut.key.toUpperCase()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        )}

        {/* Preview */}
        {(viewMode === 'preview' || viewMode === 'split') && (
          <div className={`${viewMode === 'split' ? 'w-1/2' : 'w-full'} bg-white dark:bg-gray-800 p-6 overflow-auto`}>
            <div className="prose dark:prose-invert max-w-none">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {content}
              </ReactMarkdown>
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      {!isFocusMode && (
        <div className="bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-4">
            <span>字符: {content.length}</span>
            <span>单词: {content.trim() ? content.trim().split(/\s+/).length : 0}</span>
            <span>行数: {content.split('\n').length}</span>
            {autoSave.isSaving && (
              <span className="text-blue-500">保存中...</span>
            )}
            {autoSave.hasUnsavedChanges && !autoSave.isSaving && (
              <span className="text-orange-500">有未保存更改</span>
            )}
            {autoSave.lastSaved && !autoSave.hasUnsavedChanges && (
              <span className="text-green-500">
                已保存 {autoSave.lastSaved.toLocaleTimeString()}
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <button
              onClick={autoSave.save}
              className="text-blue-500 hover:text-blue-700 underline"
              title="手动保存"
            >
              立即保存
            </button>
            <span>Martetdown Editor v1.0</span>
          </div>
        </div>
      </div>
      )}

      {/* 查找替换对话框 */}
      <FindReplaceDialog
        isOpen={showFindReplace}
        onClose={() => setShowFindReplace(false)}
        content={content}
        onContentChange={setContent}
        textareaRef={textareaRef}
      />

      {/* 导出对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        content={content}
        htmlContent={generateHTMLContent()}
      />

      {/* AI助手面板 */}
      <AIPanel
        isOpen={showAIPanel}
        onClose={() => setShowAIPanel(false)}
        content={content}
        onContentChange={setContent}
      />
    </div>
  );
}
