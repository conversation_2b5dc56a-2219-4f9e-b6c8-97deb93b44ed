"""
Excel格式转换器
"""

import os
import subprocess
from typing import Optional, List, Dict
import pandas as pd
import openpyxl

from .base_converter import BaseConverter
from ..exceptions import ConversionError


class XlsxConverter(BaseConverter):
    """Excel转换器"""
    
    @property
    def supported_input_formats(self) -> list:
        return ['xlsx']
    
    @property
    def supported_output_formats(self) -> list:
        return ['pdf', 'docx', 'txt', 'md']
    
    def convert_to_pdf(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Excel转PDF
        """
        try:
            # 尝试使用LibreOffice
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', os.path.dirname(output_path),
                input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # LibreOffice可能生成的文件名与期望不同
                expected_pdf = os.path.join(
                    os.path.dirname(output_path),
                    os.path.splitext(os.path.basename(input_path))[0] + '.pdf'
                )
                
                if os.path.exists(expected_pdf) and expected_pdf != output_path:
                    import shutil
                    shutil.move(expected_pdf, output_path)
                
                return os.path.exists(output_path)
                
        except FileNotFoundError:
            self.logger.warning("LibreOffice不可用，使用备用方法")
        
        # 备用方法：使用reportlab
        return self._convert_xlsx_to_pdf_alternative(input_path, output_path, **kwargs)
    
    def convert_to_docx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Excel转Word
        将表格转换为Word文档中的表格
        """
        try:
            from docx import Document
            from docx.shared import Inches
            
            # 读取Excel文件
            excel_data = self._read_excel_sheets(input_path)
            
            if not excel_data:
                return False
            
            # 创建Word文档
            doc = Document()
            
            for sheet_name, df in excel_data.items():
                if len(excel_data) > 1:
                    # 如果有多个工作表，添加标题
                    doc.add_heading(f'工作表: {sheet_name}', level=1)
                
                # 创建表格
                if not df.empty:
                    # 添加表格（包含标题行）
                    table = doc.add_table(rows=1, cols=len(df.columns))
                    table.style = 'Table Grid'
                    
                    # 添加标题行
                    header_cells = table.rows[0].cells
                    for i, column_name in enumerate(df.columns):
                        header_cells[i].text = str(column_name)
                    
                    # 添加数据行
                    for _, row in df.iterrows():
                        row_cells = table.add_row().cells
                        for i, value in enumerate(row):
                            row_cells[i].text = str(value) if pd.notna(value) else ''
                
                # 添加分页符（除了最后一个工作表）
                if sheet_name != list(excel_data.keys())[-1]:
                    doc.add_page_break()
            
            # 保存文档
            doc.save(output_path)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Excel转Word失败: {str(e)}")
            return False
    
    def convert_to_xlsx(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Excel转Excel（复制文件）
        """
        try:
            import shutil
            shutil.copy2(input_path, output_path)
            return os.path.exists(output_path)
        except Exception as e:
            self.logger.error(f"Excel复制失败: {str(e)}")
            return False
    
    def convert_to_txt(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Excel转纯文本
        """
        try:
            # 读取Excel文件
            excel_data = self._read_excel_sheets(input_path)
            
            if not excel_data:
                return False
            
            text_content = []
            
            for sheet_name, df in excel_data.items():
                if len(excel_data) > 1:
                    text_content.append(f"=== 工作表: {sheet_name} ===")
                    text_content.append("")
                
                if not df.empty:
                    # 转换为制表符分隔的文本
                    text_content.append(df.to_csv(sep='\t', index=False))
                else:
                    text_content.append("(空工作表)")
                
                text_content.append("")
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(text_content))
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Excel转文本失败: {str(e)}")
            return False
    
    def convert_to_md(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        Excel转Markdown
        将表格转换为Markdown表格格式
        """
        try:
            # 读取Excel文件
            excel_data = self._read_excel_sheets(input_path)
            
            if not excel_data:
                return False
            
            markdown_content = []
            
            for sheet_name, df in excel_data.items():
                if len(excel_data) > 1:
                    markdown_content.append(f"# 工作表: {sheet_name}")
                    markdown_content.append("")
                
                if not df.empty:
                    # 转换为Markdown表格
                    markdown_table = self._dataframe_to_markdown_table(df)
                    markdown_content.append(markdown_table)
                else:
                    markdown_content.append("*(空工作表)*")
                
                markdown_content.append("")
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))
            
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"Excel转Markdown失败: {str(e)}")
            return False
    
    def _read_excel_sheets(self, excel_path: str) -> Dict[str, pd.DataFrame]:
        """
        读取Excel文件的所有工作表
        """
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(excel_path, sheet_name=None, engine='openpyxl')
            
            # 过滤空工作表
            filtered_data = {}
            for sheet_name, df in excel_data.items():
                # 检查是否为完全空的工作表
                if not df.empty or not df.dropna(how='all').empty:
                    filtered_data[sheet_name] = df
            
            return filtered_data
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {str(e)}")
            return {}
    
    def _dataframe_to_markdown_table(self, df: pd.DataFrame) -> str:
        """
        将DataFrame转换为Markdown表格
        """
        if df.empty:
            return "*(空表格)*"
        
        # 处理列名
        columns = [str(col) for col in df.columns]
        
        # 构建Markdown表格
        markdown_lines = []
        
        # 标题行
        header_line = "| " + " | ".join(columns) + " |"
        markdown_lines.append(header_line)
        
        # 分隔行
        separator_line = "| " + " | ".join(["---"] * len(columns)) + " |"
        markdown_lines.append(separator_line)
        
        # 数据行
        for _, row in df.iterrows():
            row_values = []
            for value in row:
                if pd.isna(value):
                    row_values.append("")
                else:
                    # 转换为字符串并处理特殊字符
                    str_value = str(value).replace("|", "\\|").replace("\n", " ")
                    row_values.append(str_value)
            
            data_line = "| " + " | ".join(row_values) + " |"
            markdown_lines.append(data_line)
        
        return "\n".join(markdown_lines)
    
    def _convert_xlsx_to_pdf_alternative(self, input_path: str, output_path: str, **kwargs) -> bool:
        """
        备用的Excel转PDF方法
        """
        try:
            from reportlab.lib.pagesizes import letter, A4, landscape
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib import colors
            from reportlab.lib.units import inch
            
            # 读取Excel数据
            excel_data = self._read_excel_sheets(input_path)
            
            if not excel_data:
                return False
            
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=landscape(A4))
            story = []
            styles = getSampleStyleSheet()
            
            for sheet_name, df in excel_data.items():
                if len(excel_data) > 1:
                    # 添加工作表标题
                    title = Paragraph(f"工作表: {sheet_name}", styles['Heading1'])
                    story.append(title)
                    story.append(Spacer(1, 12))
                
                if not df.empty:
                    # 准备表格数据
                    table_data = []
                    
                    # 添加标题行
                    headers = [str(col) for col in df.columns]
                    table_data.append(headers)
                    
                    # 添加数据行（限制行数以避免PDF过大）
                    max_rows = kwargs.get('max_rows', 100)
                    for i, (_, row) in enumerate(df.iterrows()):
                        if i >= max_rows:
                            break
                        row_data = [str(value) if pd.notna(value) else '' for value in row]
                        table_data.append(row_data)
                    
                    # 创建表格
                    table = Table(table_data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('FONTSIZE', (0, 1), (-1, -1), 8),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    
                    story.append(table)
                    
                    if i >= max_rows:
                        note = Paragraph(f"注：仅显示前{max_rows}行数据", styles['Normal'])
                        story.append(Spacer(1, 6))
                        story.append(note)
                
                # 添加分页符（除了最后一个工作表）
                if sheet_name != list(excel_data.keys())[-1]:
                    story.append(Spacer(1, 20))
            
            # 构建PDF
            doc.build(story)
            return os.path.exists(output_path)
            
        except Exception as e:
            self.logger.error(f"备用Excel转PDF失败: {str(e)}")
            return False
