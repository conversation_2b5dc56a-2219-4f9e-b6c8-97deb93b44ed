#!/usr/bin/env python3
"""
简化的Web应用启动脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

if __name__ == '__main__':
    try:
        from web_app.app import app
        print("🚀 文档转换器Web应用启动中...")
        print("📋 新功能: 支持最大10GB文件!")
        print("🌐 访问地址: http://localhost:5000")
        print("⚠️  按 Ctrl+C 停止服务")
        print("-" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
