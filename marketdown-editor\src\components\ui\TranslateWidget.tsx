'use client';

import React, { useEffect, useState } from 'react';

// 声明全局translate对象
declare global {
  interface Window {
    translate: {
      changeLanguage: (language: string) => void;
      execute: () => void;
      ignore: {
        tag: string[];
        class: string[];
      };
      setUseVersion2: () => void;
      setAutoDiscriminateLocalLanguage: () => void;
    };
  }
}

interface TranslateWidgetProps {
  className?: string;
}

export const TranslateWidget: React.FC<TranslateWidgetProps> = ({ className = '' }) => {
  const [isTranslateReady, setIsTranslateReady] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('chinese_simplified');

  // 检查translate.js是否加载完成
  useEffect(() => {
    const checkTranslate = () => {
      if (typeof window !== 'undefined' && window.translate) {
        setIsTranslateReady(true);
      } else {
        setTimeout(checkTranslate, 100);
      }
    };
    
    checkTranslate();
  }, []);

  // 语言选项
  const languages = [
    { code: 'chinese_simplified', name: '简体中文', flag: '🇨🇳' },
    { code: 'chinese_traditional', name: '繁體中文', flag: '🇹🇼' },
    { code: 'english', name: 'English', flag: '🇺🇸' },
    { code: 'japanese', name: '日本語', flag: '🇯🇵' },
    { code: 'korean', name: '한국어', flag: '🇰🇷' },
    { code: 'french', name: 'Français', flag: '🇫🇷' },
    { code: 'german', name: 'Deutsch', flag: '🇩🇪' },
    { code: 'spanish', name: 'Español', flag: '🇪🇸' },
    { code: 'russian', name: 'Русский', flag: '🇷🇺' },
    { code: 'arabic', name: 'العربية', flag: '🇸🇦' },
  ];

  const handleLanguageChange = (languageCode: string) => {
    if (isTranslateReady && window.translate) {
      window.translate.changeLanguage(languageCode);
      setCurrentLanguage(languageCode);
      
      // 保存用户选择的语言
      try {
        localStorage.setItem('martetdown-translate-language', languageCode);
      } catch (error) {
        console.error('Failed to save translate language:', error);
      }
    }
  };

  // 加载保存的语言设置
  useEffect(() => {
    if (isTranslateReady) {
      try {
        const savedLanguage = localStorage.getItem('martetdown-translate-language');
        if (savedLanguage) {
          setCurrentLanguage(savedLanguage);
          window.translate.changeLanguage(savedLanguage);
        }
      } catch (error) {
        console.error('Failed to load translate language:', error);
      }
    }
  }, [isTranslateReady]);

  if (!isTranslateReady) {
    return null;
  }

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  return (
    <div className={`relative inline-block ${className}`}>
      <div className="group">
        <button
          className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors"
          title="页面翻译"
        >
          <span>{currentLang.flag}</span>
          <span className="hidden sm:inline">{currentLang.name}</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {/* 下拉菜单 */}
        <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
          <div className="py-1">
            <div className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-600">
              选择翻译语言
            </div>
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`w-full flex items-center gap-3 px-3 py-2 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                  currentLanguage === language.code 
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                    : 'text-gray-700 dark:text-gray-300'
                }`}
              >
                <span>{language.flag}</span>
                <span>{language.name}</span>
                {currentLanguage === language.code && (
                  <svg className="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>
          
          <div className="border-t border-gray-200 dark:border-gray-600 px-3 py-2">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              💡 翻译功能会自动翻译页面内容，代码和编辑器内容不会被翻译
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 翻译工具函数
export const translateUtils = {
  // 手动触发翻译（用于动态内容）
  retranslate: () => {
    if (typeof window !== 'undefined' && window.translate) {
      window.translate.execute();
    }
  },
  
  // 切换到指定语言
  changeLanguage: (languageCode: string) => {
    if (typeof window !== 'undefined' && window.translate) {
      window.translate.changeLanguage(languageCode);
    }
  },
  
  // 添加忽略翻译的类名
  addIgnoreClass: (className: string) => {
    if (typeof window !== 'undefined' && window.translate) {
      if (!window.translate.ignore.class.includes(className)) {
        window.translate.ignore.class.push(className);
      }
    }
  },
  
  // 添加忽略翻译的标签
  addIgnoreTag: (tagName: string) => {
    if (typeof window !== 'undefined' && window.translate) {
      if (!window.translate.ignore.tag.includes(tagName)) {
        window.translate.ignore.tag.push(tagName);
      }
    }
  },
};
