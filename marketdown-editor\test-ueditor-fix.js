// 测试UEditor修复
console.log('=== UEditor错误修复测试 ===');

// 模拟UEditor对象
const mockUE = {
  getEditor: function(id, config) {
    console.log('创建UEditor实例:', id);
    
    return {
      ready: function(callback) {
        console.log('UEditor ready方法被调用');
        // 模拟异步初始化
        setTimeout(() => {
          console.log('UEditor初始化完成，执行回调');
          callback();
        }, 100);
      },
      
      setContent: function(content) {
        console.log('设置内容:', content.substring(0, 50) + '...');
      },
      
      getContent: function() {
        console.log('获取内容');
        return '<p>测试内容</p>';
      },
      
      addListener: function(event, callback) {
        console.log('添加监听器:', event);
      },
      
      destroy: function() {
        console.log('销毁UEditor实例');
      }
    };
  }
};

// 测试修复后的逻辑
function testUEditorFix() {
  console.log('\n--- 测试修复后的UEditor初始化逻辑 ---');
  
  const editorId = 'test-editor';
  const config = { height: 400 };
  
  try {
    // 模拟修复后的代码逻辑
    const editor = mockUE.getEditor(editorId, config);
    
    if (editor && typeof editor.ready === 'function') {
      console.log('✅ UEditor实例创建成功，ready方法存在');
      
      editor.ready(() => {
        console.log('✅ ready回调执行成功');
        
        // 测试内容设置
        if (typeof editor.setContent === 'function') {
          editor.setContent('测试内容');
          console.log('✅ 内容设置成功');
        }
        
        // 测试监听器添加
        if (typeof editor.addListener === 'function') {
          editor.addListener('contentChange', () => {
            console.log('内容变化监听器触发');
          });
          console.log('✅ 监听器添加成功');
        }
      });
    } else {
      console.log('❌ UEditor实例创建失败或ready方法不存在');
    }
  } catch (error) {
    console.error('❌ UEditor初始化失败:', error);
  }
}

// 测试错误情况
function testErrorCases() {
  console.log('\n--- 测试错误情况处理 ---');
  
  // 测试空对象
  try {
    const nullEditor = null;
    if (nullEditor && typeof nullEditor.ready === 'function') {
      nullEditor.ready(() => {});
    } else {
      console.log('✅ 正确处理了null编辑器');
    }
  } catch (error) {
    console.error('❌ null编辑器处理失败:', error);
  }
  
  // 测试没有ready方法的对象
  try {
    const invalidEditor = { someMethod: () => {} };
    if (invalidEditor && typeof invalidEditor.ready === 'function') {
      invalidEditor.ready(() => {});
    } else {
      console.log('✅ 正确处理了无ready方法的编辑器');
    }
  } catch (error) {
    console.error('❌ 无ready方法编辑器处理失败:', error);
  }
}

// 运行测试
testUEditorFix();
testErrorCases();

console.log('\n=== 测试完成 ===');
console.log('修复要点:');
console.log('1. 在调用ready方法前检查编辑器实例是否存在');
console.log('2. 检查ready方法是否为函数类型');
console.log('3. 在回调中再次检查编辑器实例');
console.log('4. 为所有编辑器方法调用添加类型检查');
console.log('5. 添加try-catch错误处理');
