'use client';

import React, { useState } from 'react';
import { formatStandards, getFormatStandard, FormatStandard } from '@/utils/formatStandards';
import { aiService } from '@/utils/ai';

interface PreciseFormatPanelProps {
  content: string;
  onContentChange: (content: string) => void;
  onClose: () => void;
}

export const PreciseFormatPanel: React.FC<PreciseFormatPanelProps> = ({
  content,
  onContentChange,
  onClose
}) => {
  const [selectedFormat, setSelectedFormat] = useState<FormatStandard | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState('');
  const [formatCheckResult, setFormatCheckResult] = useState<string>('');
  const [showFormatCheck, setShowFormatCheck] = useState(false);

  // 选择格式标准
  const handleFormatSelect = (formatId: string) => {
    const format = getFormatStandard(formatId);
    setSelectedFormat(format || null);
    setShowFormatCheck(false);
    setFormatCheckResult('');
  };

  // 执行精准格式优化
  const handlePreciseFormat = async () => {
    if (!selectedFormat || !content.trim()) {
      alert('请选择格式标准并确保有内容需要优化');
      return;
    }

    setIsProcessing(true);
    setProcessingStep('正在准备格式优化...');

    try {
      // 步骤1：格式检查
      setProcessingStep('正在检查当前格式...');
      const checkResponse = await aiService.checkDocumentFormat(content, selectedFormat.name);
      
      if (checkResponse.success && checkResponse.result) {
        setFormatCheckResult(checkResponse.result);
      }

      // 步骤2：精准优化
      setProcessingStep(`正在按照${selectedFormat.name}标准优化...`);
      const formatResponse = await aiService.formatDocumentPrecise(
        content, 
        selectedFormat.id, 
        selectedFormat.prompt
      );

      if (formatResponse.success && formatResponse.result) {
        setProcessingStep('优化完成！');
        onContentChange(formatResponse.result);
        
        // 显示格式检查结果
        setShowFormatCheck(true);
      } else {
        alert(`格式优化失败: ${formatResponse.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('精准格式优化失败:', error);
      alert('格式优化失败，请重试');
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };

  // 仅执行格式检查
  const handleFormatCheck = async () => {
    if (!selectedFormat || !content.trim()) {
      alert('请选择格式标准并确保有内容需要检查');
      return;
    }

    setIsProcessing(true);
    setProcessingStep('正在检查文档格式...');

    try {
      const response = await aiService.checkDocumentFormat(content, selectedFormat.name);
      
      if (response.success && response.result) {
        setFormatCheckResult(response.result);
        setShowFormatCheck(true);
      } else {
        alert(`格式检查失败: ${response.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('格式检查失败:', error);
      alert('格式检查失败，请重试');
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            🎯 精准格式优化
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* 功能说明 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
              ✨ 精准格式优化特点
            </h3>
            <ul className="text-blue-700 dark:text-blue-300 space-y-1 text-sm">
              <li>• <strong>具体明确</strong>：提供详细的格式规范，避免模糊的"优化格式"</li>
              <li>• <strong>标准化</strong>：基于学术、商务、简历等专业标准</li>
              <li>• <strong>可操作</strong>：每个要求都有具体的实施细节</li>
              <li>• <strong>自检验</strong>：AI会自动检查优化结果是否符合标准</li>
            </ul>
          </div>

          {/* 格式标准选择 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              选择格式标准
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {formatStandards.map((format) => (
                <button
                  key={format.id}
                  onClick={() => handleFormatSelect(format.id)}
                  className={`p-4 rounded-lg border-2 transition-all text-left ${
                    selectedFormat?.id === format.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{format.icon}</span>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {format.name}
                    </h4>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {format.description}
                  </p>
                </button>
              ))}
            </div>
          </div>

          {/* 选中格式的详细规则 */}
          {selectedFormat && (
            <div className="mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                📋 {selectedFormat.name} 格式规范
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2">标题格式</h5>
                  <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• {selectedFormat.rules.title.h1}</li>
                    <li>• {selectedFormat.rules.title.h2}</li>
                    <li>• {selectedFormat.rules.title.h3}</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2">正文格式</h5>
                  <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• {selectedFormat.rules.content.font}</li>
                    <li>• {selectedFormat.rules.content.spacing}</li>
                    <li>• {selectedFormat.rules.content.emphasis}</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* 处理状态显示 */}
          {isProcessing && (
            <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-blue-700 dark:text-blue-300 font-medium">
                  {processingStep}
                </span>
              </div>
            </div>
          )}

          {/* 格式检查结果 */}
          {showFormatCheck && formatCheckResult && (
            <div className="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
              <h4 className="font-medium text-green-800 dark:text-green-300 mb-2">
                📊 格式检查结果
              </h4>
              <div className="text-sm text-green-700 dark:text-green-300 whitespace-pre-wrap">
                {formatCheckResult}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-3">
            <button
              onClick={handleFormatCheck}
              disabled={!selectedFormat || isProcessing}
              className="flex-1 px-4 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white rounded-md transition-colors"
            >
              仅检查格式
            </button>
            <button
              onClick={handlePreciseFormat}
              disabled={!selectedFormat || isProcessing}
              className="flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-md transition-colors"
            >
              精准优化格式
            </button>
          </div>

          {/* 使用提示 */}
          <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-300 mb-2">
              💡 使用提示
            </h4>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• 选择最符合您文档用途的格式标准</li>
              <li>• 可以先"仅检查格式"了解当前问题</li>
              <li>• "精准优化格式"会按照标准重新整理文档</li>
              <li>• 优化后会显示详细的修改说明</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
