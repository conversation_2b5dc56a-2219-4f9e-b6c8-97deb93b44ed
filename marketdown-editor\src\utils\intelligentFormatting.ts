import { aiService } from './ai';

export interface TOCItem {
  level: number;
  title: string;
  anchor: string;
  line: number;
}

export interface FormatSuggestion {
  type: 'heading' | 'paragraph' | 'list' | 'table' | 'code' | 'quote' | 'link';
  line: number;
  column?: number;
  message: string;
  suggestion: string;
  severity: 'info' | 'warning' | 'error';
}

export interface DocumentStructure {
  toc: TOCItem[];
  wordCount: number;
  readingTime: number;
  headingCount: { [level: number]: number };
  suggestions: FormatSuggestion[];
}

class IntelligentFormattingService {
  // 生成目录
  generateTOC(content: string): TOCItem[] {
    const lines = content.split('\n');
    const toc: TOCItem[] = [];
    
    lines.forEach((line, index) => {
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headingMatch) {
        const level = headingMatch[1].length;
        const title = headingMatch[2].trim();
        const anchor = this.generateAnchor(title);
        
        toc.push({
          level,
          title,
          anchor,
          line: index + 1
        });
      }
    });
    
    return toc;
  }

  // 生成锚点
  private generateAnchor(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文字符
      .replace(/\s+/g, '-')
      .trim();
  }

  // 插入目录到文档
  insertTOCToDocument(content: string, tocPosition: 'top' | 'after-title' = 'after-title'): string {
    const toc = this.generateTOC(content);
    if (toc.length === 0) return content;

    const tocMarkdown = this.generateTOCMarkdown(toc);
    const lines = content.split('\n');
    
    if (tocPosition === 'top') {
      return tocMarkdown + '\n\n' + content;
    } else {
      // 在第一个标题后插入
      const firstHeadingIndex = lines.findIndex(line => line.match(/^#\s+/));
      if (firstHeadingIndex !== -1) {
        lines.splice(firstHeadingIndex + 1, 0, '', tocMarkdown, '');
        return lines.join('\n');
      } else {
        return tocMarkdown + '\n\n' + content;
      }
    }
  }

  // 生成目录Markdown
  private generateTOCMarkdown(toc: TOCItem[]): string {
    const tocLines = ['## 目录', ''];
    
    toc.forEach(item => {
      const indent = '  '.repeat(item.level - 1);
      const link = `[${item.title}](#${item.anchor})`;
      tocLines.push(`${indent}- ${link}`);
    });
    
    return tocLines.join('\n');
  }

  // 分析文档结构
  analyzeDocumentStructure(content: string): DocumentStructure {
    const toc = this.generateTOC(content);
    const wordCount = this.countWords(content);
    const readingTime = this.calculateReadingTime(wordCount);
    const headingCount = this.countHeadings(toc);
    const suggestions = this.generateFormatSuggestions(content);

    return {
      toc,
      wordCount,
      readingTime,
      headingCount,
      suggestions
    };
  }

  // 计算字数
  private countWords(content: string): number {
    // 移除Markdown语法
    const plainText = content
      .replace(/```[\s\S]*?```/g, '') // 代码块
      .replace(/`[^`]+`/g, '') // 行内代码
      .replace(/!\[.*?\]\(.*?\)/g, '') // 图片
      .replace(/\[.*?\]\(.*?\)/g, '') // 链接
      .replace(/[#*_~`]/g, '') // Markdown符号
      .replace(/\n+/g, ' '); // 换行符

    // 中文字符计数
    const chineseChars = (plainText.match(/[\u4e00-\u9fff]/g) || []).length;
    // 英文单词计数
    const englishWords = plainText
      .replace(/[\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0).length;

    return chineseChars + englishWords;
  }

  // 计算阅读时间（分钟）
  private calculateReadingTime(wordCount: number): number {
    // 中文阅读速度约300字/分钟，英文约200词/分钟
    // 这里简化为平均250字/分钟
    return Math.ceil(wordCount / 250);
  }

  // 统计标题数量
  private countHeadings(toc: TOCItem[]): { [level: number]: number } {
    const count: { [level: number]: number } = {};
    toc.forEach(item => {
      count[item.level] = (count[item.level] || 0) + 1;
    });
    return count;
  }

  // 生成格式建议
  generateFormatSuggestions(content: string): FormatSuggestion[] {
    const suggestions: FormatSuggestion[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // 检查标题格式
      if (line.match(/^#{1,6}/)) {
        // 标题后应该有空格
        if (!line.match(/^#{1,6}\s+/)) {
          suggestions.push({
            type: 'heading',
            line: lineNumber,
            message: '标题符号后应该有空格',
            suggestion: line.replace(/^(#{1,6})/, '$1 '),
            severity: 'warning'
          });
        }

        // 标题不应该以标点符号结尾
        if (line.match(/[。！？：；，]$/)) {
          suggestions.push({
            type: 'heading',
            line: lineNumber,
            message: '标题不应该以标点符号结尾',
            suggestion: line.replace(/[。！？：；，]+$/, ''),
            severity: 'info'
          });
        }
      }

      // 检查列表格式
      if (line.match(/^\s*[-*+]\s/)) {
        // 列表项缩进应该一致
        const indent = line.match(/^(\s*)/)?.[1] || '';
        if (indent.length % 2 !== 0) {
          suggestions.push({
            type: 'list',
            line: lineNumber,
            message: '列表缩进应该使用2个空格的倍数',
            suggestion: line.replace(/^(\s*)/, ' '.repeat(Math.floor(indent.length / 2) * 2)),
            severity: 'info'
          });
        }
      }

      // 检查段落格式
      if (line.trim() && !line.match(/^[#\s*-+>|`]/)) {
        // 段落过长提醒
        if (line.length > 100) {
          suggestions.push({
            type: 'paragraph',
            line: lineNumber,
            message: '段落过长，建议分段或使用列表',
            suggestion: '考虑在适当位置分段',
            severity: 'info'
          });
        }

        // 中英文间应该有空格
        if (line.match(/[\u4e00-\u9fff][a-zA-Z]|[a-zA-Z][\u4e00-\u9fff]/)) {
          const fixed = line.replace(/([\u4e00-\u9fff])([a-zA-Z])/g, '$1 $2')
                           .replace(/([a-zA-Z])([\u4e00-\u9fff])/g, '$1 $2');
          if (fixed !== line) {
            suggestions.push({
              type: 'paragraph',
              line: lineNumber,
              message: '中英文之间应该有空格',
              suggestion: fixed,
              severity: 'warning'
            });
          }
        }

        // 中文数字间应该有空格
        if (line.match(/[\u4e00-\u9fff]\d|\d[\u4e00-\u9fff]/)) {
          const fixed = line.replace(/([\u4e00-\u9fff])(\d)/g, '$1 $2')
                           .replace(/(\d)([\u4e00-\u9fff])/g, '$1 $2');
          if (fixed !== line) {
            suggestions.push({
              type: 'paragraph',
              line: lineNumber,
              message: '中文与数字之间应该有空格',
              suggestion: fixed,
              severity: 'warning'
            });
          }
        }

        // 检查多余的空格
        if (line.match(/\s{2,}/)) {
          const fixed = line.replace(/\s{2,}/g, ' ');
          if (fixed !== line) {
            suggestions.push({
              type: 'paragraph',
              line: lineNumber,
              message: '移除多余的空格',
              suggestion: fixed,
              severity: 'warning'
            });
          }
        }

        // 检查行尾空格
        if (line.match(/\s+$/)) {
          suggestions.push({
            type: 'paragraph',
            line: lineNumber,
            message: '移除行尾空格',
            suggestion: line.replace(/\s+$/, ''),
            severity: 'warning'
          });
        }
      }

      // 检查空行
      if (index > 0 && line.trim() === '' && lines[index - 1].trim() === '') {
        suggestions.push({
          type: 'paragraph',
          line: lineNumber,
          message: '避免连续空行',
          suggestion: '删除多余空行',
          severity: 'info'
        });
      }
    });

    return suggestions;
  }

  // 自动修复格式问题
  autoFixFormatting(content: string): string {
    const suggestions = this.generateFormatSuggestions(content);
    let lines = content.split('\n');
    let fixedCount = 0;

    // 按行号倒序处理，避免行号偏移
    // 修复所有可以自动修复的问题，不仅仅是警告级别的
    const validSuggestions = suggestions
      .filter(s => {
        // 过滤掉不能自动修复的建议
        return s.suggestion &&
               s.suggestion !== '删除多余空行' &&
               s.suggestion !== '考虑在适当位置分段' &&
               !s.suggestion.includes('考虑') &&
               !s.suggestion.includes('建议');
      })
      .sort((a, b) => b.line - a.line);

    // 按行分组，避免同一行的多次修复冲突
    const lineGroups = new Map();
    validSuggestions.forEach(suggestion => {
      const line = suggestion.line;
      if (!lineGroups.has(line)) {
        lineGroups.set(line, []);
      }
      lineGroups.get(line).push(suggestion);
    });

    // 对每一行应用所有修复
    Array.from(lineGroups.entries())
      .sort(([a], [b]) => b - a) // 按行号倒序
      .forEach(([lineNumber, lineSuggestions]) => {
        const lineIndex = lineNumber - 1;
        if (lineIndex >= 0 && lineIndex < lines.length) {
          let currentLine = lines[lineIndex];
          const originalLine = currentLine;

          // 按优先级应用修复（标题格式优先）
          lineSuggestions
            .sort((a, b) => {
              if (a.type === 'heading' && b.type !== 'heading') return -1;
              if (a.type !== 'heading' && b.type === 'heading') return 1;
              return 0;
            })
            .forEach(suggestion => {
              // 重新计算修复，因为前面的修复可能已经改变了内容
              let newSuggestion = suggestion.suggestion;

              // 对于标题格式，重新计算
              if (suggestion.type === 'heading') {
                if (suggestion.message.includes('空格') && !currentLine.match(/^#{1,6}\s+/)) {
                  newSuggestion = currentLine.replace(/^(#{1,6})/, '$1 ');
                } else if (suggestion.message.includes('标点符号') && currentLine.match(/[。！？：；，]$/)) {
                  newSuggestion = currentLine.replace(/[。！？：；，]+$/, '');
                } else {
                  return; // 已经修复过了
                }
              }

              if (currentLine !== newSuggestion) {
                currentLine = newSuggestion;
              }
            });

          if (originalLine !== currentLine) {
            lines[lineIndex] = currentLine;
            fixedCount++;
            console.log(`修复第${lineNumber}行: ${lineSuggestions.map(s => s.message).join(', ')}`);
          }
        }
      });

    // 移除连续空行
    const result = [];
    let lastWasEmpty = false;
    let removedEmptyLines = 0;

    for (const line of lines) {
      if (line.trim() === '') {
        if (!lastWasEmpty) {
          result.push(line);
          lastWasEmpty = true;
        } else {
          removedEmptyLines++;
        }
      } else {
        result.push(line);
        lastWasEmpty = false;
      }
    }

    if (removedEmptyLines > 0) {
      fixedCount += removedEmptyLines;
      console.log(`移除了${removedEmptyLines}个多余空行`);
    }

    console.log(`自动修复完成，共修复${fixedCount}个问题`);
    return result.join('\n');
  }

  // 分析修复前后的差异
  analyzeFixDifferences(originalContent: string, fixedContent: string): {
    changedLines: number;
    addedLines: number;
    removedLines: number;
    changes: Array<{ line: number; type: 'modified' | 'added' | 'removed'; original?: string; fixed?: string }>
  } {
    const originalLines = originalContent.split('\n');
    const fixedLines = fixedContent.split('\n');
    const changes: Array<{ line: number; type: 'modified' | 'added' | 'removed'; original?: string; fixed?: string }> = [];

    let changedLines = 0;
    let addedLines = 0;
    let removedLines = 0;

    const maxLength = Math.max(originalLines.length, fixedLines.length);

    for (let i = 0; i < maxLength; i++) {
      const originalLine = originalLines[i];
      const fixedLine = fixedLines[i];

      if (originalLine !== fixedLine) {
        if (originalLine === undefined) {
          // 新增行
          addedLines++;
          changes.push({ line: i + 1, type: 'added', fixed: fixedLine });
        } else if (fixedLine === undefined) {
          // 删除行
          removedLines++;
          changes.push({ line: i + 1, type: 'removed', original: originalLine });
        } else {
          // 修改行
          changedLines++;
          changes.push({ line: i + 1, type: 'modified', original: originalLine, fixed: fixedLine });
        }
      }
    }

    return { changedLines, addedLines, removedLines, changes };
  }

  // AI智能自动修复
  async autoFixFormattingWithAI(content: string, customPrompt?: string): Promise<{ success: boolean; result: string; error?: string; isAIFixed?: boolean; analysis?: any }> {
    try {
      // 检查AI服务是否已初始化
      if (!aiService.isInitialized()) {
        console.log('AI服务未配置，使用本地修复');
        return {
          success: true,
          result: this.autoFixFormatting(content),
          isAIFixed: false
        };
      }

      console.log('使用AI进行智能自动修复...');
      const response = await aiService.intelligentAutoFix(content, customPrompt);

      if (response.success && response.result) {
        console.log('AI智能修复成功');

        // 分析修复结果
        const analysis = this.analyzeFixDifferences(content, response.result);
        console.log('修复分析:', analysis);

        return {
          success: true,
          result: response.result,
          isAIFixed: true,
          analysis
        };
      } else {
        console.log('AI修复失败，使用本地修复作为备选:', response.error);
        const localResult = this.autoFixFormatting(content);
        const analysis = this.analyzeFixDifferences(content, localResult);

        return {
          success: true,
          result: localResult,
          error: response.error,
          isAIFixed: false,
          analysis
        };
      }
    } catch (error) {
      console.error('AI智能修复出错:', error);
      const localResult = this.autoFixFormatting(content);
      const analysis = this.analyzeFixDifferences(content, localResult);

      return {
        success: true,
        result: localResult,
        error: error instanceof Error ? error.message : '未知错误',
        isAIFixed: false,
        analysis
      };
    }
  }

  // 使用AI优化文档格式
  async optimizeWithAI(content: string, formatType: 'academic' | 'business' | 'blog' | 'general' = 'general'): Promise<string> {
    try {
      // 检查AI服务是否已初始化
      if (!aiService.isInitialized()) {
        throw new Error('AI服务未配置，请先在AI助手中配置API密钥');
      }

      // 根据格式类型构建专门的提示词
      const formatPrompts = {
        academic: `请按照学术论文格式标准优化以下Markdown文档：
1. 标题层级清晰，使用标准的学术格式
2. 段落结构合理，逻辑清晰
3. 引用格式规范
4. 图表标注准确
5. 参考文献格式统一

文档内容：
${content}`,
        business: `请按照商务报告格式标准优化以下Markdown文档：
1. 执行摘要突出
2. 数据图表清晰
3. 结论建议明确
4. 格式专业规范
5. 重点内容突出

文档内容：
${content}`,
        blog: `请按照博客文章格式标准优化以下Markdown文档：
1. 标题吸引人，层级清晰
2. 段落简洁易读
3. 列表和要点突出
4. 适合网络阅读
5. 互动性强

文档内容：
${content}`,
        general: `请优化以下Markdown文档的格式和结构，使其更加清晰易读：

文档内容：
${content}`
      };

      // 使用精准格式优化方法
      const response = await aiService.formatDocumentPrecise(content, formatType, formatPrompts[formatType]);

      if (response.success && response.result) {
        return response.result;
      } else {
        throw new Error(response.error || 'AI优化失败');
      }
    } catch (error) {
      console.error('AI格式优化失败:', error);

      // 如果是配置问题，抛出错误让用户知道
      if (error instanceof Error && error.message.includes('未配置')) {
        throw error;
      }

      // 其他错误，使用本地自动修复作为备选
      console.log('使用本地自动修复作为备选方案');
      return this.autoFixFormatting(content);
    }
  }

  // 生成文档统计报告
  generateDocumentReport(content: string): string {
    const structure = this.analyzeDocumentStructure(content);
    
    const report = [
      '# 文档分析报告',
      '',
      '## 基本信息',
      `- 总字数：${structure.wordCount}`,
      `- 预计阅读时间：${structure.readingTime} 分钟`,
      `- 标题数量：${Object.values(structure.headingCount).reduce((a, b) => a + b, 0)}`,
      '',
      '## 标题分布',
    ];

    Object.entries(structure.headingCount).forEach(([level, count]) => {
      report.push(`- H${level}：${count} 个`);
    });

    if (structure.suggestions.length > 0) {
      report.push('', '## 格式建议');
      structure.suggestions.slice(0, 10).forEach((suggestion, index) => {
        report.push(`${index + 1}. 第${suggestion.line}行：${suggestion.message}`);
      });

      if (structure.suggestions.length > 10) {
        report.push(`... 还有 ${structure.suggestions.length - 10} 条建议`);
      }
    }

    return report.join('\n');
  }
}

export const intelligentFormatting = new IntelligentFormattingService();
