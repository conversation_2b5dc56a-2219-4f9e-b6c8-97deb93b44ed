// 文档转换器前端JavaScript

// 全局变量
let currentFileId = null;
let currentTaskId = null;
let supportedFormats = [];
let conversionMatrix = {};

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const conversionCard = document.getElementById('conversionCard');
const progressCard = document.getElementById('progressCard');
const resultCard = document.getElementById('resultCard');
const errorAlert = document.getElementById('errorAlert');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

// 初始化应用
async function initializeApp() {
    try {
        // 获取支持的格式
        const response = await fetch('/api/formats');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        supportedFormats = data.formats;
        conversionMatrix = data.conversion_matrix;

        console.log('支持的格式:', supportedFormats);

        // 隐藏可能存在的错误提示
        hideError();

    } catch (error) {
        console.error('初始化失败:', error);

        // 如果是网络错误，提供更友好的提示
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            showError('网络连接失败，请检查服务器是否正常运行');
        } else {
            showError('应用初始化失败，请刷新页面重试');
        }
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 文件输入变化
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // 目标格式选择变化
    document.getElementById('targetFormat').addEventListener('change', updateConvertButton);
}

// 处理拖拽悬停
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽放置
function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

// 处理文件选择
function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

// 处理文件
async function handleFile(file) {
    // 重置状态
    hideAllCards();
    hideError();
    
    // 验证文件
    if (!validateFile(file)) {
        return;
    }
    
    // 显示上传状态
    uploadArea.classList.add('upload-success');
    
    try {
        // 上传文件
        const fileData = await uploadFile(file);
        
        // 显示文件信息
        displayFileInfo(fileData);
        
        // 更新目标格式选项
        updateTargetFormats(fileData.format);
        
        // 显示转换卡片
        conversionCard.style.display = 'block';
        
    } catch (error) {
        console.error('文件处理失败:', error);
        showError(error.message || '文件上传失败');
        uploadArea.classList.remove('upload-success');
        uploadArea.classList.add('upload-error');
    }
}

// 验证文件
function validateFile(file) {
    const maxSize = 10 * 1024 * 1024 * 1024; // 10GB
    const allowedTypes = ['pdf', 'docx', 'xlsx', 'txt', 'md'];

    // 检查文件大小
    if (file.size > maxSize) {
        showError('文件过大，最大支持10GB');
        return false;
    }
    
    // 检查文件类型
    const extension = file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(extension)) {
        showError(`不支持的文件格式。支持的格式: ${allowedTypes.join(', ')}`);
        return false;
    }
    
    return true;
}

// 上传文件
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    // 显示上传进度提示（对于大文件很重要）
    if (file.size > 100 * 1024 * 1024) { // 大于100MB显示提示
        showUploadProgress();
    }

    const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        // 对于大文件，增加超时时间
        signal: AbortSignal.timeout(3600000) // 1小时超时
    });

    // 隐藏上传进度提示
    hideUploadProgress();

    const data = await response.json();

    if (!response.ok) {
        throw new Error(data.error || '上传失败');
    }

    currentFileId = data.file_id;
    return data;
}

// 显示文件信息
function displayFileInfo(fileData) {
    document.getElementById('fileName').textContent = fileData.filename;
    document.getElementById('fileSize').textContent = formatFileSize(fileData.size);
    
    const sourceFormatElement = document.getElementById('sourceFormat');
    sourceFormatElement.textContent = fileData.format.toUpperCase();
    sourceFormatElement.className = `format-badge ${fileData.format}`;
}

// 更新目标格式选项
function updateTargetFormats(sourceFormat) {
    const targetFormatSelect = document.getElementById('targetFormat');
    targetFormatSelect.innerHTML = '<option value="">请选择目标格式</option>';
    
    const availableFormats = conversionMatrix[sourceFormat] || [];
    
    availableFormats.forEach(format => {
        const option = document.createElement('option');
        option.value = format;
        option.textContent = format.toUpperCase();
        targetFormatSelect.appendChild(option);
    });
}

// 更新转换按钮状态
function updateConvertButton() {
    const targetFormat = document.getElementById('targetFormat').value;
    const convertBtn = document.getElementById('convertBtn');
    
    convertBtn.disabled = !targetFormat;
}

// 开始转换
async function startConversion() {
    const targetFormat = document.getElementById('targetFormat').value;
    const generateToc = document.getElementById('generateToc').checked;
    
    if (!currentFileId || !targetFormat) {
        showError('请选择文件和目标格式');
        return;
    }
    
    try {
        // 隐藏其他卡片，显示进度
        hideAllCards();
        progressCard.style.display = 'block';
        
        // 重置进度
        updateProgress(0, '开始转换...');
        
        // 发送转换请求
        const response = await fetch('/api/convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_id: currentFileId,
                target_format: targetFormat,
                options: {
                    toc: generateToc
                }
            })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || '转换失败');
        }
        
        currentTaskId = data.task_id;
        
        // 开始轮询状态
        pollConversionStatus();
        
    } catch (error) {
        console.error('转换失败:', error);
        showError(error.message || '转换失败');
        hideAllCards();
        conversionCard.style.display = 'block';
    }
}

// 轮询转换状态
async function pollConversionStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/status/${currentTaskId}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || '获取状态失败');
        }
        
        // 更新进度
        updateProgress(data.progress, data.message);
        
        if (data.status === 'completed') {
            // 转换完成
            showResult();
        } else if (data.status === 'failed') {
            // 转换失败
            throw new Error(data.message || '转换失败');
        } else {
            // 继续轮询
            setTimeout(pollConversionStatus, 1000);
        }
        
    } catch (error) {
        console.error('状态查询失败:', error);
        showError(error.message || '状态查询失败');
        hideAllCards();
        conversionCard.style.display = 'block';
    }
}

// 更新进度
function updateProgress(progress, message) {
    const progressBar = document.getElementById('progressBar');
    const progressMessage = document.getElementById('progressMessage');
    
    progressBar.style.width = `${progress}%`;
    progressBar.textContent = `${progress}%`;
    progressMessage.textContent = message;
}

// 显示转换结果
function showResult() {
    hideAllCards();
    resultCard.style.display = 'block';
    
    // 设置下载按钮
    const downloadBtn = document.getElementById('downloadBtn');
    downloadBtn.onclick = downloadFile;
}

// 下载文件
async function downloadFile() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/download/${currentTaskId}`);
        
        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || '下载失败');
        }
        
        // 创建下载链接
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `converted_file.${document.getElementById('targetFormat').value}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
    } catch (error) {
        console.error('下载失败:', error);
        showError(error.message || '下载失败');
    }
}

// 重置表单
async function resetForm() {
    // 清理文件
    if (currentTaskId) {
        try {
            await fetch('/api/cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: currentTaskId
                })
            });
        } catch (error) {
            console.error('清理失败:', error);
        }
    }
    
    // 重置变量
    currentFileId = null;
    currentTaskId = null;
    
    // 重置界面
    hideAllCards();
    hideError();
    uploadArea.classList.remove('upload-success', 'upload-error', 'dragover');
    fileInput.value = '';
    document.getElementById('targetFormat').value = '';
    document.getElementById('generateToc').checked = false;
}

// 隐藏所有卡片
function hideAllCards() {
    conversionCard.style.display = 'none';
    progressCard.style.display = 'none';
    resultCard.style.display = 'none';
}

// 显示错误
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    errorAlert.style.display = 'block';

    // 滚动到错误提示位置
    errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // 8秒后自动隐藏
    setTimeout(hideError, 8000);
}

// 隐藏错误
function hideError() {
    errorAlert.style.display = 'none';
}

// 显示上传进度
function showUploadProgress() {
    const uploadArea = document.getElementById('uploadArea');
    const originalContent = uploadArea.innerHTML;

    uploadArea.innerHTML = `
        <div class="upload-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">上传中...</span>
            </div>
            <h6>正在上传大文件...</h6>
            <p class="text-muted">请耐心等待，大文件上传可能需要较长时间</p>
        </div>
    `;

    // 保存原始内容以便恢复
    uploadArea.dataset.originalContent = originalContent;
}

// 隐藏上传进度
function hideUploadProgress() {
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea.dataset.originalContent) {
        uploadArea.innerHTML = uploadArea.dataset.originalContent;
        delete uploadArea.dataset.originalContent;
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
