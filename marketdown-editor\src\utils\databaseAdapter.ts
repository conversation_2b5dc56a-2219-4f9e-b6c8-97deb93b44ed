import { db<PERSON><PERSON><PERSON> as indexedDBManager, AIModel as IndexedDBAIModel } from './indexedDB';
import { mysqlManager, AIModel as MySQLAIModel } from './mysql';

// 统一的数据库接口
export interface DatabaseAdapter {
  // AI模型操作
  getAllAIModels(): Promise<AIModel[]>;
  getAIModelById(id: number): Promise<AIModel | null>;
  createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number>;
  updateAIModel(id: number, updates: Partial<AIModel>): Promise<void>;
  deleteAIModel(id: number): Promise<void>;
  getDefaultAIModel(): Promise<AIModel | null>;
  setDefaultAIModel(id: number): Promise<void>;

  // 设置操作
  getSetting(key: string): Promise<any>;
  setSetting(key: string, value: any): Promise<void>;

  // 初始化
  init(): Promise<void>;
}

// 统一的AI模型接口
export interface AIModel {
  id?: number;
  name: string;
  api_key: string;
  base_url?: string;
  model_name: string;
  model_type: 'text' | 'image' | 'multimodal';
  temperature: number;
  max_tokens: number;
  description?: string;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
}

// IndexedDB适配器
class IndexedDBAdapter implements DatabaseAdapter {
  async getAllAIModels(): Promise<AIModel[]> {
    return await indexedDBManager.getAllAIModels();
  }

  async getAIModelById(id: number): Promise<AIModel | null> {
    return await indexedDBManager.getAIModelById(id);
  }

  async createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    return await indexedDBManager.createAIModel(model);
  }

  async updateAIModel(id: number, updates: Partial<AIModel>): Promise<void> {
    return await indexedDBManager.updateAIModel(id, updates);
  }

  async deleteAIModel(id: number): Promise<void> {
    return await indexedDBManager.deleteAIModel(id);
  }

  async getDefaultAIModel(): Promise<AIModel | null> {
    return await indexedDBManager.getDefaultAIModel();
  }

  async setDefaultAIModel(id: number): Promise<void> {
    return await indexedDBManager.setDefaultAIModel(id);
  }

  async getSetting(key: string): Promise<any> {
    return await indexedDBManager.getSetting(key);
  }

  async setSetting(key: string, value: any): Promise<void> {
    return await indexedDBManager.setSetting(key, value);
  }

  async init(): Promise<void> {
    return await indexedDBManager.init();
  }
}

// MySQL适配器
class MySQLAdapter implements DatabaseAdapter {
  async getAllAIModels(): Promise<AIModel[]> {
    return await mysqlManager.getAllAIModels();
  }

  async getAIModelById(id: number): Promise<AIModel | null> {
    return await mysqlManager.getAIModelById(id);
  }

  async createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    return await mysqlManager.createAIModel(model);
  }

  async updateAIModel(id: number, updates: Partial<AIModel>): Promise<void> {
    return await mysqlManager.updateAIModel(id, updates);
  }

  async deleteAIModel(id: number): Promise<void> {
    return await mysqlManager.deleteAIModel(id);
  }

  async getDefaultAIModel(): Promise<AIModel | null> {
    return await mysqlManager.getDefaultAIModel();
  }

  async setDefaultAIModel(id: number): Promise<void> {
    return await mysqlManager.setDefaultAIModel(id);
  }

  async getSetting(key: string): Promise<any> {
    return await mysqlManager.getSetting(key);
  }

  async setSetting(key: string, value: any): Promise<void> {
    return await mysqlManager.setSetting(key, value);
  }

  async init(): Promise<void> {
    return await mysqlManager.init();
  }
}

// 数据库类型
export type DatabaseType = 'indexeddb' | 'mysql';

// 数据库管理器
class DatabaseManager {
  private currentAdapter: DatabaseAdapter;
  private currentType: DatabaseType;

  constructor() {
    // 默认使用IndexedDB
    this.currentType = this.getStoredDatabaseType();
    this.currentAdapter = this.createAdapter(this.currentType);
  }

  // 获取存储的数据库类型
  private getStoredDatabaseType(): DatabaseType {
    if (typeof window === 'undefined') return 'indexeddb';
    
    try {
      const stored = localStorage.getItem('martetdown-database-type');
      if (stored === 'mysql' || stored === 'indexeddb') {
        return stored as DatabaseType;
      }
    } catch (error) {
      console.error('Failed to load database type preference:', error);
    }
    
    return 'indexeddb';
  }

  // 创建适配器
  private createAdapter(type: DatabaseType): DatabaseAdapter {
    switch (type) {
      case 'mysql':
        return new MySQLAdapter();
      case 'indexeddb':
      default:
        return new IndexedDBAdapter();
    }
  }

  // 切换数据库类型
  async switchDatabase(type: DatabaseType): Promise<void> {
    if (this.currentType === type) {
      return;
    }

    this.currentType = type;
    this.currentAdapter = this.createAdapter(type);

    // 保存选择
    try {
      localStorage.setItem('martetdown-database-type', type);
    } catch (error) {
      console.error('Failed to save database type preference:', error);
    }

    // 初始化新数据库
    await this.currentAdapter.init();
  }

  // 获取当前数据库类型
  getCurrentType(): DatabaseType {
    return this.currentType;
  }

  // 获取当前适配器
  getAdapter(): DatabaseAdapter {
    return this.currentAdapter;
  }

  // 检查MySQL是否可用
  async isMySQLAvailable(): Promise<boolean> {
    try {
      const mysqlAdapter = new MySQLAdapter();
      await mysqlAdapter.init();
      return true;
    } catch (error) {
      console.error('MySQL不可用:', error);
      return false;
    }
  }

  // 代理方法 - AI模型操作
  async getAllAIModels(): Promise<AIModel[]> {
    return await this.currentAdapter.getAllAIModels();
  }

  async getAIModelById(id: number): Promise<AIModel | null> {
    return await this.currentAdapter.getAIModelById(id);
  }

  async createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    return await this.currentAdapter.createAIModel(model);
  }

  async updateAIModel(id: number, updates: Partial<AIModel>): Promise<void> {
    return await this.currentAdapter.updateAIModel(id, updates);
  }

  async deleteAIModel(id: number): Promise<void> {
    return await this.currentAdapter.deleteAIModel(id);
  }

  async getDefaultAIModel(): Promise<AIModel | null> {
    return await this.currentAdapter.getDefaultAIModel();
  }

  async setDefaultAIModel(id: number): Promise<void> {
    return await this.currentAdapter.setDefaultAIModel(id);
  }

  // 代理方法 - 设置操作
  async getSetting(key: string): Promise<any> {
    return await this.currentAdapter.getSetting(key);
  }

  async setSetting(key: string, value: any): Promise<void> {
    return await this.currentAdapter.setSetting(key, value);
  }

  // 初始化
  async init(): Promise<void> {
    return await this.currentAdapter.init();
  }
}

// 创建单例实例
export const databaseManager = new DatabaseManager();

// 便捷函数
export const switchToMySQL = async () => {
  await databaseManager.switchDatabase('mysql');
};

export const switchToIndexedDB = async () => {
  await databaseManager.switchDatabase('indexeddb');
};

export const getCurrentDatabaseType = () => {
  return databaseManager.getCurrentType();
};

export const isMySQLAvailable = async () => {
  return await databaseManager.isMySQLAvailable();
};
