<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档转换器 - 在线文档格式转换</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-file-earmark-arrow-up-fill me-2"></i>
                    文档转换器
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        支持 PDF、Word、Excel、TXT、Markdown 互转 (最大10GB)
                    </span>
                </div>
            </div>
        </nav>

        <div class="container">
            <!-- 主要内容区域 -->
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <!-- 文件上传区域 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-cloud-upload me-2"></i>
                                选择文件
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-content">
                                    <i class="bi bi-cloud-upload upload-icon"></i>
                                    <h6>拖拽文件到此处或点击选择</h6>
                                    <p class="text-muted">支持 PDF、DOCX、XLSX、TXT、MD 格式，最大 10GB</p>
                                    <input type="file" id="fileInput" class="d-none" accept=".pdf,.docx,.xlsx,.txt,.md">
                                    <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                                        选择文件
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件信息和转换选项 -->
                    <div class="card mb-4" id="conversionCard" style="display: none;">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gear me-2"></i>
                                转换设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 文件信息 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">当前文件</label>
                                    <div class="file-info" id="fileInfo">
                                        <i class="bi bi-file-earmark me-2"></i>
                                        <span id="fileName">-</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">格式</label>
                                    <div class="format-badge" id="sourceFormat">-</div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">大小</label>
                                    <div id="fileSize">-</div>
                                </div>
                            </div>

                            <!-- 目标格式选择 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="targetFormat" class="form-label">转换为</label>
                                    <select class="form-select" id="targetFormat">
                                        <option value="">请选择目标格式</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">转换选项</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="generateToc">
                                        <label class="form-check-label" for="generateToc">
                                            生成目录 (适用于PDF/Word)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 转换按钮 -->
                            <div class="d-grid">
                                <button type="button" class="btn btn-success btn-lg" id="convertBtn" onclick="startConversion()">
                                    <i class="bi bi-arrow-repeat me-2"></i>
                                    开始转换
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 转换进度 -->
                    <div class="card mb-4" id="progressCard" style="display: none;">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-hourglass-split me-2"></i>
                                转换进度
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%">
                                    0%
                                </div>
                            </div>
                            <div class="text-center">
                                <div id="progressMessage">准备转换...</div>
                                <div class="spinner-border spinner-border-sm mt-2" role="status">
                                    <span class="visually-hidden">转换中...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 转换结果 -->
                    <div class="card mb-4" id="resultCard" style="display: none;">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-check-circle me-2"></i>
                                转换完成
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="result-icon mb-3">
                                <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
                            </div>
                            <h6>文件转换成功！</h6>
                            <p class="text-muted mb-3">您的文件已经转换完成，可以下载了。</p>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <button type="button" class="btn btn-primary btn-lg" id="downloadBtn">
                                    <i class="bi bi-download me-2"></i>
                                    下载文件
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    重新转换
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div class="alert alert-danger" id="errorAlert" style="display: none;">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                </div>
            </div>

            <!-- 支持格式说明 -->
            <div class="row mt-5">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                支持的转换格式
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2 mb-3">
                                    <div class="format-item">
                                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 2rem;"></i>
                                        <div class="mt-2">PDF</div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <div class="format-item">
                                        <i class="bi bi-file-earmark-word text-primary" style="font-size: 2rem;"></i>
                                        <div class="mt-2">Word</div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <div class="format-item">
                                        <i class="bi bi-file-earmark-excel text-success" style="font-size: 2rem;"></i>
                                        <div class="mt-2">Excel</div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <div class="format-item">
                                        <i class="bi bi-file-earmark-text text-secondary" style="font-size: 2rem;"></i>
                                        <div class="mt-2">TXT</div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <div class="format-item">
                                        <i class="bi bi-markdown text-info" style="font-size: 2rem;"></i>
                                        <div class="mt-2">Markdown</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 大文件处理提示 -->
            <div class="row mt-4">
                <div class="col-lg-8 mx-auto">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>大文件处理提示：</strong>
                        <ul class="mb-0 mt-2">
                            <li>支持最大10GB文件上传和转换</li>
                            <li>大文件（>100MB）上传和转换需要更长时间，请耐心等待</li>
                            <li>建议在网络稳定的环境下处理大文件</li>
                            <li>转换过程中请不要关闭浏览器窗口</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                © 2024 文档转换器 - 支持多种文档格式互转
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
