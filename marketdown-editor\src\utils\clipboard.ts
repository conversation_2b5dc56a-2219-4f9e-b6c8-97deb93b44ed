'use client';

// 复制工具类
export class ClipboardUtils {
  
  // 将Markdown表格转换为HTML表格
  static markdownTableToHTML(markdownContent: string, theme: string = 'modern'): string {
    const lines = markdownContent.split('\n');
    let htmlContent = '';
    let inTable = false;
    let tableRows: string[][] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 检测表格开始
      if (line.includes('|') && !inTable) {
        // 检查下一行是否是分隔符
        const nextLine = lines[i + 1]?.trim();
        if (nextLine && nextLine.includes('|') && nextLine.includes('-')) {
          inTable = true;
          // 解析表头
          const headerCells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
          tableRows.push(headerCells);
          i++; // 跳过分隔符行
          continue;
        }
      }
      
      // 在表格中
      if (inTable && line.includes('|')) {
        const rowCells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
        if (rowCells.length > 0) {
          tableRows.push(rowCells);
        }
      } 
      // 表格结束
      else if (inTable && !line.includes('|')) {
        // 生成HTML表格
        htmlContent += this.generateStyledTable(tableRows, theme);
        tableRows = [];
        inTable = false;
        
        // 处理非表格内容
        if (line) {
          htmlContent += this.processNonTableContent(line);
        }
      }
      // 非表格内容
      else if (!inTable && line) {
        htmlContent += this.processNonTableContent(line);
      }
    }
    
    // 处理最后的表格
    if (inTable && tableRows.length > 0) {
      htmlContent += this.generateStyledTable(tableRows, theme);
    }
    
    return htmlContent;
  }
  
  // 生成带样式的HTML表格
  static generateStyledTable(rows: string[][], theme: string): string {
    if (rows.length === 0) return '';
    
    const themeStyles = this.getThemeStyles(theme);
    
    // 确保所有行都有相同的列数
    const maxCols = Math.max(...rows.map(row => row.length));
    const normalizedRows = rows.map(row => {
      const newRow = [...row];
      while (newRow.length < maxCols) {
        newRow.push('');
      }
      return newRow;
    });
    
    let html = `<table style="${themeStyles.tableStyle}">`;
    
    // 表头
    if (normalizedRows.length > 0) {
      html += '<thead><tr>';
      normalizedRows[0].forEach(cell => {
        html += `<th style="${themeStyles.headerStyle}">${this.formatCellContent(cell)}</th>`;
      });
      html += '</tr></thead>';
    }
    
    // 表体
    if (normalizedRows.length > 1) {
      html += '<tbody>';
      normalizedRows.slice(1).forEach((row, index) => {
        const isEven = index % 2 === 0;
        html += '<tr>';
        row.forEach(cell => {
          const cellStyle = isEven ? themeStyles.evenCellStyle : themeStyles.oddCellStyle;
          html += `<td style="${cellStyle}">${this.formatCellContent(cell)}</td>`;
        });
        html += '</tr>';
      });
      html += '</tbody>';
    }
    
    html += '</table><br>';
    return html;
  }
  
  // 获取主题样式
  static getThemeStyles(theme: string) {
    const themes = {
      professional: {
        headerBg: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',
        headerColor: '#ffffff',
        evenRowBg: '#f7fafc',
        oddRowBg: '#ffffff',
        textColor: '#2d3748',
        borderColor: '#cbd5e0',
      },
      modern: {
        headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        headerColor: '#ffffff',
        evenRowBg: '#f8fafc',
        oddRowBg: '#ffffff',
        textColor: '#2d3748',
        borderColor: '#e2e8f0',
      },
      minimal: {
        headerBg: 'linear-gradient(135deg, #a0aec0 0%, #718096 100%)',
        headerColor: '#ffffff',
        evenRowBg: '#f9fafb',
        oddRowBg: '#ffffff',
        textColor: '#374151',
        borderColor: '#e2e8f0',
      },
      colorful: {
        headerBg: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
        headerColor: '#ffffff',
        evenRowBg: '#fef5e7',
        oddRowBg: '#ffffff',
        textColor: '#2d3748',
        borderColor: '#fed7d7',
      },
    };
    
    const selectedTheme = themes[theme as keyof typeof themes] || themes.modern;
    
    return {
      tableStyle: `
        border-collapse: collapse;
        width: 100%;
        margin: 16px 0;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        font-size: 14px;
        border: 2px solid ${selectedTheme.borderColor};
      `,
      headerStyle: `
        background: ${selectedTheme.headerBg};
        color: ${selectedTheme.headerColor};
        padding: 12px 16px;
        text-align: center;
        font-weight: bold;
        border: 1px solid ${selectedTheme.borderColor};
        font-size: 15px;
      `,
      evenCellStyle: `
        background-color: ${selectedTheme.evenRowBg};
        color: ${selectedTheme.textColor};
        padding: 10px 16px;
        border: 1px solid ${selectedTheme.borderColor};
        text-align: left;
        vertical-align: top;
      `,
      oddCellStyle: `
        background-color: ${selectedTheme.oddRowBg};
        color: ${selectedTheme.textColor};
        padding: 10px 16px;
        border: 1px solid ${selectedTheme.borderColor};
        text-align: left;
        vertical-align: top;
      `,
    };
  }
  
  // 处理非表格内容
  static processNonTableContent(line: string): string {
    // 标题
    if (line.startsWith('# ')) {
      return `<h1 style="color: #2d3748; font-size: 24px; font-weight: bold; margin: 20px 0 10px 0;">${line.substring(2)}</h1>`;
    } else if (line.startsWith('## ')) {
      return `<h2 style="color: #2d3748; font-size: 20px; font-weight: bold; margin: 16px 0 8px 0;">${line.substring(3)}</h2>`;
    } else if (line.startsWith('### ')) {
      return `<h3 style="color: #2d3748; font-size: 18px; font-weight: bold; margin: 14px 0 6px 0;">${line.substring(4)}</h3>`;
    }
    
    // 普通段落
    return `<p style="color: #2d3748; line-height: 1.6; margin: 8px 0;">${this.formatCellContent(line)}</p>`;
  }
  
  // 格式化单元格内容
  static formatCellContent(content: string): string {
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code style="background-color: #f1f5f9; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>');
  }
  
  // 复制到剪贴板
  static async copyToClipboard(markdownContent: string, theme: string = 'modern'): Promise<boolean> {
    try {
      const htmlContent = this.markdownTableToHTML(markdownContent, theme);
      
      // 创建ClipboardItem
      const clipboardItem = new ClipboardItem({
        'text/html': new Blob([htmlContent], { type: 'text/html' }),
        'text/plain': new Blob([markdownContent], { type: 'text/plain' })
      });
      
      await navigator.clipboard.write([clipboardItem]);
      return true;
    } catch (error) {
      console.error('复制失败:', error);
      
      // 降级方案：复制纯文本
      try {
        await navigator.clipboard.writeText(markdownContent);
        return true;
      } catch (fallbackError) {
        console.error('降级复制也失败:', fallbackError);
        return false;
      }
    }
  }
  
  // 检查剪贴板API支持
  static isClipboardSupported(): boolean {
    return typeof navigator !== 'undefined' && 
           'clipboard' in navigator && 
           'write' in navigator.clipboard;
  }
  
  // 复制选中的表格
  static async copySelectedTable(tableElement: HTMLTableElement, theme: string = 'modern'): Promise<boolean> {
    try {
      const themeStyles = this.getThemeStyles(theme);
      
      // 克隆表格并应用样式
      const clonedTable = tableElement.cloneNode(true) as HTMLTableElement;
      clonedTable.style.cssText = themeStyles.tableStyle;
      
      // 应用表头样式
      const headers = clonedTable.querySelectorAll('th');
      headers.forEach(th => {
        th.style.cssText = themeStyles.headerStyle;
      });
      
      // 应用单元格样式
      const rows = clonedTable.querySelectorAll('tbody tr');
      rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        const isEven = index % 2 === 0;
        const cellStyle = isEven ? themeStyles.evenCellStyle : themeStyles.oddCellStyle;
        
        cells.forEach(cell => {
          (cell as HTMLElement).style.cssText = cellStyle;
        });
      });
      
      const htmlContent = clonedTable.outerHTML;
      
      const clipboardItem = new ClipboardItem({
        'text/html': new Blob([htmlContent], { type: 'text/html' })
      });
      
      await navigator.clipboard.write([clipboardItem]);
      return true;
    } catch (error) {
      console.error('复制表格失败:', error);
      return false;
    }
  }
}
