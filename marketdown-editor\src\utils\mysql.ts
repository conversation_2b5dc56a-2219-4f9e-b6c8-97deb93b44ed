import mysql from 'mysql2/promise';

// MySQL数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  port: parseInt(process.env.DB_PORT || '13306'),
  user: process.env.DB_USER || 'Martetdown',
  password: process.env.DB_PASSWORD || 'qwer4321',
  database: process.env.DB_NAME || 'martetdown',
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
};

// AI模型接口
export interface AIModel {
  id?: number;
  name: string;
  api_key: string;
  base_url?: string;
  model_name: string;
  model_type: 'text' | 'image' | 'multimodal';
  temperature: number;
  max_tokens: number;
  description?: string;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
}

// 文档接口
export interface Document {
  id?: number;
  title: string;
  content: string;
  created_at?: string;
  updated_at?: string;
}

// 设置接口
export interface Setting {
  key: string;
  value: any;
  updated_at?: string;
}

// MySQL数据库管理器
class MySQLManager {
  private connection: mysql.Connection | null = null;

  // 获取数据库连接
  private async getConnection(): Promise<mysql.Connection> {
    if (!this.connection) {
      try {
        this.connection = await mysql.createConnection(dbConfig);
        console.log('MySQL数据库连接成功');
      } catch (error) {
        console.error('MySQL数据库连接失败:', error);
        throw new Error('数据库连接失败');
      }
    }
    return this.connection;
  }

  // 初始化数据库表
  async init(): Promise<void> {
    const connection = await this.getConnection();
    
    try {
      // 创建AI模型表
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS ai_models (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          api_key TEXT NOT NULL,
          base_url VARCHAR(500),
          model_name VARCHAR(255) NOT NULL,
          model_type ENUM('text', 'image', 'multimodal') DEFAULT 'text',
          temperature DECIMAL(3,2) DEFAULT 0.7,
          max_tokens INT DEFAULT 2000,
          description TEXT,
          is_default BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_is_default (is_default),
          INDEX idx_model_type (model_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建文档表
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS documents (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(500) NOT NULL,
          content LONGTEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_title (title),
          INDEX idx_updated_at (updated_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建设置表
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS settings (
          \`key\` VARCHAR(255) PRIMARY KEY,
          value JSON,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建模板表
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS templates (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          category VARCHAR(100),
          content LONGTEXT,
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_category (category)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      console.log('数据库表初始化完成');
    } catch (error) {
      console.error('数据库表初始化失败:', error);
      throw error;
    }
  }

  // AI模型操作
  async getAllAIModels(): Promise<AIModel[]> {
    const connection = await this.getConnection();
    const [rows] = await connection.execute('SELECT * FROM ai_models ORDER BY created_at DESC');
    return rows as AIModel[];
  }

  async getAIModelById(id: number): Promise<AIModel | null> {
    const connection = await this.getConnection();
    const [rows] = await connection.execute('SELECT * FROM ai_models WHERE id = ?', [id]);
    const models = rows as AIModel[];
    return models.length > 0 ? models[0] : null;
  }

  async createAIModel(model: Omit<AIModel, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const connection = await this.getConnection();
    
    // 如果设置为默认，先取消其他默认设置
    if (model.is_default) {
      await connection.execute('UPDATE ai_models SET is_default = FALSE');
    }

    const [result] = await connection.execute(`
      INSERT INTO ai_models (name, api_key, base_url, model_name, model_type, temperature, max_tokens, description, is_default)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      model.name,
      model.api_key,
      model.base_url || null,
      model.model_name,
      model.model_type,
      model.temperature,
      model.max_tokens,
      model.description || null,
      model.is_default
    ]);

    return (result as any).insertId;
  }

  async updateAIModel(id: number, updates: Partial<AIModel>): Promise<void> {
    const connection = await this.getConnection();
    
    // 如果设置为默认，先取消其他默认设置
    if (updates.is_default) {
      await connection.execute('UPDATE ai_models SET is_default = FALSE');
    }

    const fields = Object.keys(updates).filter(key => key !== 'id');
    const values = fields.map(key => (updates as any)[key]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    if (fields.length > 0) {
      await connection.execute(`UPDATE ai_models SET ${setClause} WHERE id = ?`, [...values, id]);
    }
  }

  async deleteAIModel(id: number): Promise<void> {
    const connection = await this.getConnection();
    await connection.execute('DELETE FROM ai_models WHERE id = ?', [id]);
  }

  async getDefaultAIModel(): Promise<AIModel | null> {
    const connection = await this.getConnection();
    const [rows] = await connection.execute('SELECT * FROM ai_models WHERE is_default = TRUE LIMIT 1');
    const models = rows as AIModel[];
    return models.length > 0 ? models[0] : null;
  }

  async setDefaultAIModel(id: number): Promise<void> {
    const connection = await this.getConnection();
    await connection.execute('UPDATE ai_models SET is_default = FALSE');
    await connection.execute('UPDATE ai_models SET is_default = TRUE WHERE id = ?', [id]);
  }

  // 文档操作
  async getAllDocuments(): Promise<Document[]> {
    const connection = await this.getConnection();
    const [rows] = await connection.execute('SELECT * FROM documents ORDER BY updated_at DESC');
    return rows as Document[];
  }

  async getDocumentById(id: number): Promise<Document | null> {
    const connection = await this.getConnection();
    const [rows] = await connection.execute('SELECT * FROM documents WHERE id = ?', [id]);
    const documents = rows as Document[];
    return documents.length > 0 ? documents[0] : null;
  }

  async createDocument(document: Omit<Document, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const connection = await this.getConnection();
    const [result] = await connection.execute(`
      INSERT INTO documents (title, content) VALUES (?, ?)
    `, [document.title, document.content]);
    return (result as any).insertId;
  }

  async updateDocument(id: number, updates: Partial<Document>): Promise<void> {
    const connection = await this.getConnection();
    const fields = Object.keys(updates).filter(key => key !== 'id');
    const values = fields.map(key => (updates as any)[key]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    if (fields.length > 0) {
      await connection.execute(`UPDATE documents SET ${setClause} WHERE id = ?`, [...values, id]);
    }
  }

  async deleteDocument(id: number): Promise<void> {
    const connection = await this.getConnection();
    await connection.execute('DELETE FROM documents WHERE id = ?', [id]);
  }

  // 设置操作
  async getSetting(key: string): Promise<any> {
    const connection = await this.getConnection();
    const [rows] = await connection.execute('SELECT value FROM settings WHERE `key` = ?', [key]);
    const settings = rows as any[];
    return settings.length > 0 ? settings[0].value : null;
  }

  async setSetting(key: string, value: any): Promise<void> {
    const connection = await this.getConnection();
    await connection.execute(`
      INSERT INTO settings (\`key\`, value) VALUES (?, ?)
      ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = CURRENT_TIMESTAMP
    `, [key, JSON.stringify(value)]);
  }

  // 关闭连接
  async close(): Promise<void> {
    if (this.connection) {
      await this.connection.end();
      this.connection = null;
      console.log('MySQL数据库连接已关闭');
    }
  }
}

// 创建单例实例
export const mysqlManager = new MySQLManager();

// 初始化默认AI模型
export const initializeDefaultModels = async () => {
  try {
    await mysqlManager.init();
    
    const existingModels = await mysqlManager.getAllAIModels();
    if (existingModels.length === 0) {
      // 添加默认模型
      const defaultModels = [
        {
          name: 'OpenAI GPT-3.5 Turbo',
          api_key: '',
          base_url: 'https://api.openai.com/v1',
          model_name: 'gpt-3.5-turbo',
          model_type: 'text' as const,
          temperature: 0.7,
          max_tokens: 2000,
          description: 'OpenAI官方GPT-3.5 Turbo模型，性价比高',
          is_default: true,
        },
        {
          name: 'OpenAI GPT-4',
          api_key: '',
          base_url: 'https://api.openai.com/v1',
          model_name: 'gpt-4',
          model_type: 'text' as const,
          temperature: 0.7,
          max_tokens: 2000,
          description: 'OpenAI最新GPT-4模型，能力更强',
          is_default: false,
        },
      ];

      for (const model of defaultModels) {
        await mysqlManager.createAIModel(model);
      }
      
      console.log('默认AI模型初始化完成');
    }
  } catch (error) {
    console.error('初始化默认模型失败:', error);
  }
};
