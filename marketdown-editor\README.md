# Martetdown Editor

一个功能强大的现代化Markdown编辑器，集成了富文本编辑、AI助手、多格式导出等完整功能。

## ✨ 主要特性

### 📝 编辑功能
- **双模式编辑**: Markdown文本模式 + 富文本可视化模式
- **实时预览**: 支持实时Markdown渲染预览
- **语法高亮**: 完整的Markdown语法高亮支持
- **智能补全**: 自动补全Markdown语法
- **查找替换**: 强大的文本搜索和替换功能

### 🎨 富文本编辑
- **UEditor集成**: 简化版UEditor富文本编辑器
- **可视化工具栏**: 直观的格式化按钮
- **智能降级**: 失败时自动切换到备用编辑器
- **格式转换**: HTML ↔ Markdown 无缝转换

### 🤖 AI助手
- **智能优化**: AI驱动的内容优化和格式化
- **精确格式化**: 专业的文档格式标准化
- **内容建议**: 智能的写作建议和改进
- **多步骤处理**: 可视化的AI处理流程

### 📊 导出功能
- **多格式支持**: PDF、Word、HTML、纯文本
- **表格导出**: 专业的表格格式化和导出
- **主题定制**: 多种导出主题和样式
- **批量处理**: 支持批量文档导出

### 💾 数据管理
- **本地存储**: IndexedDB本地数据库
- **自动保存**: 实时自动保存编辑内容
- **版本历史**: 完整的文档版本管理
- **文件管理**: 直观的文件组织和管理

### 🎯 用户体验
- **响应式设计**: 完美适配各种设备
- **暗色主题**: 支持明暗主题切换
- **快捷键**: 完整的键盘快捷键支持
- **国际化**: 多语言界面支持

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 开始使用

### 构建生产版本
```bash
npm run build
npm start
```

## 📁 项目结构

```
src/
├── app/                    # Next.js 应用路由
├── components/             # React 组件
│   ├── AIAssistant/       # AI助手组件
│   ├── Editor/            # 编辑器组件
│   ├── FileManager/       # 文件管理
│   ├── Preview/           # 预览组件
│   ├── Toolbar/           # 工具栏
│   └── ui/                # UI组件库
├── hooks/                 # React Hooks
├── stores/                # 状态管理
├── types/                 # TypeScript类型
└── utils/                 # 工具函数
```

## 🛠️ 技术栈

- **前端框架**: Next.js 15 + React 19
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **数据库**: IndexedDB + Better-SQLite3
- **富文本**: UEditor简化版
- **Markdown**: react-markdown + remark/rehype
- **导出**: jsPDF + docx + html2canvas
- **AI集成**: OpenAI API
- **类型检查**: TypeScript

## 📖 使用指南

### 基础编辑
1. 选择编辑模式（Markdown/富文本）
2. 在编辑器中输入内容
3. 实时查看预览效果
4. 使用工具栏快速格式化

### AI助手
1. 点击"AI助手"按钮
2. 选择优化类型（内容优化/格式化）
3. 等待AI处理完成
4. 查看并应用建议

### 导出文档
1. 点击"导出"按钮
2. 选择导出格式（PDF/Word/HTML）
3. 配置导出选项
4. 下载生成的文件

### 文件管理
1. 使用文件管理器组织文档
2. 支持创建、删除、重命名
3. 自动保存和版本历史
4. 快速搜索和过滤

## 🎨 主题定制

项目支持完整的主题定制：

- **明暗主题**: 自动或手动切换
- **编辑器主题**: 多种代码高亮主题
- **导出样式**: 自定义PDF和Word样式
- **界面布局**: 灵活的布局配置

## 🔧 配置说明

### 环境变量
```env
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
```

### 功能开关
- UEditor富文本编辑器（可选）
- AI助手功能（需要API密钥）
- 本地数据库存储
- 自动保存功能

## 📝 更新日志

### v1.0.0 (2024-01-26)
- ✅ 完整的Markdown编辑器
- ✅ UEditor富文本集成
- ✅ AI助手功能
- ✅ 多格式导出
- ✅ 本地数据库
- ✅ 响应式设计

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [UEditor](https://ueditor.baidu.com/) - 富文本编辑器
- [react-markdown](https://github.com/remarkjs/react-markdown) - Markdown渲染
- [OpenAI](https://openai.com/) - AI能力支持

---

**Martetdown Editor** - 让Markdown编辑更加智能和高效！ 🚀
