'use client';

import React, { useEffect } from 'react';
import { useEditorStore } from '@/stores/useEditorStore';
import { useResponsive } from '@/hooks/useResponsive';
import { MarkdownEditor } from './MarkdownEditor';
import { MarkdownPreview } from '@/components/Preview';
import { MarkdownToolbar } from '@/components/Toolbar';
import { FileManager } from '@/components/FileManager';

export const EditorLayout: React.FC = () => {
  const { isMobile, isTablet } = useResponsive();
  const {
    isSplitView,
    isPreviewMode,
    isFullscreen,
    setSplitView,
    setPreviewMode,
    setFullscreen,
    currentDocument,
    createNewDocument,
  } = useEditorStore();

  // 初始化时创建一个默认文档
  useEffect(() => {
    if (!currentDocument) {
      createNewDocument('Welcome to Martetdown');
      // 设置默认内容
      setTimeout(() => {
        const welcomeContent = `# Welcome to Martetdown

This is a powerful Markdown editor with real-time preview.

## Features

- **Real-time preview** - See your markdown rendered as you type
- **Split view** - Edit and preview side by side
- **File management** - Save, load, and organize your documents
- **Syntax highlighting** - Beautiful code highlighting
- **Export options** - Export to PDF, Word, and HTML

## Getting Started

1. Start typing in the editor on the left
2. See the preview update in real-time on the right
3. Use the toolbar for quick formatting
4. Save your work using the file manager

### Markdown Syntax Examples

**Bold text** and *italic text*

\`\`\`javascript
// Code blocks with syntax highlighting
function hello() {
  console.log("Hello, Martetdown!");
}
\`\`\`

> Blockquotes for important notes

- Bullet points
- Another point
  - Nested points

| Tables | Are | Supported |
|--------|-----|-----------|
| Cell 1 | Cell 2 | Cell 3 |

[Links](https://example.com) and ![Images](https://via.placeholder.com/150)

---

Happy writing! 🚀`;
        
        useEditorStore.getState().updateCurrentDocument({ content: welcomeContent });
      }, 100);
    }
  }, [currentDocument, createNewDocument]);

  const toggleView = () => {
    if (isPreviewMode) {
      setPreviewMode(false);
      setSplitView(true);
    } else if (isSplitView) {
      setSplitView(false);
      setPreviewMode(true);
    } else {
      setPreviewMode(false);
      setSplitView(true);
    }
  };

  const toggleFullscreen = () => {
    setFullscreen(!isFullscreen);
  };

  // 在移动设备上强制使用单一视图
  const effectiveIsSplitView = isMobile ? false : isSplitView;
  const effectiveIsPreviewMode = isMobile ? isPreviewMode : isPreviewMode;

  return (
    <div className={`flex h-full ${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' : ''} ${isMobile ? 'flex-col' : ''}`}>
      {/* 文件管理器侧边栏 */}
      {!isFullscreen && (
        <div className={`${isMobile ? 'h-48 border-b' : 'w-64 border-r'} flex-shrink-0 border-gray-200 dark:border-gray-700`}>
          <FileManager />
        </div>
      )}

      {/* 主编辑区域 */}
      <div className="flex-1 flex flex-col">
        {/* 工具栏 */}
        <div className="flex items-center justify-between bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <MarkdownToolbar className="flex-1" />
          
          {/* 视图控制按钮 */}
          <div className="flex items-center gap-2 px-4">
            <button
              onClick={toggleView}
              className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              title="Toggle view mode"
            >
              {isPreviewMode ? '📝 Edit' : isSplitView ? '👁️ Preview' : '📝 Edit'}
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              title="Toggle fullscreen"
            >
              {isFullscreen ? '🗗' : '🗖'}
            </button>
          </div>
        </div>

        {/* 编辑器和预览区域 */}
        <div className={`flex-1 flex ${isMobile ? 'flex-col' : ''}`}>
          {/* 编辑器 */}
          {!effectiveIsPreviewMode && (
            <div className={`${effectiveIsSplitView && !isMobile ? 'w-1/2' : 'w-full'} ${!isMobile ? 'border-r border-gray-200 dark:border-gray-700' : ''} ${isMobile && effectiveIsSplitView ? 'h-1/2 border-b border-gray-200 dark:border-gray-700' : ''}`}>
              <MarkdownEditor />
            </div>
          )}

          {/* 预览 */}
          {(effectiveIsPreviewMode || effectiveIsSplitView) && (
            <div className={`${effectiveIsSplitView && !isMobile ? 'w-1/2' : 'w-full'} ${isMobile && effectiveIsSplitView ? 'h-1/2' : ''} bg-white dark:bg-gray-900`}>
              <MarkdownPreview />
            </div>
          )}
        </div>

        {/* 状态栏 */}
        <div className="h-6 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-4">
            {currentDocument && (
              <>
                <span>Document: {currentDocument.title}</span>
                <span>Characters: {currentDocument.content.length}</span>
                <span>Words: {currentDocument.content.trim() ? currentDocument.content.trim().split(/\s+/).length : 0}</span>
              </>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <span>Mode: {isPreviewMode ? 'Preview' : isSplitView ? 'Split' : 'Edit'}</span>
            {isFullscreen && <span>Fullscreen</span>}
          </div>
        </div>
      </div>
    </div>
  );
};
