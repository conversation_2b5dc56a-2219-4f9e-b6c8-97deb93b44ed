'use client';

import React, { useState, useRef, useEffect } from 'react';

interface AdvancedRichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  className?: string;
}

interface FontStyle {
  fontFamily: string;
  fontSize: string;
  fontWeight: string;
  fontStyle: string;
  color: string;
  backgroundColor: string;
  textDecoration: string;
  textAlign: string;
}

export const AdvancedRichTextEditor: React.FC<AdvancedRichTextEditorProps> = ({
  content,
  onChange,
  className = ''
}) => {
  const [isRichMode, setIsRichMode] = useState(false);
  const [showFontPanel, setShowFontPanel] = useState(false);
  const [showColorPanel, setShowColorPanel] = useState(false);
  const [showTablePanel, setShowTablePanel] = useState(false);
  const [currentStyle, setCurrentStyle] = useState<FontStyle>({
    fontFamily: 'Microsoft YaHei',
    fontSize: '14px',
    fontWeight: 'normal',
    fontStyle: 'normal',
    color: '#000000',
    backgroundColor: 'transparent',
    textDecoration: 'none',
    textAlign: 'left'
  });

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const richEditorRef = useRef<HTMLDivElement>(null);

  // 字体选项
  const fontFamilies = [
    { name: '微软雅黑', value: 'Microsoft YaHei' },
    { name: '宋体', value: 'SimSun' },
    { name: '黑体', value: 'SimHei' },
    { name: '楷体', value: 'KaiTi' },
    { name: 'Arial', value: 'Arial' },
    { name: 'Times New Roman', value: 'Times New Roman' },
    { name: 'Helvetica', value: 'Helvetica' },
    { name: 'Georgia', value: 'Georgia' },
    { name: 'Verdana', value: 'Verdana' },
    { name: 'Courier New', value: 'Courier New' }
  ];

  const fontSizes = [
    { name: '初号', value: '42px' },
    { name: '小初', value: '36px' },
    { name: '一号', value: '26px' },
    { name: '小一', value: '24px' },
    { name: '二号', value: '22px' },
    { name: '小二', value: '18px' },
    { name: '三号', value: '16px' },
    { name: '小三', value: '15px' },
    { name: '四号', value: '14px' },
    { name: '小四', value: '12px' },
    { name: '五号', value: '10.5px' },
    { name: '小五', value: '9px' },
    { name: '六号', value: '7.5px' },
    { name: '小六', value: '6.5px' }
  ];

  // 颜色预设
  const colorPresets = [
    '#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF',
    '#FF0000', '#FF6600', '#FFCC00', '#FFFF00', '#99FF00', '#00FF00',
    '#00FFCC', '#00CCFF', '#0066FF', '#0000FF', '#6600FF', '#CC00FF',
    '#FF0099', '#FF3366', '#FF6699', '#FF99CC', '#FFCCFF', '#CCCCFF'
  ];

  // 执行富文本命令
  const execCommand = (command: string, value?: string) => {
    if (richEditorRef.current) {
      richEditorRef.current.focus();
      document.execCommand(command, false, value);
      updateContent();
    }
  };

  // 更新内容
  const updateContent = () => {
    if (richEditorRef.current) {
      const html = richEditorRef.current.innerHTML;
      const markdown = htmlToMarkdown(html);
      onChange(markdown);
    }
  };

  // HTML转Markdown（增强版）
  const htmlToMarkdown = (html: string): string => {
    return html
      // 标题
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
      .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
      .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
      .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')
      // 粗体和斜体
      .replace(/<(strong|b)[^>]*>(.*?)<\/(strong|b)>/gi, '**$2**')
      .replace(/<(em|i)[^>]*>(.*?)<\/(em|i)>/gi, '*$2*')
      .replace(/<u[^>]*>(.*?)<\/u>/gi, '<u>$1</u>')
      .replace(/<s[^>]*>(.*?)<\/s>/gi, '~~$1~~')
      // 上标下标
      .replace(/<sup[^>]*>(.*?)<\/sup>/gi, '^$1^')
      .replace(/<sub[^>]*>(.*?)<\/sub>/gi, '~$1~')
      // 代码
      .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
      .replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gi, '```\n$1\n```')
      // 链接
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
      // 图片
      .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)')
      .replace(/<img[^>]*src="([^"]*)"[^>]*>/gi, '![]($1)')
      // 列表
      .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
      .replace(/<ol[^>]*>(.*?)<\/ol>/gi, '$1\n')
      .replace(/<ul[^>]*>(.*?)<\/ul>/gi, '$1\n')
      // 引用
      .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1\n')
      // 分隔线
      .replace(/<hr[^>]*>/gi, '\n---\n')
      // 段落
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
      .replace(/<div[^>]*>(.*?)<\/div>/gi, '$1\n')
      // 换行
      .replace(/<br[^>]*>/gi, '\n')
      // 清理HTML标签
      .replace(/<[^>]*>/g, '')
      // 清理多余空行
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  };

  // Markdown转HTML（增强版）
  const markdownToHtml = (markdown: string): string => {
    return markdown
      // 代码块
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      // 标题
      .replace(/^###### (.*$)/gim, '<h6>$1</h6>')
      .replace(/^##### (.*$)/gim, '<h5>$1</h5>')
      .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 粗体和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/~~(.*?)~~/g, '<s>$1</s>')
      .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
      // 上标下标
      .replace(/\^(.*?)\^/g, '<sup>$1</sup>')
      .replace(/~(.*?)~/g, '<sub>$1</sub>')
      // 行内代码
      .replace(/`(.*?)`/g, '<code>$1</code>')
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
      // 图片
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1">')
      // 列表
      .replace(/^\* (.*$)/gim, '<li>$1</li>')
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
      // 引用
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
      // 分隔线
      .replace(/^---$/gim, '<hr>')
      // 换行
      .replace(/\n/g, '<br>');
  };

  // 插入表格
  const insertTable = (rows: number, cols: number) => {
    let tableHtml = '<table border="1" style="border-collapse: collapse; width: 100%;">';
    
    // 表头
    tableHtml += '<thead><tr>';
    for (let j = 0; j < cols; j++) {
      tableHtml += '<th style="padding: 8px; background-color: #f5f5f5;">表头' + (j + 1) + '</th>';
    }
    tableHtml += '</tr></thead>';
    
    // 表体
    tableHtml += '<tbody>';
    for (let i = 0; i < rows - 1; i++) {
      tableHtml += '<tr>';
      for (let j = 0; j < cols; j++) {
        tableHtml += '<td style="padding: 8px;">内容' + (i + 1) + '-' + (j + 1) + '</td>';
      }
      tableHtml += '</tr>';
    }
    tableHtml += '</tbody></table><br>';

    if (richEditorRef.current) {
      richEditorRef.current.focus();
      document.execCommand('insertHTML', false, tableHtml);
      updateContent();
    }
    setShowTablePanel(false);
  };

  // 插入链接
  const insertLink = () => {
    const url = prompt('请输入链接地址:');
    const text = prompt('请输入链接文字:') || url;
    if (url) {
      if (isRichMode) {
        execCommand('createLink', url);
      } else {
        insertMarkdownText(`[${text}](${url})`);
      }
    }
  };

  // 插入图片
  const insertImage = () => {
    const url = prompt('请输入图片地址:');
    const alt = prompt('请输入图片描述:') || '图片';
    if (url) {
      if (isRichMode) {
        const imgHtml = `<img src="${url}" alt="${alt}" style="max-width: 100%; height: auto;">`;
        execCommand('insertHTML', imgHtml);
      } else {
        insertMarkdownText(`![${alt}](${url})`);
      }
    }
  };

  // 插入Markdown文本
  const insertMarkdownText = (text: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + text + content.substring(end);
      onChange(newContent);
      
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + text.length, start + text.length);
      }, 0);
    }
  };

  // 工具栏按钮组件
  const ToolbarButton: React.FC<{
    onClick: () => void;
    title: string;
    children: React.ReactNode;
    active?: boolean;
    disabled?: boolean;
  }> = ({ onClick, title, children, active = false, disabled = false }) => (
    <button
      onClick={onClick}
      title={title}
      disabled={disabled}
      className={`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm ${
        active ? 'bg-blue-100 dark:bg-blue-900' : ''
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );

  return (
    <div className={`border border-gray-300 dark:border-gray-600 rounded-md ${className}`}>
      {/* 主工具栏 */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* 模式切换 */}
        <div className="flex items-center gap-1 mr-4">
          <button
            onClick={() => setIsRichMode(false)}
            className={`px-3 py-1 text-xs rounded ${
              !isRichMode 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
          >
            Markdown
          </button>
          <button
            onClick={() => setIsRichMode(true)}
            className={`px-3 py-1 text-xs rounded ${
              isRichMode 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
          >
            富文本
          </button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mr-2"></div>

        {/* 字体设置 */}
        <div className="relative">
          <ToolbarButton
            onClick={() => setShowFontPanel(!showFontPanel)}
            title="字体设置"
          >
            <span className="text-sm">字体</span>
          </ToolbarButton>
          
          {showFontPanel && (
            <div className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3 min-w-[300px]">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium mb-1">字体</label>
                  <select 
                    value={currentStyle.fontFamily}
                    onChange={(e) => {
                      setCurrentStyle({...currentStyle, fontFamily: e.target.value});
                      if (isRichMode) execCommand('fontName', e.target.value);
                    }}
                    className="w-full text-xs border rounded px-2 py-1"
                  >
                    {fontFamilies.map(font => (
                      <option key={font.value} value={font.value}>{font.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium mb-1">字号</label>
                  <select 
                    value={currentStyle.fontSize}
                    onChange={(e) => {
                      setCurrentStyle({...currentStyle, fontSize: e.target.value});
                      if (isRichMode) execCommand('fontSize', e.target.value);
                    }}
                    className="w-full text-xs border rounded px-2 py-1"
                  >
                    {fontSizes.map(size => (
                      <option key={size.value} value={size.value}>{size.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 文本格式 */}
        <ToolbarButton onClick={() => execCommand('bold')} title="粗体 (Ctrl+B)">
          <strong>B</strong>
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('italic')} title="斜体 (Ctrl+I)">
          <em>I</em>
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('underline')} title="下划线 (Ctrl+U)">
          <u>U</u>
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('strikeThrough')} title="删除线">
          <s>S</s>
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('superscript')} title="上标">
          X²
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('subscript')} title="下标">
          X₂
        </ToolbarButton>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 颜色设置 */}
        <div className="relative">
          <ToolbarButton
            onClick={() => setShowColorPanel(!showColorPanel)}
            title="文字颜色"
          >
            <span className="text-sm">🎨</span>
          </ToolbarButton>
          
          {showColorPanel && (
            <div className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3">
              <div className="mb-3">
                <label className="block text-xs font-medium mb-1">文字颜色</label>
                <div className="grid grid-cols-6 gap-1 mb-2">
                  {colorPresets.map(color => (
                    <button
                      key={color}
                      onClick={() => {
                        setCurrentStyle({...currentStyle, color});
                        if (isRichMode) execCommand('foreColor', color);
                      }}
                      className="w-6 h-6 rounded border border-gray-300"
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
                <input
                  type="color"
                  value={currentStyle.color}
                  onChange={(e) => {
                    setCurrentStyle({...currentStyle, color: e.target.value});
                    if (isRichMode) execCommand('foreColor', e.target.value);
                  }}
                  className="w-full h-8 rounded border"
                />
              </div>
              <div>
                <label className="block text-xs font-medium mb-1">背景颜色</label>
                <div className="grid grid-cols-6 gap-1 mb-2">
                  {colorPresets.map(color => (
                    <button
                      key={color}
                      onClick={() => {
                        setCurrentStyle({...currentStyle, backgroundColor: color});
                        if (isRichMode) execCommand('backColor', color);
                      }}
                      className="w-6 h-6 rounded border border-gray-300"
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
                <input
                  type="color"
                  value={currentStyle.backgroundColor === 'transparent' ? '#ffffff' : currentStyle.backgroundColor}
                  onChange={(e) => {
                    setCurrentStyle({...currentStyle, backgroundColor: e.target.value});
                    if (isRichMode) execCommand('backColor', e.target.value);
                  }}
                  className="w-full h-8 rounded border"
                />
              </div>
            </div>
          )}
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 对齐方式 */}
        <ToolbarButton onClick={() => execCommand('justifyLeft')} title="左对齐">
          ⬅️
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('justifyCenter')} title="居中对齐">
          ↔️
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('justifyRight')} title="右对齐">
          ➡️
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('justifyFull')} title="两端对齐">
          ↕️
        </ToolbarButton>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 列表 */}
        <ToolbarButton onClick={() => execCommand('insertUnorderedList')} title="无序列表">
          •
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('insertOrderedList')} title="有序列表">
          1.
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('indent')} title="增加缩进">
          →
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('outdent')} title="减少缩进">
          ←
        </ToolbarButton>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 插入元素 */}
        <ToolbarButton onClick={insertLink} title="插入链接">
          🔗
        </ToolbarButton>
        <ToolbarButton onClick={insertImage} title="插入图片">
          🖼️
        </ToolbarButton>
        
        <div className="relative">
          <ToolbarButton
            onClick={() => setShowTablePanel(!showTablePanel)}
            title="插入表格"
          >
            ⊞
          </ToolbarButton>
          
          {showTablePanel && (
            <div className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3">
              <div className="text-xs font-medium mb-2">选择表格大小</div>
              <div className="grid grid-cols-5 gap-1">
                {Array.from({length: 25}, (_, i) => {
                  const row = Math.floor(i / 5) + 1;
                  const col = (i % 5) + 1;
                  return (
                    <button
                      key={i}
                      onClick={() => insertTable(row, col)}
                      className="w-6 h-6 border border-gray-300 hover:bg-blue-100 text-xs"
                      title={`${row}×${col}`}
                    >
                      {row}×{col}
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <ToolbarButton onClick={() => execCommand('insertHorizontalRule')} title="分隔线">
          —
        </ToolbarButton>

        {/* 特殊格式 */}
        <ToolbarButton
          onClick={() => {
            if (isRichMode) {
              execCommand('formatBlock', 'blockquote');
            } else {
              insertMarkdownText('> 引用内容');
            }
          }}
          title="引用"
        >
          ❝
        </ToolbarButton>

        <ToolbarButton
          onClick={() => {
            if (isRichMode) {
              execCommand('formatBlock', 'pre');
            } else {
              insertMarkdownText('```\n代码块\n```');
            }
          }}
          title="代码块"
        >
          { }
        </ToolbarButton>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 标题 */}
        <select
          onChange={(e) => {
            if (e.target.value && isRichMode) {
              execCommand('formatBlock', e.target.value);
            }
          }}
          className="text-xs border rounded px-2 py-1 bg-white dark:bg-gray-700"
          defaultValue=""
        >
          <option value="">标题</option>
          <option value="h1">一级标题</option>
          <option value="h2">二级标题</option>
          <option value="h3">三级标题</option>
          <option value="h4">四级标题</option>
          <option value="h5">五级标题</option>
          <option value="h6">六级标题</option>
          <option value="p">正文</option>
        </select>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>

        {/* 编辑操作 */}
        <ToolbarButton onClick={() => execCommand('undo')} title="撤销 (Ctrl+Z)">
          ↶
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('redo')} title="重做 (Ctrl+Y)">
          ↷
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('removeFormat')} title="清除格式">
          🧹
        </ToolbarButton>
        <ToolbarButton onClick={() => execCommand('selectAll')} title="全选 (Ctrl+A)">
          📄
        </ToolbarButton>

        {/* 查找替换 */}
        <ToolbarButton
          onClick={() => {
            const searchText = prompt('请输入要查找的文字:');
            if (searchText) {
              execCommand('findString', searchText);
            }
          }}
          title="查找"
        >
          🔍
        </ToolbarButton>
      </div>

      {/* 编辑器内容 */}
      {isRichMode ? (
        // 富文本模式
        <div
          ref={richEditorRef}
          contentEditable
          className="p-4 min-h-[400px] focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 overflow-y-auto"
          dangerouslySetInnerHTML={{ __html: markdownToHtml(content) }}
          onInput={updateContent}
          onKeyDown={(e) => {
            // 快捷键支持
            if (e.ctrlKey || e.metaKey) {
              switch (e.key) {
                case 'b':
                  e.preventDefault();
                  execCommand('bold');
                  break;
                case 'i':
                  e.preventDefault();
                  execCommand('italic');
                  break;
                case 'u':
                  e.preventDefault();
                  execCommand('underline');
                  break;
                case 'z':
                  e.preventDefault();
                  execCommand('undo');
                  break;
                case 'y':
                  e.preventDefault();
                  execCommand('redo');
                  break;
              }
            }
          }}
          style={{
            lineHeight: '1.6',
            fontFamily: currentStyle.fontFamily,
            fontSize: currentStyle.fontSize
          }}
        />
      ) : (
        // Markdown模式
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onChange(e.target.value)}
          className="w-full p-4 min-h-[400px] resize-none focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono text-sm"
          placeholder="输入Markdown内容..."
          style={{ lineHeight: '1.6' }}
        />
      )}

      {/* 状态栏 */}
      <div className="flex items-center justify-between px-4 py-2 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-300 dark:border-gray-600">
        <span>
          {isRichMode ? '🎨 富文本模式' : '📝 Markdown模式'} | 字符数: {content.length} | 行数: {content.split('\n').length}
        </span>
        <span>
          快捷键: Ctrl+B(粗体) Ctrl+I(斜体) Ctrl+U(下划线) Ctrl+Z(撤销) Ctrl+Y(重做)
        </span>
      </div>
    </div>
  );
};
