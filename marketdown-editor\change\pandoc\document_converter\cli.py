"""
命令行界面
"""

import os
import sys
import click
import logging
from pathlib import Path
from typing import List, Optional

from .core import DocumentConverter
from .utils.file_utils import detect_file_format, get_file_info
from .utils.conversion_utils import get_supported_formats, get_conversion_matrix
from .exceptions import ConversionError, UnsupportedFormatError, FileNotFoundError


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='启用详细输出')
@click.option('--quiet', '-q', is_flag=True, help='静默模式')
@click.pass_context
def cli(ctx, verbose, quiet):
    """文档格式转换工具
    
    支持PDF、Word(.docx)、Excel(.xlsx)、TXT、Markdown(.md)之间的互相转换
    """
    ctx.ensure_object(dict)
    
    # 设置日志级别
    if quiet:
        log_level = 'ERROR'
    elif verbose:
        log_level = 'DEBUG'
    else:
        log_level = 'INFO'
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    ctx.obj['log_level'] = log_level


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.argument('output_file', type=click.Path())
@click.option('--format', '-f', 'target_format', help='目标格式 (pdf, docx, xlsx, txt, md)')
@click.option('--template', help='模板文件路径')
@click.option('--quality', default='normal', type=click.Choice(['low', 'normal', 'high']), help='转换质量')
@click.option('--toc', is_flag=True, help='生成目录（适用于PDF/Word）')
@click.option('--max-rows', type=int, default=1000, help='Excel转换时的最大行数')
@click.pass_context
def convert(ctx, input_file, output_file, target_format, template, quality, toc, max_rows):
    """转换单个文件
    
    示例:
        docconvert convert input.pdf output.docx
        docconvert convert input.md output.pdf --toc
        docconvert convert input.xlsx output.txt --max-rows 500
    """
    try:
        # 创建转换器
        converter = DocumentConverter(log_level=ctx.obj['log_level'])
        
        # 检测输入格式
        source_format = detect_file_format(input_file)
        click.echo(f"检测到输入格式: {source_format}")
        
        # 确定目标格式
        if target_format:
            target_format = target_format.lower()
        else:
            _, ext = os.path.splitext(output_file.lower())
            target_format = ext.lstrip('.')
        
        click.echo(f"目标格式: {target_format}")
        
        # 验证转换是否支持
        if not converter.validate_conversion(source_format, target_format):
            raise UnsupportedFormatError(f"不支持从 {source_format} 转换到 {target_format}")
        
        # 准备转换参数
        kwargs = {
            'target_format': target_format,
            'quality': quality,
            'toc': toc,
            'max_rows': max_rows
        }
        
        if template:
            kwargs['template'] = template
        
        # 执行转换
        click.echo(f"开始转换: {input_file} -> {output_file}")
        
        success = converter.convert(input_file, output_file, **kwargs)
        
        if success:
            click.echo(click.style("转换成功!", fg='green'))
            
            # 显示输出文件信息
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                click.echo(f"输出文件: {output_file} ({file_size} 字节)")
        else:
            click.echo(click.style("转换失败!", fg='red'))
            sys.exit(1)
            
    except Exception as e:
        click.echo(click.style(f"错误: {str(e)}", fg='red'))
        sys.exit(1)


@cli.command()
@click.argument('input_pattern')
@click.option('--output-dir', '-o', required=True, type=click.Path(), help='输出目录')
@click.option('--format', '-f', 'target_format', required=True, help='目标格式')
@click.option('--recursive', '-r', is_flag=True, help='递归处理子目录')
@click.option('--template', help='模板文件路径')
@click.option('--quality', default='normal', type=click.Choice(['low', 'normal', 'high']), help='转换质量')
@click.option('--max-workers', type=int, default=4, help='并行处理的最大工作线程数')
@click.pass_context
def batch(ctx, input_pattern, output_dir, target_format, recursive, template, quality, max_workers):
    """批量转换文件
    
    示例:
        docconvert batch "*.pdf" -o ./converted -f docx
        docconvert batch "/path/to/docs" -o ./output -f md -r
    """
    try:
        import glob
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        # 创建转换器
        converter = DocumentConverter(log_level=ctx.obj['log_level'])
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 收集输入文件
        input_files = []
        
        if os.path.isdir(input_pattern):
            # 目录模式
            if recursive:
                for root, dirs, files in os.walk(input_pattern):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            detect_file_format(file_path)
                            input_files.append(file_path)
                        except:
                            continue
            else:
                for file in os.listdir(input_pattern):
                    file_path = os.path.join(input_pattern, file)
                    if os.path.isfile(file_path):
                        try:
                            detect_file_format(file_path)
                            input_files.append(file_path)
                        except:
                            continue
        else:
            # 通配符模式
            input_files = glob.glob(input_pattern)
            input_files = [f for f in input_files if os.path.isfile(f)]
        
        if not input_files:
            click.echo("未找到匹配的文件")
            return
        
        click.echo(f"找到 {len(input_files)} 个文件待转换")
        
        # 准备转换参数
        kwargs = {
            'target_format': target_format,
            'quality': quality
        }
        
        if template:
            kwargs['template'] = template
        
        # 并行转换
        def convert_file(input_file):
            try:
                filename = os.path.basename(input_file)
                name_without_ext = os.path.splitext(filename)[0]
                output_file = os.path.join(output_dir, f"{name_without_ext}.{target_format}")
                
                success = converter.convert(input_file, output_file, **kwargs)
                return input_file, success, None
            except Exception as e:
                return input_file, False, str(e)
        
        successful = 0
        failed = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_file = {executor.submit(convert_file, f): f for f in input_files}
            
            # 处理结果
            with click.progressbar(as_completed(future_to_file), length=len(input_files)) as bar:
                for future in bar:
                    input_file, success, error = future.result()
                    
                    if success:
                        successful += 1
                        click.echo(f"✓ {os.path.basename(input_file)}")
                    else:
                        failed += 1
                        click.echo(f"✗ {os.path.basename(input_file)}: {error or '转换失败'}")
        
        click.echo(f"\n转换完成: {successful} 成功, {failed} 失败")
        
    except Exception as e:
        click.echo(click.style(f"错误: {str(e)}", fg='red'))
        sys.exit(1)


@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
def info(file_path):
    """显示文件信息"""
    try:
        file_info = get_file_info(file_path)
        
        click.echo(f"文件路径: {file_info['path']}")
        click.echo(f"文件名: {file_info['name']}")
        click.echo(f"格式: {file_info['format']}")
        click.echo(f"大小: {file_info['size']} 字节")
        
        if 'encoding' in file_info:
            click.echo(f"编码: {file_info['encoding']} (置信度: {file_info['confidence']:.2f})")
        
        # 显示转换选项
        converter = DocumentConverter()
        supported_formats = converter.get_supported_formats()
        available_formats = [fmt for fmt in supported_formats if fmt != file_info['format']]
        
        click.echo(f"可转换为: {', '.join(available_formats)}")
        
    except Exception as e:
        click.echo(click.style(f"错误: {str(e)}", fg='red'))


@cli.command()
def formats():
    """显示支持的格式和转换矩阵"""
    supported_formats = get_supported_formats()
    conversion_matrix = get_conversion_matrix()
    
    click.echo("支持的格式:")
    for fmt in supported_formats:
        click.echo(f"  - {fmt}")
    
    click.echo("\n转换矩阵:")
    for source_format, target_formats in conversion_matrix.items():
        click.echo(f"  {source_format} -> {', '.join(target_formats)}")


@cli.command()
def version():
    """显示版本信息"""
    from . import __version__, __author__
    click.echo(f"Document Converter v{__version__}")
    click.echo(f"作者: {__author__}")


def main():
    """主入口点"""
    cli()


if __name__ == '__main__':
    main()
